"""
Pytest configuration and fixtures for the Auto Latent Video Processor test suite.
"""

import os
import sys
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, MagicMock

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@pytest.fixture
def temp_workspace(tmp_path):
    """Create a temporary workspace with typical project structure."""
    workspace = tmp_path / "test_workspace"
    workspace.mkdir()
    
    # Create directory structure
    (workspace / "input").mkdir()
    (workspace / "input" / "speaker1_video").mkdir()
    (workspace / "input" / "speaker1_audio").mkdir()
    (workspace / "input" / "speaker2_video").mkdir()
    (workspace / "input" / "speaker2_audio").mkdir()
    (workspace / "output").mkdir()
    (workspace / "temp").mkdir()
    (workspace / "tts-output").mkdir()
    (workspace / "workflows").mkdir()
    (workspace / "voices").mkdir()
    
    return workspace


@pytest.fixture
def sample_workflow():
    """Create a sample workflow for testing."""
    return {
        "1": {
            "class_type": "VHS_LoadVideo",
            "inputs": {
                "video": "",
                "force_rate": 0,
                "custom_width": 0,
                "custom_height": 0,
                "frame_load_cap": 0,
                "skip_first_frames": 0,
                "select_every_nth": 1,
                "format": "AnimateDiff"
            }
        },
        "2": {
            "class_type": "VideoLengthAdjuster",
            "inputs": {
                "mode": "loop_to_audio",
                "fps": 25,
                "silent_padding_sec": 0.3
            }
        },
        "3": {
            "class_type": "LatentSync",
            "inputs": {
                "seed": 685,
                "lips_expression": 2.1,
                "inference_steps": 20
            }
        },
        "4": {
            "class_type": "AudioLoader",
            "inputs": {
                "audio": ""
            }
        },
        "5": {
            "class_type": "VideoCombine",
            "inputs": {
                "crf": 19,
                "fps": 25
            }
        }
    }


@pytest.fixture
def sample_framepack_workflow():
    """Create a sample FramePack workflow for testing."""
    return {
        "72": {
            "class_type": "LoadImage",
            "inputs": {
                "image": ""
            }
        },
        "62": {
            "class_type": "StringConstantMultiline",
            "inputs": {
                "string": "default prompt"
            }
        },
        "67": {
            "class_type": "CR Seed",
            "inputs": {
                "seed": 0
            }
        }
    }


@pytest.fixture
def sample_config():
    """Create a sample configuration for testing."""
    return {
        'comfyui_url': '127.0.0.1:8188',
        'workflow_path': 'workflows/latentsync_workflow_api.json',
        'framepack_workflow_path': 'workflows/framepack_comfyui1.json',
        'comfyui_output_base_dir': '/tmp/comfyui_output',
        'input_dir': 'input',
        'output_dir': 'output',
        'zonos_api_key': 'test_api_key',
        'tts_output_dir': 'tts-output',
        'use_local_tts': False,
        'seed': 12345,
        'lips_expression': 2.0,
        'inference_steps': 25,
        'crf': 20,
        'silent_padding_sec': 0.4,
        'framepack_prompt': 'test prompt',
        'framepack_seed': 67890,
        'framepack_random': False
    }


@pytest.fixture
def sample_media_files(temp_workspace):
    """Create sample media files for testing."""
    media_files = {}
    
    # Create sample video files
    video_files = [
        temp_workspace / "input" / "speaker1_video" / "video1.mp4",
        temp_workspace / "input" / "speaker2_video" / "video2.mp4"
    ]
    for video_file in video_files:
        video_file.write_bytes(b"fake_video_data")
    media_files['videos'] = video_files
    
    # Create sample audio files
    audio_files = [
        temp_workspace / "input" / "speaker1_audio" / "audio1.wav",
        temp_workspace / "input" / "speaker2_audio" / "audio2.wav"
    ]
    for audio_file in audio_files:
        audio_file.write_bytes(b"fake_audio_data")
    media_files['audios'] = audio_files
    
    # Create sample image files
    image_files = [
        temp_workspace / "input" / "speaker1_video" / "image1.jpg",
        temp_workspace / "input" / "speaker2_video" / "image2.png"
    ]
    for image_file in image_files:
        image_file.write_bytes(b"fake_image_data")
    media_files['images'] = image_files
    
    return media_files


@pytest.fixture
def mock_requests():
    """Mock requests module for testing network operations."""
    with pytest.MonkeyPatch().context() as m:
        mock_requests = Mock()
        
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {'prompt_id': 'test-prompt-id'}
        mock_response.raise_for_status.return_value = None
        mock_response.status_code = 200
        
        mock_requests.post.return_value = mock_response
        mock_requests.get.return_value = mock_response
        
        m.setattr('backend_processor.requests', mock_requests)
        yield mock_requests


@pytest.fixture
def mock_opencv():
    """Mock OpenCV for testing without requiring actual OpenCV installation."""
    mock_cv2 = Mock()
    
    # Mock VideoCapture
    mock_cap = Mock()
    mock_cap.isOpened.return_value = True
    mock_cap.get.return_value = 30.0  # Mock FPS
    mock_cap.read.return_value = (True, Mock())  # Mock frame read
    mock_cap.release.return_value = None
    
    mock_cv2.VideoCapture.return_value = mock_cap
    mock_cv2.CAP_PROP_FPS = 5
    mock_cv2.CAP_PROP_FRAME_COUNT = 7
    mock_cv2.CAP_PROP_POS_FRAMES = 1
    mock_cv2.COLOR_BGR2RGB = 4
    mock_cv2.INTER_AREA = 3
    mock_cv2.cvtColor.return_value = Mock()
    mock_cv2.resize.return_value = Mock()
    
    return mock_cv2


@pytest.fixture
def mock_pyside6():
    """Mock PySide6 components for testing GUI logic without Qt."""
    mock_qt = Mock()
    
    # Mock Qt classes
    mock_qt.QSize = Mock
    mock_qt.QImage = Mock
    mock_qt.QPixmap = Mock
    mock_qt.QIcon = Mock
    
    # Mock Qt enums
    mock_qt.Qt = Mock()
    mock_qt.Qt.KeepAspectRatio = 1
    mock_qt.Qt.SmoothTransformation = 1
    
    return mock_qt


@pytest.fixture(autouse=True)
def setup_logging():
    """Setup logging for tests."""
    import logging
    
    # Configure logging for tests
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Suppress some noisy loggers during tests
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


@pytest.fixture
def mock_file_system(temp_workspace):
    """Mock file system operations for consistent testing."""
    class MockFileSystem:
        def __init__(self, workspace):
            self.workspace = workspace
        
        def create_file(self, relative_path, content=b"test_content"):
            """Create a file in the workspace."""
            file_path = self.workspace / relative_path
            file_path.parent.mkdir(parents=True, exist_ok=True)
            file_path.write_bytes(content)
            return file_path
        
        def create_workflow_file(self, name, workflow_data):
            """Create a workflow JSON file."""
            import json
            workflow_path = self.workspace / "workflows" / name
            workflow_path.write_text(json.dumps(workflow_data, indent=2))
            return workflow_path
        
        def list_files(self, relative_path, extensions=None):
            """List files in a directory with optional extension filtering."""
            dir_path = self.workspace / relative_path
            if not dir_path.exists():
                return []
            
            files = []
            for file_path in dir_path.iterdir():
                if file_path.is_file():
                    if extensions is None or file_path.suffix.lower() in extensions:
                        files.append(file_path)
            
            return sorted(files)
    
    return MockFileSystem(temp_workspace)


# Test markers for categorizing tests
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "gui: mark test as a GUI test"
    )
    config.addinivalue_line(
        "markers", "network: mark test as requiring network access"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Skip tests that require specific dependencies
def pytest_collection_modifyitems(config, items):
    """Modify test collection to handle missing dependencies."""
    skip_opencv = pytest.mark.skip(reason="OpenCV not available")
    skip_pyside6 = pytest.mark.skip(reason="PySide6 not available")
    skip_network = pytest.mark.skip(reason="Network tests disabled")
    
    for item in items:
        # Skip OpenCV tests if not available
        if "opencv" in item.nodeid.lower():
            try:
                import cv2
            except ImportError:
                item.add_marker(skip_opencv)
        
        # Skip PySide6 tests if not available
        if "gui" in item.keywords:
            try:
                import PySide6
            except ImportError:
                item.add_marker(skip_pyside6)
        
        # Skip network tests by default
        if "network" in item.keywords:
            if not config.getoption("--run-network-tests", default=False):
                item.add_marker(skip_network)


def pytest_addoption(parser):
    """Add custom command line options."""
    parser.addoption(
        "--run-network-tests",
        action="store_true",
        default=False,
        help="Run tests that require network access"
    )
    parser.addoption(
        "--run-slow-tests",
        action="store_true",
        default=False,
        help="Run slow tests"
    )
