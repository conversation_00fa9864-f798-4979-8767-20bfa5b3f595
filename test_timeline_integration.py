"""
Test script for Timeline Editor integration.
"""

import sys
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
    from PySide6.QtCore import Qt
    
    from timeline_editor import TimelineEditor, ClipType, TrackType
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Timeline Editor Test")
            self.setGeometry(100, 100, 1200, 800)
            
            # Central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # Timeline editor
            self.timeline_editor = TimelineEditor()
            layout.addWidget(self.timeline_editor)
            
            # Test buttons
            button_layout = QVBoxLayout()
            
            add_video_btn = QPushButton("Add Test Video Clip")
            add_video_btn.clicked.connect(self.add_test_video)
            button_layout.addWidget(add_video_btn)
            
            add_audio_btn = QPushButton("Add Test Audio Clip")
            add_audio_btn.clicked.connect(self.add_test_audio)
            button_layout.addWidget(add_audio_btn)
            
            layout.addLayout(button_layout)
            
            # Connect signals
            self.timeline_editor.clip_selected.connect(self.on_clip_selected)
            self.timeline_editor.time_changed.connect(self.on_time_changed)
            
            print("✅ Timeline Editor Test Window initialized")
        
        def add_test_video(self):
            """Add a test video clip."""
            success = self.timeline_editor.add_media_clip(
                "test_video.mp4", 
                ClipType.VIDEO_CLIP, 
                track_index=0
            )
            if success:
                print("✅ Test video clip added")
            else:
                print("❌ Failed to add test video clip")
        
        def add_test_audio(self):
            """Add a test audio clip."""
            success = self.timeline_editor.add_media_clip(
                "test_audio.wav", 
                ClipType.AUDIO_CLIP, 
                track_index=2
            )
            if success:
                print("✅ Test audio clip added")
            else:
                print("❌ Failed to add test audio clip")
        
        def on_clip_selected(self, clip):
            """Handle clip selection."""
            print(f"📋 Clip selected: {clip.name} ({clip.clip_type.value})")
        
        def on_time_changed(self, time):
            """Handle time change."""
            print(f"⏰ Time changed: {time:.2f}s")
    
    def main():
        app = QApplication(sys.argv)
        
        window = TestWindow()
        window.show()
        
        print("🚀 Timeline Editor Test started")
        print("📋 Use the buttons to add test clips")
        print("🎬 Try dragging clips around on the timeline")
        print("▶️ Use playback controls to test timeline")
        
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure PySide6 is installed and timeline_editor.py is available")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
