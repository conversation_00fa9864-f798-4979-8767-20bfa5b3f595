import sys
import os
import json
import re
import time
import logging
import requests
import subprocess
import re
from pathlib import Path
from datetime import datetime
import tempfile  # Added to enable tempfile.mkstemp in trimming logic
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QLabel, QWidget, QVBoxLayout, QHBoxLayout,
    QLineEdit, QPushButton, QFileDialog, QGroupBox, QSplitter, QListWidget,
    QMessageBox, QAbstractItemView, QListWidgetItem, QTabWidget, QDockWidget,
    QTextEdit, QStatusBar, QSpinBox, QDoubleSpinBox, QFormLayout, QGridLayout,
    QDialog, QDialogButtonBox, QMenu, QCheckBox, QComboBox, QSlider,
    QProgressBar, QTreeWidget, QTreeWidgetItem, QScrollArea, QFrame,
    QMenuBar, QToolBar, QDial, QLCDNumber
)
from PySide6.QtCore import Qt, QUrl, QSize, QThreadPool, QRunnable, QObject, Signal, Slot
from PySide6.QtGui import QIcon, QPixmap, QImage, QAction, QShortcut
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from requests.exceptions import HTTPError, ReadTimeout

# OpenCV integration - now working with current NumPy version
try:
    import cv2
    OPENCV_AVAILABLE = True
    logging.info(f"OpenCV {cv2.__version__} loaded successfully")
except ImportError as e:
    cv2 = None
    OPENCV_AVAILABLE = False
    logging.warning(f"OpenCV not available: {e}. Video thumbnails will use FFmpeg only.")

from backend_processor import (
    load_workflow,
    update_workflow,
    update_framepack_workflow,
    submit_workflow,
    poll_comfyui_status,
    run_ffmpeg_concat,
    run_tts_pipeline,
    auto_pair_audio_images,
    generate_script,
    voice_manager,
)
from config_manager import get_config_manager, get_config
from pydub import AudioSegment
import itertools, glob
from itertools import cycle

# Import timeline editor
try:
    from timeline_editor import TimelineEditor, TrackType, ClipType
    TIMELINE_EDITOR_AVAILABLE = True
    logging.info("Timeline Editor imported successfully")
except ImportError as e:
    TIMELINE_EDITOR_AVAILABLE = False
    logging.warning(f"Timeline Editor not available: {e}")

# Import project management
try:
    from project_management import ProjectManager, ProjectWidget
    PROJECT_MANAGEMENT_AVAILABLE = True
    logging.info("Project Management imported successfully")
except ImportError as e:
    PROJECT_MANAGEMENT_AVAILABLE = False
    logging.warning(f"Project Management not available: {e}")

# Import media library
try:
    from media_library import MediaLibraryWidget, MediaDatabase
    MEDIA_LIBRARY_AVAILABLE = True
    logging.info("Media Library imported successfully")
except ImportError as e:
    MEDIA_LIBRARY_AVAILABLE = False
    logging.warning(f"Media Library not available: {e}")

# Get configuration
config = get_config()
config_manager = get_config_manager()

# File extensions from configuration
VIDEO_EXTENSIONS = config.media.video_extensions
IMAGE_EXTENSIONS = config.media.image_extensions
AUDIO_EXTENSIONS = config.media.audio_extensions

# Separate QObject for emitting signals
class LogSignalEmitter(QObject):
    log_signal = Signal(str)

# Custom logging handler that uses a separate emitter
class QtLogHandler(logging.Handler):
    def __init__(self, emitter, parent=None):
        super().__init__()
        self.emitter = emitter # Store the emitter instance
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

    def emit(self, record):
        if not self.filter(record):
            return
        msg = self.format(record)
        try:
            self.emitter.log_signal.emit(msg) # Use the emitter
        except Exception as e:
            print(f"QtLogHandler Error emitting signal via emitter: {e}", file=sys.stderr)

# --- Trim Dialog ---
class TrimDialog(QDialog):
    """Dialog to specify trim start and end times for a video with preview."""
    def __init__(self, parent, video_path: str):
        super().__init__(parent)
        self.setWindowTitle("Trim Video Segment")
        # Media preview setup
        self.media_player = QMediaPlayer(self)
        self.audio_output = QAudioOutput(self)
        self.media_player.setAudioOutput(self.audio_output)
        self.video_widget = QVideoWidget(self)
        self.media_player.setVideoOutput(self.video_widget)
        import os
        abs_path = os.path.abspath(video_path)
        self.media_player.setSource(QUrl.fromLocalFile(abs_path))
        # Ensure widget has visible size
        self.video_widget.setMinimumSize(640, 360)
        # Load first frame by playing and immediately pausing
        self.media_player.play()
        self.media_player.pause()
        self.media_player.setPosition(0)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.video_widget)
        # Playback controls
        ctrl_layout = QHBoxLayout()
        play_btn = QPushButton("Play")
        play_btn.clicked.connect(lambda: (self.media_player.setPosition(int(self.start_input.value()*1000)), self.media_player.play()))
        ctrl_layout.addWidget(play_btn)
        pause_btn = QPushButton("Pause")
        pause_btn.clicked.connect(lambda: self.media_player.pause())
        ctrl_layout.addWidget(pause_btn)
        main_layout.addLayout(ctrl_layout)

        # Trim form inputs
        form_layout = QFormLayout()
        self.start_input = QDoubleSpinBox()
        self.start_input.setRange(0.0, 9999.0)
        self.start_input.setDecimals(3)
        self.start_input.setSingleStep(0.1)
        self.end_input = QDoubleSpinBox()
        self.end_input.setRange(0.0, 9999.0)
        self.end_input.setDecimals(3)
        self.end_input.setSingleStep(0.1)
        form_layout.addRow("Start (s):", self.start_input)
        form_layout.addRow("End (s):", self.end_input)
        main_layout.addLayout(form_layout)

        # Dialog buttons
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        main_layout.addWidget(buttons)

        # Populate end_input with the real video duration when available
        self.media_player.durationChanged.connect(lambda ms: self.end_input.setValue(ms / 1000.0))

        # Limit preview to specified trim range
        self.media_player.positionChanged.connect(lambda ms: self.media_player.pause() if ms >= int(self.end_input.value()*1000) else None)

        # Ensure a reasonable default size for preview
        self.resize(800, 600)

    def get_times(self):
        return self.start_input.value(), self.end_input.value()

# --- Audio Trim Dialog ---
class AudioTrimDialog(QDialog):
    def __init__(self, parent, audio_path: str):
        super().__init__(parent)
        self.setWindowTitle("Trim Audio Segment")
        from pydub import AudioSegment
        audio = AudioSegment.from_file(audio_path)
        duration_ms = len(audio)
        self.player = QMediaPlayer(self)
        out = QAudioOutput(self); self.player.setAudioOutput(out)
        self.player.setSource(QUrl.fromLocalFile(audio_path))
        layout = QVBoxLayout(self)
        ctrl = QHBoxLayout()
        self.play_btn = QPushButton("Play"); ctrl.addWidget(self.play_btn)
        self.pause_btn = QPushButton("Pause"); ctrl.addWidget(self.pause_btn)
        self.stop_btn = QPushButton("Stop"); ctrl.addWidget(self.stop_btn)
        self.slider = QSlider(Qt.Horizontal); self.slider.setRange(0, duration_ms); ctrl.addWidget(self.slider)
        layout.addLayout(ctrl)
        form = QFormLayout()
        self.start_input = QDoubleSpinBox(); self.start_input.setRange(0, duration_ms/1000); form.addRow("Start (s):", self.start_input)
        self.end_input   = QDoubleSpinBox(); self.end_input.setRange(0, duration_ms/1000); self.end_input.setValue(duration_ms/1000); form.addRow("End (s):", self.end_input)
        layout.addLayout(form)
        buttons = QDialogButtonBox(QDialogButtonBox.Ok|QDialogButtonBox.Cancel); layout.addWidget(buttons)
        buttons.accepted.connect(self.accept); buttons.rejected.connect(self.reject)
        self.play_btn.clicked.connect(lambda: self.player.setPosition(int(self.start_input.value()*1000)) or self.player.play())
        self.pause_btn.clicked.connect(self.player.pause)
        self.stop_btn.clicked.connect(self.player.stop)
        self.player.positionChanged.connect(self.slider.setValue)
        self.slider.sliderMoved.connect(self.player.setPosition)
    def get_times(self):
        return self.start_input.value(), self.end_input.value()

# --- Worker Signals ---
class WorkerSignals(QObject):
    finished = Signal(object) 
    error = Signal(str)
    progress = Signal(str) 
    pair_finished = Signal(int, bool, list) 
    all_finished = Signal(list, bool) 

class BatchProcessorWorker(QRunnable):
    def __init__(self, processing_queue, config):
        super().__init__()
        self.signals = WorkerSignals()
        self.processing_queue = list(processing_queue) 
        self.config = config # Store the whole config dict
        self.is_cancelled = False
        self.all_intermediate_files = []
        self.any_errors = False

    # No Slot decorator needed for QRunnable.run()
    def run(self):
        total_pairs = len(self.processing_queue)
        if total_pairs == 0:
            self.signals.all_finished.emit([], False)
            return

        base_workflow = load_workflow(self.config['workflow_path'])
        if not base_workflow:
            error_msg = f"Failed to load base workflow from {self.config['workflow_path']}"
            logging.error(error_msg)
            self.signals.error.emit(error_msg)
            self.signals.all_finished.emit([], True) 
            return

        # Load FramePack workflow for image-to-video conversion
        base_framepack = load_workflow(self.config.get('framepack_workflow_path', ''))
        if not base_framepack:
            error_msg = f"Failed to load FramePack workflow from {self.config.get('framepack_workflow_path')}"
            logging.error(error_msg)
            self.signals.error.emit(error_msg)
            self.signals.all_finished.emit([], True)
            return

        # Prepare ffmpeg startup info for Windows
        startupinfo = None
        if sys.platform == "win32":
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

        for i, entry in enumerate(self.processing_queue):
            # Unpack video, audio, optional trim range
            if isinstance(entry, (list, tuple)) and len(entry) >= 2:
                video_path, audio_path = entry[0], entry[1]
                trim_range = entry[2] if len(entry) >= 3 else None
            else:
                continue

            if self.is_cancelled:
                logging.info("Batch processing cancelled.")
                break 

            video_basename = os.path.basename(video_path)
            audio_basename = os.path.basename(audio_path)
            pair_index = i
            progress_msg = f"Processing pair {i+1}/{total_pairs}: {video_basename} / {audio_basename}"
            logging.info(progress_msg)
            self.signals.progress.emit(progress_msg)
            
            pair_intermediate_files = []
            success = False
            tmp_trimmed = None
            input_video = video_path

            try:
                # Trim video segment if requested
                if trim_range:
                    start, end = trim_range
                    duration = end - start
                    fd, tmp_trimmed = tempfile.mkstemp(suffix=os.path.splitext(video_path)[1])
                    os.close(fd)
                    cmd = ['ffmpeg', '-y', '-ss', str(start), '-i', video_path, '-t', str(duration), '-c', 'copy', tmp_trimmed]
                    logging.info(f"Trimming video {video_path}: {start:.3f}s to {end:.3f}s")
                    subprocess.run(cmd, capture_output=True, text=True, check=True, startupinfo=startupinfo)
                    input_video = tmp_trimmed

                # If input was an image, run FramePack first
                ext = os.path.splitext(video_path)[1].lower()
                if ext in IMAGE_EXTENSIONS:
                    fp_workflow = update_framepack_workflow(base_framepack, video_path, self.config)
                    if not fp_workflow:
                        raise RuntimeError("FramePack workflow update failed.")
                    fp_prompt_id = submit_workflow(self.config['comfyui_url'], fp_workflow)
                    if not fp_prompt_id:
                        raise RuntimeError("FramePack workflow submission failed.")
                    self.signals.progress.emit(f"Submitted FramePack for pair {i+1}... waiting for completion.")
                    time.sleep(0.5)
                    fp_success, fp_files = poll_comfyui_status(self.config['comfyui_url'], fp_prompt_id)
                    if not fp_success:
                        raise RuntimeError("FramePack processing failed or no output files.")
                    
                    # Locate FramePack MP4 outputs in ComfyUI directory
                    mp4_files = []
                    for root, dirs, files in os.walk(self.config['comfyui_output_base_dir']):
                        for f in files:
                            if f.lower().endswith('.mp4') and f.startswith('FramePack'):
                                mp4_files.append(os.path.join(root, f))
                    if not mp4_files:
                        raise RuntimeError(f"No FramePack MP4 found under {self.config['comfyui_output_base_dir']}")
                    # Pick latest by modification time
                    mp4_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
                    input_video = mp4_files[0]
                    # if PNG found, try using its MP4 counterpart
                    if input_video.lower().endswith('.png'):
                        base, _ = os.path.splitext(input_video)
                        candidate_mp4 = base + '.mp4'
                        if os.path.exists(candidate_mp4):
                            logging.info(f"Found FramePack video file {candidate_mp4}, using it instead of PNG")
                            input_video = candidate_mp4

                updated_workflow = update_workflow(base_workflow, input_video, audio_path, self.config)
                if not updated_workflow:
                    raise RuntimeError("Workflow update failed.")

                prompt_id = submit_workflow(self.config['comfyui_url'], updated_workflow)
                if not prompt_id:
                    raise RuntimeError("Workflow submission failed.")
                self.signals.progress.emit(f"Submitted pair {i+1} (ID: {prompt_id[:8]}...). Waiting for completion...")

                # --- Add a small delay before polling --- 
                time.sleep(0.5) # Wait 500ms for server to register job
                # -----------------------------------------

                poll_success, output_filenames = poll_comfyui_status(self.config['comfyui_url'], prompt_id)
                if not poll_success:
                    raise RuntimeError(f"Polling failed or timed out for prompt ID {prompt_id}")

                # Filter LatentSync outputs to only video files
                video_output_files = [f for f in output_filenames if os.path.splitext(f)[1].lower() in VIDEO_EXTENSIONS]
                if video_output_files:
                    logging.debug(f"Using LatentSync video outputs: {video_output_files}")
                    output_filenames = video_output_files

                if not output_filenames:
                    logging.warning(f"No output files found for prompt ID {prompt_id}. Skipping concatenation for this pair.")
                    success = True 
                else:
                    for fname in output_filenames:
                        full_output_path = os.path.join(self.config['comfyui_output_base_dir'], fname)
                        logging.debug(f"Checking for output file at: {full_output_path}") # Added debug log
                        if os.path.exists(full_output_path):
                            pair_intermediate_files.append(full_output_path)
                        else:
                            potential_alt_path = os.path.join(self.config['output_dir'], fname)
                            if os.path.exists(potential_alt_path):
                                pair_intermediate_files.append(potential_alt_path)
                                logging.warning(f"Output file '{fname}' not in ComfyUI base output ({self.config['comfyui_output_base_dir']}), found in main output dir ({self.config['output_dir']}).")
                            else:
                                logging.error(f"Could not find processed output file: {fname} in expected ComfyUI output ({self.config['comfyui_output_base_dir']}) or main output dir ({self.config['output_dir']})")
                    
                    if not pair_intermediate_files:
                        logging.error(f"ComfyUI reported output files ({output_filenames}) but none could be found on disk.")
                        raise RuntimeError("Expected output files not found on disk.")
                    else:
                        self.all_intermediate_files.extend(pair_intermediate_files)
                        success = True
                        logging.info(f"Pair {i+1} completed successfully. Found intermediate files: {pair_intermediate_files}")

            except Exception as e:
                error_msg = f"Error processing pair {i+1} ({video_basename}/{audio_basename}): {e}"
                logging.error(error_msg)
                self.signals.error.emit(error_msg) 
                self.any_errors = True 
                success = False
            
            finally:
                # Cleanup temporary trimmed file
                if tmp_trimmed and os.path.exists(tmp_trimmed):
                    try:
                        os.remove(tmp_trimmed)
                    except Exception as cleanup_err:
                        logging.warning(f"Failed to remove temp file {tmp_trimmed}: {cleanup_err}")

            # Emit signal regardless of success/failure for this pair
            self.signals.pair_finished.emit(pair_index, success, pair_intermediate_files) 
            time.sleep(0.1) 
        
        logging.info(f"Batch processing loop finished. Errors occurred: {self.any_errors}. Total intermediate files: {len(self.all_intermediate_files)}")
        self.signals.all_finished.emit(list(self.all_intermediate_files), self.any_errors)

    def cancel(self):
        self.is_cancelled = True
        self.signals.progress.emit("Cancellation requested...")


class MainWindow(QMainWindow):
    VALID_EMOTIONS = {"happiness", "sadness", "disgust", "fear", "surprise", "anger", "other", "neutral"}

    def _post_process_ollama_script(self, script_text: str) -> str:
        processed_lines = []
        for line in script_text.splitlines():
            match = re.search(r"(emotion=)({.*?})", line)
            if match:
                prefix = match.group(1)  # "emotion="
                emotion_block_str = match.group(2)  # "{key:val, ...}"
                corrected_emotion_payload_str = '{"neutral":1.0}' # Default

                try:
                    # Attempt to make the content inside braces valid for json.loads
                    # Content is from emotion_block_str[1:-1] if it has braces
                    inner_content = emotion_block_str.strip()
                    if inner_content.startswith('{') and inner_content.endswith('}'):
                        inner_content = inner_content[1:-1]
                    
                    parsed_emotions = {}
                    if inner_content.strip(): # If there's anything to parse
                        # Manual parsing for robustness against malformed AI output
                        # Split by comma, then by colon, clean up keys and values
                        pairs = inner_content.split(',')
                        for pair_str in pairs:
                            if ':' not in pair_str:
                                continue
                            key_part, val_part = pair_str.split(':', 1)
                            key = key_part.strip().replace("'", "").replace('"', '')
                            
                            try:
                                intensity = float(val_part.strip())
                                intensity = max(0.0, min(1.0, intensity)) # Clamp
                            except ValueError:
                                intensity = 0.5 # Default for unparseable intensity
                            
                            if key in self.VALID_EMOTIONS:
                                parsed_emotions[key] = intensity
                            # Else: invalid keys are dropped

                    if parsed_emotions: # If any valid emotions were parsed
                        # Ensure keys are double-quoted for valid JSON output
                        corrected_emotion_payload_str = '{' + ', '.join([f'\"{k}\": {v}' for k, v in parsed_emotions.items()]) + '}'
                    # Else: it remains default '{"neutral":1.0}'
                    
                    line = line.replace(match.group(0), prefix + corrected_emotion_payload_str, 1)
                except Exception as e:
                    # If any error during this complex parsing, fallback to neutral
                    logging.warning(f"Error post-processing emotion: {e}. Line: '{line}'. Defaulting emotion.")
                    line = re.sub(r"emotion={.*?}", 'emotion={\"neutral\":1.0}', line, 1)
            
            processed_lines.append(line)
        return "\n".join(processed_lines)

    def __init__(self):
        super().__init__()

        # Initialize from configuration
        self.setWindowTitle(config.ui.window_title)
        self.setGeometry(*config.ui.window_geometry)

        self.current_selected_video_path = None
        self.current_selected_audio_path = None
        self.processing_queue = []
        self.all_intermediate_files = []

        # Use speaker folders from configuration
        self.subfolder_names = config.ui.speaker_folders.copy()

        self.speaker_image_folders = {}
        self.speaker_audio_files = {}

        self.threadpool = QThreadPool()
        print(f"Multithreading with maximum {self.threadpool.maxThreadCount()} threads")

        # Initialize advanced features
        self._initialize_advanced_features()

        # Initialize attributes for selected files (needed before panel creation)
        self.current_selected_video_path = None
        self.current_selected_audio_path = None

        # --- Create Media Player and Video Widget EARLY --- 
        # Create the video widget here so it's available for the input files panel
        self.video_widget = QVideoWidget()
        self.video_widget.setStyleSheet("background-color: black;") # Make it visible
        self._setup_media_player() # Setup player components (player, audio output)

        # Create Dock Widgets
        self.media_dock = QDockWidget("Available Media Files", self) # Renamed slightly
        self.media_dock.setWidget(self._create_input_files_panel())
        self.media_dock.setAllowedAreas(Qt.DockWidgetArea.LeftDockWidgetArea)
        self.media_dock.setFeatures(QDockWidget.DockWidgetFeature.NoDockWidgetFeatures) # Keep it fixed on left
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, self.media_dock)

        self.queue_config_log_dock = QDockWidget("Queue & Configuration", self)
        self.queue_config_log_dock.setWidget(self._create_queue_config_log_panel()) # This panel includes Queue, Config, Log tabs
        self.queue_config_log_dock.setAllowedAreas(Qt.DockWidgetArea.RightDockWidgetArea)
        self.queue_config_log_dock.setFeatures(QDockWidget.DockWidgetFeature.NoDockWidgetFeatures) # Keep it fixed on right
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.queue_config_log_dock)

        # --- Create a NEW Dock for the Preview --- 
        self.preview_dock = QDockWidget("Media Preview", self)
        self.preview_dock.setWidget(self.video_widget) # Use the pre-created video widget
        self.preview_dock.setAllowedAreas(Qt.DockWidgetArea.RightDockWidgetArea)
        self.preview_dock.setFeatures(QDockWidget.DockWidgetFeature.NoDockWidgetFeatures) # Keep it fixed on right
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.preview_dock)

        # Place Preview dock BELOW the Queue/Config dock
        self.splitDockWidget(self.queue_config_log_dock, self.preview_dock, Qt.Orientation.Vertical)

        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

        # Setup logging to capture to the Log tab
        self.log_emitter = LogSignalEmitter() # Create emitter instance
        self.log_handler = QtLogHandler(self.log_emitter) # Pass emitter to handler
        self.log_emitter.log_signal.connect(self._append_log_message) # Connect to emitter's signal
        logging.getLogger().addHandler(self.log_handler)
        logging.getLogger().setLevel(logging.DEBUG) # Ensure root logger captures DEBUG

        # Initial population of file lists
        self._refresh_file_lists()

    def _setup_media_player(self):
        """Initializes the QMediaPlayer and its associated QVideoWidget."""
        try:
            # --- MOVED WIDGET CREATION TO __init__ ---
            # self.video_widget = QVideoWidget()
            # self.video_widget.setStyleSheet("background-color: black;") # Optional: make it visible

            self.media_player = QMediaPlayer()
            self.audio_output = QAudioOutput() # Required for audio playback
            self.media_player.setAudioOutput(self.audio_output)
            self.media_player.errorOccurred.connect(self._handle_media_error)

            # Set the video output to the PRE-CREATED video_widget
            if hasattr(self, 'video_widget') and self.video_widget:
                self.media_player.setVideoOutput(self.video_widget)
                logging.debug("QMediaPlayer video output set to QVideoWidget.")
            else:
                logging.error("Video widget not initialized before setting media player output!")

        except Exception as e:
            logging.error(f"Error initializing media player: {e}", exc_info=True)
            QMessageBox.critical(self, "Media Player Error", f"Could not initialize media player components: {e}")

    def _create_config_panel(self):
        config_group = QGroupBox("Configuration")
        layout = QVBoxLayout()

        def create_row(label_text, default_value="", is_dir=False, is_file=False):
            row_layout = QHBoxLayout()
            label = QLabel(label_text)
            line_edit = QLineEdit(default_value)
            row_layout.addWidget(label)
            row_layout.addWidget(line_edit)
            if is_dir or is_file:
                button = QPushButton("Browse...")
                row_layout.addWidget(button)
                return row_layout, line_edit, button
            else:
                return row_layout, line_edit, None

        # Use configuration defaults
        url_layout, self.comfyui_url_edit, _ = create_row("ComfyUI URL:", config.comfyui.url)
        layout.addLayout(url_layout)

        # Set default workflow path from config
        wf_layout, self.workflow_path_edit, wf_button = create_row("Workflow File:", config.workflow.latentsync_workflow_path, is_file=True)
        wf_button.clicked.connect(self._browse_workflow_file)
        layout.addLayout(wf_layout)

        # FramePack workflow file selection from config
        fpf_layout, self.framepack_workflow_path_edit, fpf_button = create_row(
            "FramePack Workflow File:", config.workflow.framepack_workflow_path, is_file=True
        )
        fpf_button.clicked.connect(self._browse_framepack_workflow_file)
        layout.addLayout(fpf_layout)

        # ComfyUI output directory selection from config
        cop_layout, self.comfyui_output_base_dir_edit, cop_button = create_row(
            "ComfyUI Output Directory:", config.comfyui.output_base_dir, is_dir=True
        )
        cop_button.clicked.connect(self._browse_comfyui_output_dir)
        layout.addLayout(cop_layout)

        vid_layout, self.input_dir_input, vid_button = create_row("Input Directory:", config.media.input_dir, is_dir=True)
        vid_button.clicked.connect(self._browse_input_dir)
        self.input_dir_input.editingFinished.connect(self._refresh_file_lists)
        layout.addLayout(vid_layout)

        out_layout, self.output_dir_input, out_button = create_row("Output Dir:", config.media.output_dir, is_dir=True)
        out_button.clicked.connect(self._browse_output_dir)
        layout.addLayout(out_layout)

        # Zonos API Key from config
        key_layout, self.zonos_api_key_input, _ = create_row("Zonos API Key:", config.tts.zonos_api_key)
        layout.addLayout(key_layout)
        # TTS Output Dir from config
        tts_layout, self.tts_output_dir_input, tts_btn = create_row("TTS Output Dir:", config.tts.output_dir, is_dir=True)
        tts_btn.clicked.connect(self._browse_tts_output_dir)
        layout.addLayout(tts_layout)

        # --- Add Use Local Zonos TTS Checkbox ---
        self.use_local_tts_checkbox = QCheckBox("Use Local Zonos TTS")
        self.use_local_tts_checkbox.setToolTip("If checked, Zonos TTS will be generated locally instead of using the API.")
        layout.addWidget(self.use_local_tts_checkbox)
        # -----------------------------------------

        # Advanced Auto-Pairing options
        self.auto_pair_duration_checkbox = QCheckBox("Smart Pair by Duration")
        layout.addWidget(self.auto_pair_duration_checkbox)
        regex_layout = QHBoxLayout()
        regex_label = QLabel("Filename Regex Pattern:")
        self.auto_pair_regex_input = QLineEdit()
        regex_layout.addWidget(regex_label)
        regex_layout.addWidget(self.auto_pair_regex_input)
        layout.addLayout(regex_layout)

        # Prepare configuration for the backend

        # FramePack Configuration Inputs
        prompt_label = QLabel("FramePack Prompt:")
        self.framepack_prompt_input = QTextEdit()
        self.framepack_prompt_input.setPlainText(config.parameters.framepack_prompt)
        layout.addWidget(prompt_label)
        layout.addWidget(self.framepack_prompt_input)
        seed_layout = QHBoxLayout()
        seed_label = QLabel("FramePack Seed:")
        self.framepack_seed_input = QSpinBox()
        self.framepack_seed_input.setRange(0, 2**31 - 1) # Range must fit signed 32-bit int
        self.framepack_seed_input.setValue(config.parameters.framepack_seed)
        seed_layout.addWidget(seed_label)
        seed_layout.addWidget(self.framepack_seed_input)
        layout.addLayout(seed_layout)
        self.framepack_random_checkbox = QCheckBox("Randomize FramePack Seed")
        self.framepack_random_checkbox.setChecked(config.parameters.framepack_random)
        layout.addWidget(self.framepack_random_checkbox)

        config_group.setLayout(layout)
        return config_group

    def _create_workflow_params_panel(self):
        """Creates the panel for workflow-specific parameters."""
        group = QGroupBox("Workflow Parameters")
        layout = QFormLayout()

        # Defaults - Load the workflow to get defaults, handle errors
        defaults = {}
        try:
            workflow_path = self.workflow_path_edit.text()
            if os.path.exists(workflow_path):
                with open(workflow_path, 'r') as f:
                    wf = json.load(f)
                    defaults = {
                        'seed': wf.get('3', {}).get('inputs', {}).get('seed', 0),
                        'lips_expression': wf.get('3', {}).get('inputs', {}).get('lips_expression', 1.0),
                        'inference_steps': wf.get('3', {}).get('inputs', {}).get('inference_steps', 20),
                        'crf': wf.get('5', {}).get('inputs', {}).get('crf', 19),
                        'silent_padding_sec': wf.get('2', {}).get('inputs', {}).get('silent_padding_sec', 0.3)
                    }
            else:
                logging.warning(f"Default workflow not found at: {workflow_path}. Using hardcoded defaults for params.")
                defaults = {'seed': 0, 'lips_expression': 1.0, 'inference_steps': 20, 'crf': 19, 'silent_padding_sec': 0.3}

        except Exception as e:
            logging.error(f"Error loading workflow for default parameters: {e}")
            QMessageBox.warning(self, "Workflow Load Error", f"Could not load default workflow to get parameters: {e}\nUsing hardcoded defaults.")
            # Use configuration defaults
            defaults = {
                'seed': config.parameters.seed,
                'lips_expression': config.parameters.lips_expression,
                'inference_steps': config.parameters.inference_steps,
                'crf': config.parameters.crf,
                'silent_padding_sec': config.parameters.silent_padding_sec
            }


        # Seed (Node 3)
        self.seed_input = QSpinBox()
        self.seed_input.setRange(-1, 2**31 - 1) # Use signed 32-bit max
        self.seed_input.setValue(int(defaults.get('seed', 0)))
        layout.addRow("Seed:", self.seed_input)

        # Lips Expression (Node 3)
        self.lips_expr_input = QDoubleSpinBox()
        self.lips_expr_input.setRange(0.0, 10.0) # Adjust range as needed
        self.lips_expr_input.setDecimals(2)
        self.lips_expr_input.setSingleStep(0.1)
        self.lips_expr_input.setValue(float(defaults.get('lips_expression', 1.0)))
        layout.addRow("Lips Expression:", self.lips_expr_input)

        # Inference Steps (Node 3)
        self.inference_steps_input = QSpinBox()
        self.inference_steps_input.setRange(1, 200) # Adjust range as needed
        self.inference_steps_input.setValue(int(defaults.get('inference_steps', 20)))
        layout.addRow("Inference Steps:", self.inference_steps_input)

        # CRF (Node 5)
        self.crf_input = QSpinBox()
        self.crf_input.setRange(0, 51) # 0 (lossless) to 51 (worst)
        self.crf_input.setValue(int(defaults.get('crf', 19)))
        layout.addRow("CRF (Video Quality):", self.crf_input)

        # Silent Padding (Node 2)
        self.silent_padding_input = QDoubleSpinBox()
        self.silent_padding_input.setRange(0.0, 5.0) # Adjust range as needed
        self.silent_padding_input.setDecimals(2)
        self.silent_padding_input.setSingleStep(0.1)
        self.silent_padding_input.setValue(float(defaults.get('silent_padding_sec', 0.3)))
        layout.addRow("Silent Padding (sec):", self.silent_padding_input)

        group.setLayout(layout)
        return group

    def _create_input_files_panel(self):
        """Creates the main panel holding the input file lists and media preview."""
        input_files_group = QGroupBox("Input Files") # Renamed Group Box
        main_group_layout = QVBoxLayout(input_files_group)

        # Create a splitter for vertical resizing
        splitter = QSplitter(Qt.Orientation.Vertical)

        # --- Widget for File Lists (Top part of splitter) ---
        file_lists_widget = QWidget()
        file_lists_layout = QGridLayout(file_lists_widget) # Use Grid Layout for lists

        # --- Speaker 1 Videos ---
        s1_vid_label = QLabel("Speaker 1 Videos:")
        self.s1_video_list = QListWidget()
        self.s1_video_list.setIconSize(QSize(100, 60)) # Thumbnail size
        self.s1_video_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.s1_video_list.itemClicked.connect(self._on_media_item_clicked) # Playback on single click
        self.s1_video_list.itemDoubleClicked.connect(self._update_selection_state) # Select on double click
        # self.s1_video_list.itemSelectionChanged.connect(self._update_selection_state) # Remove selection change connection
        s1_vid_refresh_button = QPushButton("Refresh")
        s1_vid_refresh_button.clicked.connect(lambda: self._refresh_specific_list('s1_vid'))
        file_lists_layout.addWidget(s1_vid_label, 0, 0)
        file_lists_layout.addWidget(self.s1_video_list, 1, 0)
        file_lists_layout.addWidget(s1_vid_refresh_button, 2, 0)

        # --- Speaker 2 Videos ---
        s2_vid_label = QLabel("Speaker 2 Videos:")
        self.s2_video_list = QListWidget()
        self.s2_video_list.setIconSize(QSize(100, 60))
        self.s2_video_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.s2_video_list.itemClicked.connect(self._on_media_item_clicked) # Playback on single click
        self.s2_video_list.itemDoubleClicked.connect(self._update_selection_state) # Select on double click
        # self.s2_video_list.itemSelectionChanged.connect(self._update_selection_state) # Remove selection change connection
        s2_vid_refresh_button = QPushButton("Refresh")
        s2_vid_refresh_button.clicked.connect(lambda: self._refresh_specific_list('s2_vid'))
        file_lists_layout.addWidget(s2_vid_label, 0, 1) # Changed column to 1
        file_lists_layout.addWidget(self.s2_video_list, 1, 1) # Changed column to 1
        file_lists_layout.addWidget(s2_vid_refresh_button, 2, 1) # Changed column to 1

        # --- Speaker 1 Audios ---
        s1_aud_label = QLabel("Speaker 1 Audios:")
        self.s1_audio_list = QListWidget()
        self.s1_audio_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.s1_audio_list.itemClicked.connect(self._on_media_item_clicked) # Playback on single click
        self.s1_audio_list.itemDoubleClicked.connect(self._update_selection_state) # Select on double click
        # self.s1_audio_list.itemSelectionChanged.connect(self._update_selection_state) # Remove selection change connection
        s1_aud_refresh_button = QPushButton("Refresh")
        s1_aud_refresh_button.clicked.connect(lambda: self._refresh_specific_list('s1_aud'))
        file_lists_layout.addWidget(s1_aud_label, 3, 0) # Changed row to 3
        file_lists_layout.addWidget(self.s1_audio_list, 4, 0) # Changed row to 4
        file_lists_layout.addWidget(s1_aud_refresh_button, 5, 0) # Changed row to 5

        # --- Speaker 2 Audios ---
        s2_aud_label = QLabel("Speaker 2 Audios:")
        self.s2_audio_list = QListWidget()
        self.s2_audio_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.s2_audio_list.itemClicked.connect(self._on_media_item_clicked) # Playback on single click
        self.s2_audio_list.itemDoubleClicked.connect(self._update_selection_state) # Select on double click
        # self.s2_audio_list.itemSelectionChanged.connect(self._update_selection_state) # Remove selection change connection
        s2_aud_refresh_button = QPushButton("Refresh")
        s2_aud_refresh_button.clicked.connect(lambda: self._refresh_specific_list('s2_aud'))
        file_lists_layout.addWidget(s2_aud_label, 3, 1) # Changed row to 3, column to 1
        file_lists_layout.addWidget(self.s2_audio_list, 4, 1) # Changed row to 4, column to 1
        file_lists_layout.addWidget(s2_aud_refresh_button, 5, 1) # Changed row to 5, column to 1

        # Add the file lists widget to the splitter
        splitter.addWidget(file_lists_widget)

        # --- Media Preview Widget (Middle part of splitter) ---
        # Video widget is created in _setup_media_player, add it here
        # preview_container = QWidget() # Use a container for potential future additions
        # preview_layout = QVBoxLayout(preview_container)
        # if hasattr(self, 'video_widget') and self.video_widget:
        #     preview_layout.addWidget(self.video_widget)
        # else:
        #     # Should not happen if __init__ order is correct
        #     preview_layout.addWidget(QLabel("Error: Media Preview unavailable.")) 
        # preview_layout.setContentsMargins(0, 5, 0, 5) # Add some vertical spacing
        # splitter.addWidget(preview_container) 

        # --- Widget for Manual Pairing Button (Bottom part of splitter) ---
        manual_pair_widget = QWidget()
        manual_pair_layout = QVBoxLayout(manual_pair_widget)

        # --- Currently Selected Group ---
        selected_group = QGroupBox("Currently Selected Pair")
        selected_layout = QFormLayout(selected_group)
        self.selected_video_display_label = QLabel("None")
        self.selected_audio_display_label = QLabel("None")
        self.selected_video_display_label.setWordWrap(True)
        self.selected_audio_display_label.setWordWrap(True)
        selected_layout.addRow("Video:", self.selected_video_display_label)
        selected_layout.addRow("Audio:", self.selected_audio_display_label)
        manual_pair_layout.addWidget(selected_group)
        # ------------------------------

        self.add_pair_button = QPushButton("Add Selected Pair to Queue")
        self.add_pair_button.clicked.connect(self._add_selected_pair_to_queue)
        self.add_pair_button.setEnabled(False) # Initially disabled
        manual_pair_layout.addWidget(self.add_pair_button)
        # manual_pair_layout.addStretch() # Remove stretch to keep button closer

        # Add the manual pairing widget to the splitter
        splitter.addWidget(manual_pair_widget)

        # Configure splitter sizes (adjust as needed for 3 sections)
        splitter.setSizes([300, 100]) # Initial height distribution (Lists, Button)

        main_group_layout.addWidget(splitter)

        return input_files_group

    def _create_queue_config_log_panel(self):
        tab_widget = QTabWidget()

        queue_tab_content = QWidget()
        queue_tab_layout = QVBoxLayout(queue_tab_content)

        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0) 

        self.process_button = QPushButton("Start Processing Queue")
        self.process_button.setIcon(QIcon.fromTheme("system-run")) 
        self.process_button.clicked.connect(self._start_processing)
        self.process_button.setStyleSheet("QPushButton { padding: 10px; font-size: 14px; background-color: #4CAF50; color: white; border-radius: 5px; } QPushButton:hover { background-color: #45a049; } QPushButton:disabled { background-color: #cccccc; }")
        
        queue_group = QGroupBox("Processing Queue")
        queue_layout = QVBoxLayout()
        
        self.queue_list_widget = QListWidget()
        self.queue_list_widget.setAlternatingRowColors(True)
        self.queue_list_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.queue_list_widget.setDragEnabled(True)
        self.queue_list_widget.setAcceptDrops(True)
        self.queue_list_widget.setDropIndicatorShown(True)
        self.queue_list_widget.setDragDropMode(QAbstractItemView.InternalMove)
        self.queue_list_widget.model().rowsMoved.connect(self._on_queue_reordered)
        self.queue_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.queue_list_widget.customContextMenuRequested.connect(self._show_queue_item_context_menu)
        queue_layout.addWidget(self.queue_list_widget)

        queue_button_layout = QHBoxLayout()

        self.add_to_queue_button = QPushButton("Add Selected Pair to Queue")
        self.add_to_queue_button.setIcon(QIcon.fromTheme("list-add"))
        self.add_to_queue_button.clicked.connect(self._add_selected_pair_to_queue)
        queue_button_layout.addWidget(self.add_to_queue_button)

        # --- Add Auto-Pair All Button ---
        self.auto_pair_button = QPushButton("Auto-Pair All")
        self.auto_pair_button.setIcon(QIcon.fromTheme("view-refresh")) # Or a better icon
        self.auto_pair_button.setToolTip("Automatically pair speaker audio files sequentially with speaker video files.")
        self.auto_pair_button.clicked.connect(self._auto_pair_all)
        queue_button_layout.addWidget(self.auto_pair_button)
        # ---------------------------------

        # --- Add Remove Selected Button ---
        self.remove_selected_button = QPushButton("Remove Selected Pair")
        self.remove_selected_button.setIcon(QIcon.fromTheme("list-remove")) # Or a suitable icon
        self.remove_selected_button.clicked.connect(self._remove_selected_queue_item)
        self.remove_selected_button.setEnabled(False) # Initially disabled, enable on selection
        queue_button_layout.addWidget(self.remove_selected_button)
        # -----------------------------------

        self.clear_queue_button = QPushButton("Clear Queue")
        self.clear_queue_button.setIcon(QIcon.fromTheme("edit-clear"))
        self.clear_queue_button.clicked.connect(self._clear_queue)
        queue_button_layout.addWidget(self.clear_queue_button)

        # Save/Load Queue buttons
        self.save_queue_button = QPushButton("Save Queue")
        self.save_queue_button.setIcon(QIcon.fromTheme("document-save"))
        self.save_queue_button.clicked.connect(self._save_queue)
        queue_button_layout.addWidget(self.save_queue_button)
        self.load_queue_button = QPushButton("Load Queue")
        self.load_queue_button.setIcon(QIcon.fromTheme("document-open"))
        self.load_queue_button.clicked.connect(self._load_queue)
        queue_button_layout.addWidget(self.load_queue_button)

        queue_layout.addLayout(queue_button_layout)
        queue_group.setLayout(queue_layout)
        
        layout.addWidget(queue_group)
        layout.addWidget(self.process_button) 
        layout.addStretch() 

        panel.setLayout(layout)
        queue_tab_layout.addWidget(panel)
        tab_widget.addTab(queue_tab_content, "Queue")

        # --- Enable/disable remove button based on queue selection ---
        self.queue_list_widget.itemSelectionChanged.connect(
            lambda: self.remove_selected_button.setEnabled(bool(self.queue_list_widget.selectedItems()))
        )
        # -----------------------------------------------------------

        config_tab_content = QWidget()
        config_tab_layout = QVBoxLayout(config_tab_content)
        config_tab_layout.addWidget(self._create_config_panel())
        config_tab_layout.addWidget(self._create_workflow_params_panel())
        tab_widget.addTab(config_tab_content, "Configuration")

        log_tab_content = QWidget()
        log_tab_layout = QVBoxLayout(log_tab_content)
        self.log_text_edit = QTextEdit() # Assign to self
        self.log_text_edit.setReadOnly(True) # Make log view read-only
        log_tab_layout.addWidget(self.log_text_edit)
        tab_widget.addTab(log_tab_content, "Log")

        # --- Podcast Script Tab ---
        podcast_tab = QWidget()
        podcast_layout = QVBoxLayout(podcast_tab)
        instr = QLabel(
            "Use this format to create your script:\n"
            "[SPEAKER_NAME, emotion(intensity), speed=value]: Text\n\n"
            "Example:\n"
            "[HOST, happiness(0.8), speed=20]: Welcome to our podcast! I'm so *happy* to have you here.\n"
            "[GUEST, sadness(0.6), speed=15]: Thank you. It's been a tough week, but I'm glad to join.\n"
            "[HOST, surprise(0.7), speed=18]: Oh, I didn't *expect* that. Want to talk about it?\n"
            "[GUEST, neutral, speed=15]: Maybe a little. Let's get started with our main topic.\n\n"
            "Supported emotions: happiness, sadness, disgust, fear, surprise, anger, other, neutral\n\n"
            "- Emphasize important words with *word* or **word** for stronger emphasis.\n"
            "- De-emphasize words with _word_.\n"
            "- Vary speaking speeds (5-35) based on emotion and content.\n"
        )
        instr.setWordWrap(True)
        podcast_layout.addWidget(instr)
        self.script_text = QTextEdit()
        self.script_text.setPlaceholderText("Enter your script here…")
        podcast_layout.addWidget(self.script_text)
        self.analyze_btn = QPushButton("Analyze Script for Speakers")
        self.analyze_btn.clicked.connect(self.analyze_script)
        podcast_layout.addWidget(self.analyze_btn)
        self.speaker_list = QListWidget()
        self.speaker_list.setSelectionMode(QAbstractItemView.SingleSelection)
        podcast_layout.addWidget(self.speaker_list)
        self.preview_voice_btn = QPushButton("Preview Voice")
        self.preview_voice_btn.setEnabled(False)
        self.preview_voice_btn.clicked.connect(self.preview_voice)
        podcast_layout.addWidget(self.preview_voice_btn)
        # Full audio preview button
        self.preview_full_audio_btn = QPushButton("Preview Full Audio")
        self.preview_full_audio_btn.setEnabled(False)
        self.preview_full_audio_btn.clicked.connect(self.preview_full_audio)
        podcast_layout.addWidget(self.preview_full_audio_btn)
        # Full audio playback controls
        self.play_all_btn = QPushButton("Play All"); self.play_all_btn.setEnabled(False)
        self.pause_all_btn = QPushButton("Pause"); self.pause_all_btn.setEnabled(False)
        self.stop_all_btn = QPushButton("Stop"); self.stop_all_btn.setEnabled(False)
        self.audio_slider = QSlider(Qt.Horizontal); self.audio_slider.setEnabled(False)
        controls_layout = QHBoxLayout()
        controls_layout.addWidget(self.play_all_btn)
        controls_layout.addWidget(self.pause_all_btn)
        controls_layout.addWidget(self.stop_all_btn)
        controls_layout.addWidget(self.audio_slider)
        podcast_layout.addLayout(controls_layout)
        # Voice Assignment UI
        self.speaker_list.currentItemChanged.connect(self.on_speaker_selected)
        assign_hbox = QHBoxLayout()
        self.voice_combo = QComboBox()
        self.populate_voice_combo() # Populate with initial voices
        assign_hbox.addWidget(self.voice_combo)
        self.assign_voice_btn = QPushButton("Assign Voice")
        self.assign_voice_btn.setEnabled(False)
        self.assign_voice_btn.clicked.connect(self.assign_voice)
        assign_hbox.addWidget(self.assign_voice_btn)
        podcast_layout.addLayout(assign_hbox)
        # Image folder picker
        img_hbox = QHBoxLayout()
        self.image_folder_input = QLineEdit()
        self.image_folder_input.setPlaceholderText("Select image folder…")
        self.image_folder_input.setEnabled(False)
        self.browse_image_folder_btn = QPushButton("Browse Images")
        self.browse_image_folder_btn.setEnabled(False)
        self.browse_image_folder_btn.clicked.connect(self.browse_image_folder)
        img_hbox.addWidget(self.image_folder_input)
        img_hbox.addWidget(self.browse_image_folder_btn)
        podcast_layout.addLayout(img_hbox)
        # Voice Cloning
        clone_box = QGroupBox("Voice Cloning")
        clone_form = QFormLayout(clone_box)
        self.upload_voice_btn = QPushButton("Upload Your Voice Sample")
        self.upload_voice_btn.clicked.connect(self.upload_voice_sample)
        clone_form.addRow(self.upload_voice_btn)
        self.voice_name_input = QLineEdit()
        self.voice_name_input.setPlaceholderText("e.g., My Voice")
        clone_form.addRow("Name for this voice:", self.voice_name_input)
        podcast_layout.addWidget(clone_box)
        self.generate_podcast_btn = QPushButton("Generate Podcast")
        self.generate_podcast_btn.clicked.connect(self.generate_podcast)
        podcast_layout.addWidget(self.generate_podcast_btn)
        tab_widget.addTab(podcast_tab, "Podcast Script")

        # LLM settings
        llm_tab = QWidget()
        llm_layout = QVBoxLayout(llm_tab)
        llm_hbox = QHBoxLayout()
        self.llm_provider_combo = QComboBox()
        self.llm_provider_combo.addItems(["Ollama", "OpenRouter"])
        self.llm_provider_combo.currentTextChanged.connect(self.on_llm_provider_changed)
        llm_hbox.addWidget(self.llm_provider_combo)
        self.ollama_url_input = QLineEdit()
        self.ollama_url_input.setPlaceholderText("Ollama URL")
        self.ollama_url_input.setText("http://127.0.0.1:11434")
        llm_hbox.addWidget(self.ollama_url_input)
        self.openrouter_url_input = QLineEdit()
        self.openrouter_url_input.setPlaceholderText("OpenRouter URL")
        llm_hbox.addWidget(self.openrouter_url_input)
        self.openrouter_key_input = QLineEdit()
        self.openrouter_key_input.setPlaceholderText("OpenRouter Key")
        self.openrouter_key_input.setText("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")
        llm_hbox.addWidget(self.openrouter_key_input)
        self.llm_model_combo = QComboBox()
        llm_hbox.addWidget(self.llm_model_combo)
        self.refresh_models_btn = QPushButton("Refresh Models")
        self.refresh_models_btn.clicked.connect(self.refresh_models)
        llm_hbox.addWidget(self.refresh_models_btn)
        self.generate_script_btn = QPushButton("Generate Script")
        self.generate_script_btn.clicked.connect(self.handle_generate_script)
        llm_hbox.addWidget(self.generate_script_btn)
        llm_layout.addLayout(llm_hbox)
        params_hbox = QHBoxLayout()
        params_hbox.addWidget(QLabel("Speakers:"))
        self.num_speakers_spin = QSpinBox()
        self.num_speakers_spin.setRange(1, 10)
        params_hbox.addWidget(self.num_speakers_spin)
        params_hbox.addWidget(QLabel("Names (comma-separated):"))
        self.speaker_names_input = QLineEdit()
        params_hbox.addWidget(self.speaker_names_input)
        params_hbox.addWidget(QLabel("Subject:"))
        self.subject_input = QLineEdit()
        params_hbox.addWidget(self.subject_input)
        llm_layout.addLayout(params_hbox)
        tab_widget.addTab(llm_tab, "LLM Script Generation")

        return tab_widget


    def _browse_workflow_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Workflow File", "", "JSON Files (*.json)")
        if file_path:
            self.workflow_path_edit.setText(file_path)

    def _browse_framepack_workflow_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select FramePack Workflow File", "", "JSON Files (*.json)")
        if file_path:
            self.framepack_workflow_path_edit.setText(file_path)

    def _browse_input_dir(self):
        directory = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if directory:
            self.input_dir_input.setText(directory)
            self._refresh_file_lists()

    def _browse_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "Select Output Directory", self.output_dir_input.text())
        if dir_path:
            self.output_dir_input.setText(dir_path)

    def _browse_comfyui_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "Select ComfyUI Output Directory")
        if dir_path:
            self.comfyui_output_base_dir_edit.setText(dir_path)

    def _browse_tts_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "Select TTS Output Directory")
        if dir_path:
            self.tts_output_dir_input.setText(dir_path)

    def _refresh_file_lists(self):
        """Refreshes all four input file lists."""
        logging.info("Refreshing all input file lists...")
        base_input_dir = self.input_dir_input.text()
        if not base_input_dir or not os.path.isdir(base_input_dir):
            QMessageBox.warning(self, "Input Directory Error",
                                "Please select a valid base Input Directory in the Configuration tab.")
            # Clear all lists if base dir is invalid
            self.s1_video_list.clear()
            self.s2_video_list.clear()
            self.s1_audio_list.clear()
            self.s2_audio_list.clear()
            self.s1_video_list.addItem("(Set Input Dir)")
            self.s2_video_list.addItem("(Set Input Dir)")
            self.s1_audio_list.addItem("(Set Input Dir)")
            self.s2_audio_list.addItem("(Set Input Dir)")
            return

        # Map list keys to their widgets
        list_map = {
            's1_vid': self.s1_video_list,
            's2_vid': self.s2_video_list,
            's1_aud': self.s1_audio_list,
            's2_aud': self.s2_audio_list
        }

        # Use the updated _refresh_list method for all lists
        for list_key, list_widget in list_map.items():
            # Get the specific directory for this list from config (e.g., config['s1_vid_dir'])
            # Note: The _refresh_list method itself now handles getting the dir from config
            self._refresh_list(list_widget, list_key)

        # Reset selections after full refresh
        self._update_selection_state()

    def _get_selected_file_path(self, list_widget):
        """Helper to get file path from selected item's data."""
        selected_items = list_widget.selectedItems()
        if selected_items:
            item = selected_items[0]
            return item.data(Qt.ItemDataRole.UserRole) # Get path from user data
        return None

    def _update_selection_state(self):
        """Updates the internal state and labels for the currently selected video/audio.
         Called on double-click or potentially during refresh.
         """
        sender = self.sender()
        if sender:
            # Only get sender info if called as a slot
            sender_name = sender.objectName()
            file_path = self._get_selected_file_path(sender)
            logging.debug(f"_update_selection_state triggered by: {sender_name}")
            logging.debug(f"  Selected item path: {file_path}")

            if not file_path:
                logging.warning("_update_selection_state: No file path found for selected item.")
                # Optionally clear selection or just return if this is unexpected
                # self.current_selected_video_path = None
                # self.current_selected_audio_path = None
                # self._update_selection_labels()
                return 

            # Determine if it's a video or audio list based on the sender object
            is_video = sender in [self.s1_video_list, self.s2_video_list]
            is_audio = sender in [self.s1_audio_list, self.s2_audio_list]

            if is_video:
                self.current_selected_video_path = file_path
                # Don't clear the other lists' selections on double-click
                # self.s1_video_list.clearSelection()
                # self.s2_video_list.clearSelection()
                logging.debug(f"  Updated current_selected_video_path to: {self.current_selected_video_path}")
            elif is_audio:
                self.current_selected_audio_path = file_path
                # Don't clear the other lists' selections on double-click
                # self.s1_audio_list.clearSelection()
                # self.s2_audio_list.clearSelection()
                logging.debug(f"  Updated current_selected_audio_path to: {self.current_selected_audio_path}")
            else:
                logging.warning(f"_update_selection_state called by unexpected sender: {sender_name}")
        else:
            # Called directly (e.g., during refresh), likely just need to clear
            logging.debug("_update_selection_state called directly (likely init/refresh). Clearing selections.")
            self.current_selected_video_path = None
            self.current_selected_audio_path = None

        # Update the display labels based on the current state
        video_base = os.path.basename(self.current_selected_video_path) if self.current_selected_video_path else "None"
        audio_base = os.path.basename(self.current_selected_audio_path) if self.current_selected_audio_path else "None"
        self.selected_video_display_label.setText(video_base)
        self.selected_audio_display_label.setText(audio_base)

        # Enable Add button only if one video AND one audio are selected
        can_add = self.current_selected_video_path is not None and self.current_selected_audio_path is not None
        self.add_pair_button.setEnabled(can_add)

    def _populate_list_widget(self, list_widget, directory, file_types, get_metadata_func):
        """Populates a list widget with files from a directory."""
        list_widget.clear()
        if not os.path.isdir(directory):
            logging.warning(f"Input directory not found or not a directory: {directory}")
            # Optionally show a message to the user or disable the list
            list_widget.addItem("(Directory not found)") # Placeholder
            list_widget.setEnabled(False)
            return

        list_widget.setEnabled(True)
        count = 0
        try:
            for filename in os.listdir(directory):
                if filename.lower().endswith(file_types):
                    file_path = os.path.join(directory, filename)
                    metadata = get_metadata_func(file_path)
                    if metadata:
                        item_text = f"{filename} ({metadata})"
                        item = QListWidgetItem(item_text)
                        item.setData(Qt.ItemDataRole.UserRole, file_path) # Store full path
                        list_widget.addItem(item)
                        count += 1
        except Exception as e:
            logging.error(f"Error reading directory {directory}: {e}")

        if count == 0:
             list_widget.addItem("(No matching files found)")

    def _refresh_specific_list(self, list_key):
        """Refreshes a single specified input list based on its key."""
        logging.info(f"Refreshing list: {list_key}")
        base_input_dir = self.input_dir_input.text()
        if not base_input_dir or not os.path.isdir(base_input_dir):
            QMessageBox.warning(self, "Input Directory Error", "Please select a valid base Input Directory.")
            return

        subfolder = self.subfolder_names.get(list_key)
        if not subfolder:
            logging.error(f"Invalid list key provided for refresh: {list_key}")
            return

        target_dir = os.path.join(base_input_dir, subfolder)

        if not os.path.isdir(target_dir):
            logging.warning(f"Subdirectory not found for {list_key}: {target_dir}")
            list_widget = self.s1_video_list if list_key == 's1_vid' else self.s2_video_list if list_key == 's2_vid' else self.s1_audio_list if list_key == 's1_aud' else self.s2_audio_list
            list_widget.addItem("(Subdirectory Not Found)")
            # Create the directory?
            # try:
            #     os.makedirs(target_dir)
            #     logging.info(f"Created missing directory: {target_dir}")
            # except OSError as e:
            #     logging.error(f"Failed to create directory {target_dir}: {e}")
            #     list_widget.addItem("(Cannot Create Subdir)")
            #     return
            return # Don't proceed if dir doesn't exist

        # Determine if it's a video list for thumbnail generation
        is_video_list = list_key in ["s1_vid", "s2_vid"]
        thumbnail_size = QSize(48, 48) # Smaller thumbnail size

        # Initialize ffmpeg check flag if it doesn't exist
        if not hasattr(self, '_ffmpeg_checked'):
             self._ffmpeg_checked = True # Assume available until proven otherwise

        # Check if FFmpeg check has already failed
        if is_video_list and not self._ffmpeg_checked:
             logging.warning("Skipping thumbnail generation as FFmpeg was not found or failed previously.")

        try:
            files_to_process = sorted(os.listdir(target_dir))
            if not files_to_process:
                list_widget = self.s1_video_list if list_key == 's1_vid' else self.s2_video_list if list_key == 's2_vid' else self.s1_audio_list if list_key == 's1_aud' else self.s2_audio_list
                list_widget.addItem("(Directory is empty)")
                return

            items_added = 0
            for filename in files_to_process:
                full_path = os.path.join(target_dir, filename)
                if os.path.isfile(full_path):
                    # Check extension based on list type
                    allowed_extensions = []
                    if "_vid" in list_key:
                        allowed_extensions = VIDEO_EXTENSIONS + IMAGE_EXTENSIONS
                    elif "_aud" in list_key:
                        allowed_extensions = AUDIO_EXTENSIONS
                    else: # Should not happen with current names, but good practice
                        continue

                    if any(filename.lower().endswith(ext) for ext in allowed_extensions):
                        # Get duration first
                        duration_str = self._get_media_duration(full_path)
                        display_text = f"[{duration_str}] {filename}"

                        item = QListWidgetItem(display_text)
                        item.setData(Qt.ItemDataRole.UserRole, full_path) # Store full path

                        # Generate and set thumbnail IF it's a video list AND FFmpeg seems available
                        if is_video_list and self._ffmpeg_checked:
                            ext = os.path.splitext(full_path)[1].lower()
                            if ext in VIDEO_EXTENSIONS:
                                icon = self._generate_video_thumbnail(full_path, thumbnail_size)
                                if icon:
                                    item.setIcon(icon)
                            elif ext in IMAGE_EXTENSIONS:
                                pixmap = QPixmap(full_path)
                                icon = QIcon(pixmap.scaled(thumbnail_size, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                                item.setIcon(icon)
                            # If icon generation fails (icon is None), simply don't set one.

                        list_widget = self.s1_video_list if list_key == 's1_vid' else self.s2_video_list if list_key == 's2_vid' else self.s1_audio_list if list_key == 's1_aud' else self.s2_audio_list
                        list_widget.addItem(item)
                        items_added += 1

            if items_added == 0:
                 list_widget = self.s1_video_list if list_key == 's1_vid' else self.s2_video_list if list_key == 's2_vid' else self.s1_audio_list if list_key == 's1_aud' else self.s2_audio_list
                 list_widget.addItem("(No matching files found)")

        except OSError as e:
            logging.error(f"Error reading directory {target_dir}: {e}")
            list_widget = self.s1_video_list if list_key == 's1_vid' else self.s2_video_list if list_key == 's2_vid' else self.s1_audio_list if list_key == 's1_aud' else self.s2_audio_list
            list_widget.addItem(f"Error reading directory: {e}")

    def _start_processing(self):
        logging.debug("Entering _start_processing")
        comfyui_url = self.comfyui_url_edit.text()
        workflow_path = self.workflow_path_edit.text()
        output_dir = self.output_dir_input.text()
        comfyui_output_base_dir = self.comfyui_output_base_dir_edit.text()

        if not all([comfyui_url, workflow_path, output_dir]):
             QMessageBox.critical(self, "Error", "Please configure ComfyUI URL, Workflow Path, and Output Directory first.")
             return
        if not comfyui_output_base_dir or not os.path.isdir(comfyui_output_base_dir):
            QMessageBox.critical(self, "Error", "Please configure a valid ComfyUI Output Directory.")
            return

        if not self.processing_queue:
            QMessageBox.warning(self, "Queue Empty", "Please add video/audio pairs to the queue before starting.")
            return
        
        self.set_ui_enabled(False) 
        self.status_bar.showMessage("Starting batch processing...")
        self.all_intermediate_files.clear() 

        config = {
            'comfyui_url': comfyui_url,
            'workflow_path': workflow_path,
            'framepack_workflow_path': self.framepack_workflow_path_edit.text(),
            'output_dir': output_dir,
            'comfyui_output_base_dir': comfyui_output_base_dir,
            'seed': self.seed_input.value(),
            'lips_expression': self.lips_expr_input.value(),
            'inference_steps': self.inference_steps_input.value(),
            'crf': self.crf_input.value(),
            'silent_padding_sec': self.silent_padding_input.value(),
            'framepack_prompt': self.framepack_prompt_input.toPlainText(),
            'framepack_seed': self.framepack_seed_input.value(),
            'framepack_random': self.framepack_random_checkbox.isChecked(),
            'zonos_api_key': self.zonos_api_key_input.text().strip(),
            'tts_output_dir': self.tts_output_dir_input.text().strip() or None,
        }

        self.batch_worker = BatchProcessorWorker(
            processing_queue=self.processing_queue,
            config=config
        )
        
        self.batch_worker.signals.progress.connect(self._update_batch_progress)
        self.batch_worker.signals.error.connect(self._handle_batch_error)
        self.batch_worker.signals.pair_finished.connect(self._handle_pair_finished)
        self.batch_worker.signals.all_finished.connect(self._handle_batch_finished)
        
        self.threadpool.start(self.batch_worker)
        logging.info(f"Started BatchProcessorWorker for {len(self.processing_queue)} pairs.")

    def set_ui_enabled(self, enabled: bool):
        """Enable or disable UI elements that shouldn't be used during processing."""
        # Disable the config tab itself
        self.queue_config_log_dock.setEnabled(enabled) 
        # Disable file list refresh and selection
        self.s1_video_list.setEnabled(enabled)
        self.s2_video_list.setEnabled(enabled)
        self.s1_audio_list.setEnabled(enabled)
        self.s2_audio_list.setEnabled(enabled)
        # Find the refresh button within the file_lists_panel (assuming it's the only button there)
        # This might need adjustment if the structure changes
        file_list_panel_children = self.media_dock.findChildren(QPushButton)
        if file_list_panel_children:
            file_list_panel_children[0].setEnabled(enabled) # Assuming refresh is the first/only button

        # Disable queue modification buttons
        self.add_to_queue_button.setEnabled(enabled)
        self.clear_queue_button.setEnabled(enabled)
        
        # Disable/Enable the main process button (logic might be reversed for this one)
        self.process_button.setEnabled(enabled) 
        # Optionally, change text/style based on state
        if not enabled:
            self.process_button.setText("Processing...")
            # Add a cancel button or similar logic here if needed
        else:
            self.process_button.setText("Start Processing Queue")

    def _update_batch_progress(self, message):
        self.status_bar.showMessage(message)
        logging.debug(f"Progress update: {message}")

    def _handle_batch_error(self, error_message):
        logging.error(f"Batch Worker Error: {error_message}")

    def _handle_pair_finished(self, pair_index, success, intermediate_files):
        item = self.queue_list_widget.item(pair_index)
        if item:
            if success:
                item.setForeground(Qt.GlobalColor.darkGreen) 
                item.setText(f"[DONE] {item.text()}") 
                logging.info(f"Pair {pair_index+1} finished successfully.")
            else:
                item.setForeground(Qt.GlobalColor.red) 
                item.setText(f"[FAILED] {item.text()}") 
                logging.warning(f"Pair {pair_index+1} failed.")
        QApplication.processEvents() 

    def _handle_batch_finished(self, all_intermediate_files, any_errors):
        logging.info(f"Batch finished. Errors occurred: {any_errors}. Found {len(all_intermediate_files)} intermediate files.")
        self.status_bar.showMessage("Batch processing finished.")

        final_output_path = None
        if not all_intermediate_files:
            if any_errors:
                 QMessageBox.warning(self, "Batch Complete", "Batch processing finished, but errors occurred and no output files were generated for concatenation.")
            else:
                 QMessageBox.information(self, "Batch Complete", "Batch processing finished, but no output files were generated by ComfyUI for concatenation.")
        else:
            first_video_path, first_audio_path, _ = self.processing_queue[0] 
            video_basename = Path(first_video_path).stem
            audio_basename = Path(first_audio_path).stem
            output_dir = self.output_dir_input.text() 
            final_output_filename = f"final_BATCH_{video_basename}_{audio_basename}.mp4"
            final_output_path = os.path.join(output_dir, final_output_filename)

            self.status_bar.showMessage(f"Concatenating {len(all_intermediate_files)} files...")
            QApplication.processEvents()
            logging.info(f"Starting final FFmpeg concatenation for {len(all_intermediate_files)} files -> {final_output_path}")
            
            ffmpeg_success = run_ffmpeg_concat(all_intermediate_files, final_output_path)
            
            if ffmpeg_success:
                 QMessageBox.information(self, "Success", f"Batch processing and concatenation complete!\nFinal video saved to:\n{final_output_path}")
                 self.status_bar.showMessage("Batch completed successfully!")
            else:
                 QMessageBox.critical(self, "FFmpeg Error", f"Batch processing finished, but FFmpeg concatenation failed.\nCheck logs for details. Attempted output:\n{final_output_path}")
                 self.status_bar.showMessage("Batch completed with concatenation errors.")
        
        self.set_ui_enabled(True) 
        self.batch_worker = None 

    def _on_media_item_clicked(self, item): # Rename back to original
        """Handles single-click: plays the selected media file."""
        list_widget = self.sender()
        file_path = item.data(Qt.ItemDataRole.UserRole) # Get the full path
        if file_path and os.path.exists(file_path):
            logging.info(f"Attempting to play: {file_path}")
            try:
                self.media_player.setSource(QUrl.fromLocalFile(file_path))
                self.media_player.play()
                self.status_bar.showMessage(f"Playing: {os.path.basename(file_path)}")
            except Exception as e:
                logging.error(f"Error setting media source or playing {file_path}: {e}", exc_info=True)
                QMessageBox.warning(self, "Playback Error", f"Could not play file: {file_path}\nError: {e}")
        elif file_path:
            logging.warning(f"File path from item data does not exist: {file_path}")
            self.status_bar.showMessage(f"File not found: {os.path.basename(file_path)}")
        else:
             logging.warning("_on_media_item_clicked: No file path associated with item.")

    def _handle_media_error(self, error, error_string=""):
        """Handles errors reported by the QMediaPlayer."""
        # Use the provided error_string if available, otherwise get from player
        error_msg = error_string if error_string else self.media_player.errorString()
        log_msg = f"Media player error: {error} - {error_msg}"
        if self.media_player.source().isValid():
            log_msg += f" (Source: {self.media_player.source().toLocalFile()})"

        logging.error(log_msg)
        self.status_bar.showMessage(f"Media Error: {error_msg}")
        # Optional: Show a message box to the user
        # QMessageBox.warning(self, "Media Player Error", f"An error occurred during playback: {error_msg}")


    def _add_selected_pair_to_queue(self):
        """Adds the currently selected video and audio files to the queue list widget and internal queue."""
        if not self.current_selected_video_path or not self.current_selected_audio_path:
            QMessageBox.warning(self, "Selection Missing", "Please select both a video and an audio file first.")
            return

        video_path = self.current_selected_video_path
        audio_path = self.current_selected_audio_path
        pair = (video_path, audio_path, None)

        # Check for duplicates in the internal queue
        if pair in self.processing_queue:
            QMessageBox.information(self, "Duplicate Pair", "This video/audio pair is already in the queue.")
            return

        # Add to internal queue
        self.processing_queue.append(pair)
        logging.info(f"Added to internal queue: {os.path.basename(video_path)} / {os.path.basename(audio_path)}")

        # Add to list widget
        video_name = os.path.basename(video_path)
        audio_name = os.path.basename(audio_path)
        item_text = f"V: {video_name}\nA: {audio_name}" # New two-line format
        item = QListWidgetItem(item_text)
        item.setToolTip(f"Video: {video_path}\nAudio: {audio_path}")
        item.setData(Qt.ItemDataRole.UserRole, pair) # Store the pair data
        self.queue_list_widget.addItem(item)
        logging.info(f"Added to queue list widget: {item_text}") # Maybe too verbose
        self.process_button.setEnabled(True)

        # Optionally clear the selection after adding
        self._clear_current_selection()
        self.status_bar.showMessage(f"Pair added to queue ({len(self.processing_queue)} total).")

    def _clear_queue(self):
        """Clears the entire processing queue (both internal list and UI list)."""
        reply = QMessageBox.question(self, "Confirm Clear",
                                   "Are you sure you want to clear the entire processing queue?",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            self.processing_queue.clear()
            self.queue_list_widget.clear()
            self.status_bar.showMessage("Queue cleared.")
            logging.info("Processing queue cleared.")
            self.process_button.setEnabled(False) # Disable process button if queue is empty
            # Consider resetting any batch progress UI elements if needed

    # --- New Method to Remove Selected Item ---
    def _remove_selected_queue_item(self):
        """Removes the selected item from the processing queue."""
        selected_items = self.queue_list_widget.selectedItems()
        if not selected_items:
            self.status_bar.showMessage("No item selected in the queue to remove.")
            return

        # Assuming single selection mode for the queue list
        item_to_remove = selected_items[0]
        pair_to_remove = item_to_remove.data(Qt.ItemDataRole.UserRole)

        if pair_to_remove:
            try:
                # Remove from internal list
                if pair_to_remove in self.processing_queue:
                    self.processing_queue.remove(pair_to_remove)
                    logging.info(f"Removed pair from internal queue: {pair_to_remove}")

                    # Remove from list widget
                    row = self.queue_list_widget.row(item_to_remove)
                    self.queue_list_widget.takeItem(row)
                    logging.info(f"Removed item from queue widget: {item_to_remove.text()}")
                    self.status_bar.showMessage(f"Removed selected pair. ({len(self.processing_queue)} pairs remaining).")

                    # Disable process button if queue becomes empty
                    self.process_button.setEnabled(len(self.processing_queue) > 0)

                else:
                    logging.warning(f"Pair {pair_to_remove} found in list widget but not in internal queue. Removing from widget only.")
                    # Remove from list widget even if not found internally (consistency)
                    row = self.queue_list_widget.row(item_to_remove)
                    self.queue_list_widget.takeItem(row)
                    self.status_bar.showMessage("Removed inconsistent item from queue display.")

            except Exception as e:
                logging.error(f"Error removing queue item: {e}", exc_info=True)
                QMessageBox.critical(self, "Error", f"An error occurred while removing the item: {e}")
        else:
            logging.warning("Selected queue item has no associated pair data. Cannot remove internal pair.")
            # Optionally remove the item from the list widget anyway?
            # row = self.queue_list_widget.row(item_to_remove)
            # self.queue_list_widget.takeItem(row)
            # self.status_bar.showMessage("Removed item with missing data from queue display.")
            QMessageBox.warning(self, "Removal Error", "Selected item has missing data. Cannot guarantee removal from internal processing list.")

    # ----------------------------------------

    # --- Helper function to extract numeric index from audio filenames ---
    def _extract_audio_index(self, filename):
        """Extracts the numeric index (e.g., 000, 001, 5, 123) from an audio filename.
           Assumes format like 'SpeakerName_NNN...'"""
        # Look for start of string, any letters (speaker name), underscore, digits
        # match = re.search(r'(?:speaker[12]|s[12])[_-](\d+)', os.path.basename(filename), re.IGNORECASE) # Old regex
        match = re.search(r'^[a-zA-Z]+[_](\d+)', os.path.basename(filename)) # New regex
        if match:
            try:
                index = int(match.group(1))
                logging.debug(f"Extracted index {index} from {os.path.basename(filename)}")
                return index
            except (ValueError, TypeError):
                logging.warning(f"Could not convert extracted index '{match.group(1)}' to int for {os.path.basename(filename)}")
                return float('inf') # Treat non-numeric as last if conversion fails
        logging.debug(f"Could not find index pattern in {os.path.basename(filename)}")
        return float('inf') # Files without a clear number sequence sort last

    # --- Auto-Pairing Logic ---
    def _auto_pair_all(self):
        """Automatically pairs audio and video files based on speaker and sequence."""
        logging.info("Starting auto-pairing process...")
        self.status_bar.showMessage("Auto-pairing in progress...")

        # 1. Gather all file paths from list widgets
        def get_paths_from_list(list_widget):
            paths = []
            for i in range(list_widget.count()):
                item = list_widget.item(i)
                path = item.data(Qt.ItemDataRole.UserRole)
                if path and os.path.exists(path):
                    paths.append(path)
                else:
                    logging.warning(f"Skipping invalid/non-existent path in {list_widget.objectName()}: {path}")
            return paths

        s1_videos = get_paths_from_list(self.s1_video_list)
        s2_videos = get_paths_from_list(self.s2_video_list)
        s1_audios = get_paths_from_list(self.s1_audio_list)
        s2_audios = get_paths_from_list(self.s2_audio_list)

        logging.debug(f"Found S1 Videos: {s1_videos}")
        logging.debug(f"Found S2 Videos: {s2_videos}")
        logging.debug(f"Found S1 Audios: {s1_audios}")
        logging.debug(f"Found S2 Audios: {s2_audios}")

        if not s1_audios and not s2_audios:
            QMessageBox.information(self, "Auto-Pairing", "No audio files found in the lists to pair.")
            self.status_bar.showMessage("Auto-pairing failed: No audio files.")
            logging.info("Auto-pairing stopped: No audio files found.") # Changed log level
            return

        # Read advanced pairing settings
        use_duration = getattr(self, 'auto_pair_duration_checkbox', None) and self.auto_pair_duration_checkbox.isChecked()
        regex_pattern = getattr(self, 'auto_pair_regex_input', None).text().strip() if getattr(self, 'auto_pair_regex_input', None) else ''
        regex_compiled = None
        if regex_pattern:
            try:
                regex_compiled = re.compile(regex_pattern)
            except re.error as e:
                QMessageBox.warning(self, "Regex Error", f"Invalid regex: {e}")
                regex_compiled = None
        # Advanced interleaved pairing
        s1_audios.sort(key=self._extract_audio_index)
        s2_audios.sort(key=self._extract_audio_index)
        s1_audio_map = {self._extract_audio_index(f): f for f in s1_audios if self._extract_audio_index(f) != float('inf')}
        s2_audio_map = {self._extract_audio_index(f): f for f in s2_audios if self._extract_audio_index(f) != float('inf')}
        all_indices = sorted(set(s1_audio_map.keys()) | set(s2_audio_map.keys()))
        audio_sequence = []
        for idx in all_indices:
            a1 = s1_audio_map.get(idx)
            if a1:
                audio_sequence.append(('s1', a1))
            a2 = s2_audio_map.get(idx)
            if a2:
                audio_sequence.append(('s2', a2))
        pairs_added_count = 0
        if regex_compiled:
            for speaker, audio in audio_sequence:
                videos = s1_videos if speaker == 's1' else s2_videos
                m = regex_compiled.search(os.path.basename(audio))
                if not m:
                    continue
                key = m.group(1)
                candidates = [v for v in videos if (lambda mm: mm and mm.group(1) == key)(regex_compiled.search(os.path.basename(v)))]
                if candidates and self._add_pair_to_queue_internal(candidates[0], audio):
                    pairs_added_count += 1
            self._finalize_auto_pair(pairs_added_count)
            return
        if use_duration:
            video_infos_s1 = [(v, self._get_duration_seconds(v)) for v in s1_videos]
            video_infos_s2 = [(v, self._get_duration_seconds(v)) for v in s2_videos]
            for speaker, audio in audio_sequence:
                a_dur = self._get_duration_seconds(audio)
                if a_dur is None:
                    continue
                infos = video_infos_s1 if speaker == 's1' else video_infos_s2
                best_video = None
                best_diff = float('inf')
                for v, v_dur in infos:
                    if v_dur is None:
                        continue
                    diff = abs(v_dur - a_dur)
                    if diff < best_diff:
                        best_diff = diff
                        best_video = v
                if best_video and self._add_pair_to_queue_internal(best_video, audio):
                    pairs_added_count += 1
            self._finalize_auto_pair(pairs_added_count)
            return
        # Fallback round-robin interleaved pairing
        video_idx = {'s1': 0, 's2': 0}
        for speaker, audio in audio_sequence:
            vids = s1_videos if speaker == 's1' else s2_videos
            if not vids:
                logging.warning(f"No videos for {speaker} to pair with {os.path.basename(audio)}")
                continue
            idx = video_idx[speaker] % len(vids)
            v_path = vids[idx]
            video_idx[speaker] += 1
            if self._add_pair_to_queue_internal(v_path, audio):
                pairs_added_count += 1
        self._finalize_auto_pair(pairs_added_count)
        return

    def _finalize_auto_pair(self, pairs_added_count):
        if pairs_added_count > 0:
            self.status_bar.showMessage(f"Auto-pairing added {pairs_added_count} pairs to the queue.")
            logging.info(f"Auto-pairing added {pairs_added_count} pairs.")
            self.process_button.setEnabled(True)
        else:
            self.status_bar.showMessage("Auto-pairing finished. No new pairs were added.")
            logging.info("Auto-pairing finished, no pairs added.")

    def _get_duration_seconds(self, file_path):
        """Return media duration in seconds using ffprobe, or None on error."""
        try:
            result = subprocess.run([
                'ffprobe', '-v', 'error', '-select_streams', 'v:0',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                file_path
            ], capture_output=True, text=True, check=True)
            return float(result.stdout.strip())
        except Exception as e:
            logging.warning(f"Could not get duration for {os.path.basename(file_path)}: {e}")
            return None

    def _pair_by_duration(self, videos, audios):
        """Pair audio to the video with closest duration."""
        count = 0
        video_infos = [(v, self._get_duration_seconds(v)) for v in videos]
        for audio in audios:
            a_dur = self._get_duration_seconds(audio)
            if a_dur is None:
                continue
            best_video = None
            best_diff = float('inf')
            for v, v_dur in video_infos:
                if v_dur is None:
                    continue
                diff = abs(v_dur - a_dur)
                if diff < best_diff:
                    best_diff = diff
                    best_video = v
            if best_video and self._add_pair_to_queue_internal(best_video, audio):
                count += 1
        return count

    def _pair_by_regex(self, videos, audios, regex):
        """Pair files by matching filename regex group(1)."""
        count = 0
        groups = {}
        # Map videos by key
        for v in videos:
            m = regex.search(os.path.basename(v))
            if m:
                key = m.group(1)
                groups.setdefault(key, []).append(v)
        # Pair each audio with corresponding video list
        for audio in audios:
            m = regex.search(os.path.basename(audio))
            if not m:
                continue
            key = m.group(1)
            vids = groups.get(key)
            if not vids:
                continue
            selected_v = vids[count % len(vids)]
            if self._add_pair_to_queue_internal(selected_v, audio):
                count += 1
        return count

    def _finalize_auto_pair(self, pairs_added_count):
        if pairs_added_count > 0:
            self.status_bar.showMessage(f"Auto-pairing added {pairs_added_count} pairs to the queue.")
            logging.info(f"Auto-pairing added {pairs_added_count} pairs.")
            self.process_button.setEnabled(True)
        else:
            self.status_bar.showMessage("Auto-pairing finished. No new pairs were added.")
            logging.info("Auto-pairing finished, no pairs added.")

    def _auto_pair_all(self):
        """Automatically pairs audio and video files based on speaker and sequence."""
        logging.info("Starting auto-pairing process...")
        self.status_bar.showMessage("Auto-pairing in progress...")

        # 1. Gather all file paths from list widgets
        def get_paths_from_list(list_widget):
            paths = []
            for i in range(list_widget.count()):
                item = list_widget.item(i)
                path = item.data(Qt.ItemDataRole.UserRole)
                if path and os.path.exists(path):
                    paths.append(path)
                else:
                    logging.warning(f"Skipping invalid/non-existent path in {list_widget.objectName()}: {path}")
            return paths

        s1_videos = get_paths_from_list(self.s1_video_list)
        s2_videos = get_paths_from_list(self.s2_video_list)
        s1_audios = get_paths_from_list(self.s1_audio_list)
        s2_audios = get_paths_from_list(self.s2_audio_list)

        logging.debug(f"Found S1 Videos: {s1_videos}")
        logging.debug(f"Found S2 Videos: {s2_videos}")
        logging.debug(f"Found S1 Audios: {s1_audios}")
        logging.debug(f"Found S2 Audios: {s2_audios}")

        if not s1_audios and not s2_audios:
            QMessageBox.information(self, "Auto-Pairing", "No audio files found in the lists to pair.")
            self.status_bar.showMessage("Auto-pairing failed: No audio files.")
            logging.info("Auto-pairing stopped: No audio files found.") # Changed log level
            return

        # Read advanced pairing settings
        use_duration = getattr(self, 'auto_pair_duration_checkbox', None) and self.auto_pair_duration_checkbox.isChecked()
        regex_pattern = getattr(self, 'auto_pair_regex_input', None).text().strip() if getattr(self, 'auto_pair_regex_input', None) else ''
        regex_compiled = None
        if regex_pattern:
            try:
                regex_compiled = re.compile(regex_pattern)
            except re.error as e:
                QMessageBox.warning(self, "Regex Error", f"Invalid regex: {e}")
                regex_compiled = None
        # Advanced interleaved pairing
        s1_audios.sort(key=self._extract_audio_index)
        s2_audios.sort(key=self._extract_audio_index)
        s1_audio_map = {self._extract_audio_index(f): f for f in s1_audios if self._extract_audio_index(f) != float('inf')}
        s2_audio_map = {self._extract_audio_index(f): f for f in s2_audios if self._extract_audio_index(f) != float('inf')}
        all_indices = sorted(set(s1_audio_map.keys()) | set(s2_audio_map.keys()))
        audio_sequence = []
        for idx in all_indices:
            a1 = s1_audio_map.get(idx)
            if a1:
                audio_sequence.append(('s1', a1))
            a2 = s2_audio_map.get(idx)
            if a2:
                audio_sequence.append(('s2', a2))
        pairs_added_count = 0
        if regex_compiled:
            for speaker, audio in audio_sequence:
                videos = s1_videos if speaker == 's1' else s2_videos
                m = regex_compiled.search(os.path.basename(audio))
                if not m:
                    continue
                key = m.group(1)
                candidates = [v for v in videos if (lambda mm: mm and mm.group(1) == key)(regex_compiled.search(os.path.basename(v)))]
                if candidates and self._add_pair_to_queue_internal(candidates[0], audio):
                    pairs_added_count += 1
            self._finalize_auto_pair(pairs_added_count)
            return
        if use_duration:
            video_infos_s1 = [(v, self._get_duration_seconds(v)) for v in s1_videos]
            video_infos_s2 = [(v, self._get_duration_seconds(v)) for v in s2_videos]
            for speaker, audio in audio_sequence:
                a_dur = self._get_duration_seconds(audio)
                if a_dur is None:
                    continue
                infos = video_infos_s1 if speaker == 's1' else video_infos_s2
                best_video = None
                best_diff = float('inf')
                for v, v_dur in infos:
                    if v_dur is None:
                        continue
                    diff = abs(v_dur - a_dur)
                    if diff < best_diff:
                        best_diff = diff
                        best_video = v
                if best_video and self._add_pair_to_queue_internal(best_video, audio):
                    pairs_added_count += 1
            self._finalize_auto_pair(pairs_added_count)
            return
        # Fallback round-robin interleaved pairing
        video_idx = {'s1': 0, 's2': 0}
        for speaker, audio in audio_sequence:
            vids = s1_videos if speaker == 's1' else s2_videos
            if not vids:
                logging.warning(f"No videos for {speaker} to pair with {os.path.basename(audio)}")
                continue
            idx = video_idx[speaker] % len(vids)
            v_path = vids[idx]
            video_idx[speaker] += 1
            if self._add_pair_to_queue_internal(v_path, audio):
                pairs_added_count += 1
        self._finalize_auto_pair(pairs_added_count)
        return

    def _add_pair_to_queue_internal(self, video_path, audio_path):
        """Internal helper to add a given pair to the queue, checking for duplicates."""
        pair = (video_path, audio_path, None)

        # Check for duplicates in the internal queue first
        if pair in self.processing_queue:
            logging.warning(f"Skipping duplicate pair during auto-pairing: {os.path.basename(video_path)} / {os.path.basename(audio_path)}")
            return False # Indicate pair was not added

        # Add to internal queue
        self.processing_queue.append(pair)

        # Add to list widget
        video_name = os.path.basename(video_path)
        audio_name = os.path.basename(audio_path)
        item_text = f"V: {video_name}\nA: {audio_name}" # Two-line format
        item = QListWidgetItem(item_text)
        item.setToolTip(f"Video: {video_path}\nAudio: {audio_path}")
        item.setData(Qt.ItemDataRole.UserRole, pair) # Store the pair data
        self.queue_list_widget.addItem(item)
        logging.debug(f"Auto-added to queue: {item_text}") # Maybe too verbose
        self.process_button.setEnabled(True)
        return True # Indicate pair was added

    def _refresh_list(self, list_widget: QListWidget, list_name: str):
        """Refreshes the contents of a specific QListWidget based on its name."""
        logging.info(f"Refreshing list: {list_name}")
        list_widget.clear()
        input_dir = self.input_dir_input.text()
        if not input_dir or not os.path.isdir(input_dir):
            logging.warning(f"Directory not found or invalid for {list_name}: {input_dir}")
            list_widget.addItem(f"Set '{list_name}_dir' in config")
            return

        subfolder = self.subfolder_names.get(list_name)
        if not subfolder:
            logging.error(f"Invalid list key provided for refresh: {list_name}")
            list_widget.addItem("(Internal Error: Bad List Name)")
            return

        target_dir = os.path.join(input_dir, subfolder)

        if not os.path.isdir(target_dir):
            logging.warning(f"Subdirectory not found for {list_name}: {target_dir}")
            list_widget.addItem("(Subdirectory Not Found)")
            # Create the directory?
            # try:
            #     os.makedirs(target_dir)
            #     logging.info(f"Created missing directory: {target_dir}")
            # except OSError as e:
            #     logging.error(f"Failed to create directory {target_dir}: {e}")
            #     list_widget.addItem("(Cannot Create Subdir)")
            #     return
            return # Don't proceed if dir doesn't exist

        # Determine if it's a video list for thumbnail generation
        is_video_list = list_name in ["s1_vid", "s2_vid"]
        thumbnail_size = QSize(48, 48) # Smaller thumbnail size

        # Initialize ffmpeg check flag if it doesn't exist
        if not hasattr(self, '_ffmpeg_checked'):
             self._ffmpeg_checked = True # Assume available until proven otherwise

        # Check if FFmpeg check has already failed
        if is_video_list and not self._ffmpeg_checked:
             logging.warning("Skipping thumbnail generation as FFmpeg was not found or failed previously.")

        try:
            files_to_process = sorted(os.listdir(target_dir))
            if not files_to_process:
                list_widget.addItem("(Directory is empty)")
                return

            items_added = 0
            for filename in files_to_process:
                full_path = os.path.join(target_dir, filename)
                if os.path.isfile(full_path):
                    # Check extension based on list type
                    allowed_extensions = []
                    if "_vid" in list_name:
                        allowed_extensions = VIDEO_EXTENSIONS + IMAGE_EXTENSIONS
                    elif "_aud" in list_name:
                        allowed_extensions = AUDIO_EXTENSIONS
                    else: # Should not happen with current names, but good practice
                        continue

                    if any(filename.lower().endswith(ext) for ext in allowed_extensions):
                        # Get duration first
                        duration_str = self._get_media_duration(full_path)
                        display_text = f"[{duration_str}] {filename}"

                        item = QListWidgetItem(display_text)
                        item.setData(Qt.ItemDataRole.UserRole, full_path) # Store full path

                        # Generate and set thumbnail IF it's a video list AND FFmpeg seems available
                        if is_video_list and self._ffmpeg_checked:
                            ext = os.path.splitext(full_path)[1].lower()
                            if ext in VIDEO_EXTENSIONS:
                                icon = self._generate_video_thumbnail(full_path, thumbnail_size)
                                if icon:
                                    item.setIcon(icon)
                            elif ext in IMAGE_EXTENSIONS:
                                pixmap = QPixmap(full_path)
                                icon = QIcon(pixmap.scaled(thumbnail_size, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                                item.setIcon(icon)
                            # If icon generation fails (icon is None), simply don't set one.

                        list_widget.addItem(item)
                        items_added += 1

            if items_added == 0:
                 list_widget.addItem("(No matching files found)")

        except OSError as e:
            logging.error(f"Error reading directory {target_dir}: {e}")
            list_widget.addItem(f"Error reading directory: {e}")

    @Slot(str)
    def _append_log_message(self, message):
        self.log_text_edit.append(message)
        # Optional: Auto-scroll to the bottom
        # self.log_text_edit.verticalScrollBar().setValue(self.log_text_edit.verticalScrollBar().maximum())

    def _clear_current_selection(self):
        """Resets the currently selected video and audio paths."""
        self.current_selected_video_path = None
        self.current_selected_audio_path = None
        # Optionally clear labels or visual indicators if they exist
        # self.selected_video_label.setText("Selected Video: None")
        # self.selected_audio_label.setText("Selected Audio: None")
        logging.debug("Cleared current video/audio selection.")

    # --- Queue reorder/save/load methods ---
    def _on_queue_reordered(self, parent, start, end, destination, row):
        """Update internal queue after user reorders via drag & drop."""
        self.processing_queue = [
            self.queue_list_widget.item(i).data(Qt.ItemDataRole.UserRole)
            for i in range(self.queue_list_widget.count())
        ]
        self.status_bar.showMessage(f"Queue reordered ({len(self.processing_queue)} items)", 2000)
        logging.info(f"Queue reordered: {self.processing_queue}")

    def _save_queue(self):
        """Save current processing queue to a JSON file."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Queue List", "", "JSON Files (*.json)")
        if file_path:
            try:
                with open(file_path, "w") as f:
                    json.dump(self.processing_queue, f, indent=2)
                self.status_bar.showMessage(f"Queue saved to {file_path}", 2000)
                logging.info(f"Queue saved to {file_path}")
            except Exception as e:
                logging.error(f"Error saving queue: {e}")
                QMessageBox.critical(self, "Save Error", f"Could not save queue: {e}")

    def _load_queue(self):
        """Load processing queue from a JSON file."""
        file_path, _ = QFileDialog.getOpenFileName(self, "Load Queue List", "", "JSON Files (*.json)")
        if file_path:
            try:
                with open(file_path, "r") as f:
                    loaded_queue = json.load(f)
                self.processing_queue.clear()
                self.queue_list_widget.clear()
                for pair in loaded_queue:
                    if isinstance(pair, (list, tuple)) and len(pair) == 3:
                        self.processing_queue.append(tuple(pair))
                        video_name = os.path.basename(pair[0])
                        audio_name = os.path.basename(pair[1])
                        item_text = f"V: {video_name}\nA: {audio_name}"
                        item = QListWidgetItem(item_text)
                        item.setToolTip(f"Video: {pair[0]}\nAudio: {pair[1]}")
                        item.setData(Qt.ItemDataRole.UserRole, tuple(pair))
                        self.queue_list_widget.addItem(item)
                self.status_bar.showMessage(f"Queue loaded ({len(self.processing_queue)} items)", 2000)
                logging.info(f"Queue loaded from {file_path}")
                self.process_button.setEnabled(bool(self.processing_queue))
            except Exception as e:
                logging.error(f"Error loading queue: {e}")
                QMessageBox.critical(self, "Load Error", f"Could not load queue: {e}")

    def _show_queue_item_context_menu(self, pos):
        item = self.queue_list_widget.itemAt(pos)
        if not item:
            return
        menu = QMenu(self.queue_list_widget)
        trim_act = QAction("Trim", self.queue_list_widget)
        trim_act.triggered.connect(lambda: self._trim_queue_item(item))
        menu.addAction(trim_act)
        menu.exec(self.queue_list_widget.viewport().mapToGlobal(pos))

    def _trim_queue_item(self, item):
        """Launch trim dialog and store trim times on the queue item."""
        pair = item.data(Qt.ItemDataRole.UserRole)
        if not pair:
            return
        # Determine if trimming audio segment or video
        if len(pair) == 2:
            img, wav = pair
            dialog = AudioTrimDialog(self, wav)
        else:
            video_path, audio_path, _ = pair
            dialog = TrimDialog(self, video_path)
        if dialog.exec() == QDialog.Accepted:
            start, end = dialog.get_times()
            # Update pair with trim times
            if len(pair) == 2:
                new_pair = (img, wav, (start, end))
            else:
                new_pair = (video_path, audio_path, (start, end))
            item.setData(Qt.ItemDataRole.UserRole, new_pair)
            tip = item.toolTip() or ''
            item.setToolTip(f"{tip}\nTrim: {start:.3f}s - {end:.3f}s")
            idx = self.queue_list_widget.row(item)
            if 0 <= idx < len(self.processing_queue):
                self.processing_queue[idx] = new_pair

    def browse_image_folder(self):
        current_item = self.speaker_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "No Speaker Selected", "Please select a speaker first.")
            return
        speaker = current_item.text()
        dir_path = QFileDialog.getExistingDirectory(self, f"Select Image Folder for {speaker}")
        if dir_path:
            self.image_folder_input.setText(dir_path)
            self.speaker_image_folders[speaker] = dir_path

    def analyze_script(self):
        text = self.script_text.toPlainText()
        speakers = sorted(set(re.findall(r'\[([^,\]]+)', text)))
        self.speaker_list.clear()
        for spk in speakers:
            self.speaker_list.addItem(spk)
        self.status_bar.showMessage(f"Detected speakers: {', '.join(speakers)}")
        self.preview_voice_btn.setEnabled(bool(speakers))

    def preview_voice(self):
        item = self.speaker_list.currentItem()
        if not item:
            QMessageBox.warning(self, "Select Speaker", "Please select a speaker first.")
            return
        speaker = item.text()
        QMessageBox.information(self, "Preview Voice", f"(Would preview voice for {speaker})")

    def upload_voice_sample(self):
        path, _ = QFileDialog.getOpenFileName(self, "Upload Voice Sample", "", "Audio Files (*.wav *.mp3)")
        if path:
            self.voice_sample_path = path
            self.upload_voice_btn.setText(os.path.basename(path))

    def generate_podcast(self):
        script = self.script_text.toPlainText()
        api_key = self.zonos_api_key_input.text().strip()
        use_local = self.use_local_tts_checkbox.isChecked()
    
        logging.info(f"Local Zonos TTS checkbox state: {'Checked' if use_local else 'Unchecked'}")

        if not use_local and not api_key:
            QMessageBox.warning(self, "Missing API Key", 
                                "Zonos API key is required for API-based TTS. "
                                "Please enter it in the Configuration tab or check 'Use Local Zonos TTS'.")
            return

        # --- Auto-populate speaker_audio_files from VoiceManager ---
        self.speaker_audio_files.clear()
        assignments = voice_manager.voice_profiles.get("speaker_assignments", {})
        custom_voices = {cv["id"]: cv.get("file_path") for cv in voice_manager.voice_profiles.get("custom_voices", [])}
        for speaker, voice_id in assignments.items():
            # Only map if custom voice and file exists
            audio_path = custom_voices.get(voice_id)
            if audio_path and os.path.exists(audio_path):
                self.speaker_audio_files[speaker] = audio_path
        logging.debug(f"[AUTO-POPULATE] speaker_audio_files before TTS: {self.speaker_audio_files}")
        cfg = {
            "zonos_api_key": api_key, 
            "tts_output_dir": self.tts_output_dir_input.text().strip() or None,
            "speaker_audio_files_map": self.speaker_audio_files
        }
        
        self.queue_list_widget.clear()
        self.processing_queue.clear()
        
        wavs = run_tts_pipeline(script, cfg, use_local_tts=use_local)
        speech_wavs = [w for w in wavs if not w.endswith('_pause.wav')]
        if not speech_wavs:
            QMessageBox.warning(self, "No Speech Segments", "No speech segments found after TTS. Check your script or pause settings.")
            return
        # Pair images per speaker, cycling through each speaker's selected folder
        pairs = []
        image_iters = {}
        for wav in speech_wavs:
            base = os.path.basename(wav)
            name_no_ext, _ = os.path.splitext(base)
            parts = name_no_ext.split("_")
            speaker = parts[1] if len(parts) > 1 else None
            # Extract only the speaker name before any comma
            segments = name_no_ext.split("_", 1)
            speaker_raw = segments[1] if len(segments) > 1 else ""
            speaker = speaker_raw.split(",", 1)[0].strip()
            folder = self.speaker_image_folders.get(speaker)
            if not folder or not os.path.isdir(folder):
                logging.warning(f"No image folder set or invalid for speaker {speaker}")
                continue
            if speaker not in image_iters:
                images = sorted([f for f in glob.glob(os.path.join(folder, "*")) if os.path.splitext(f)[1].lower() in IMAGE_EXTENSIONS])
                if not images:
                    logging.warning(f"No images found in folder for speaker {speaker}: {folder}")
                    continue
                image_iters[speaker] = itertools.cycle(images)
            img = next(image_iters[speaker])
            pairs.append((img, wav))
        for img, wav in pairs:
            self._add_pair_to_queue_internal(img, wav)
        self.preview_full_audio_btn.setEnabled(True)

    def on_llm_provider_changed(self, provider):
        if provider == "Ollama":
            self.ollama_url_input.setEnabled(True)
            self.openrouter_url_input.setEnabled(False)
            self.openrouter_key_input.setEnabled(False)
        elif provider == "OpenRouter":
            self.ollama_url_input.setEnabled(False)
            self.openrouter_url_input.setEnabled(True)
            self.openrouter_key_input.setEnabled(True)

    def refresh_models(self):
        provider = self.llm_provider_combo.currentText()
        if provider == "Ollama":
            url = self.ollama_url_input.text()
            if not url or not url.startswith(("http://", "https://")):
                QMessageBox.warning(self, "Invalid URL", "Please enter a valid Ollama URL (including http:// or https://).")
                return
            url_base = url.rstrip("/")
            models = None
            try:
                resp = requests.get(f"{url_base}/api/tags", timeout=10)
                resp.raise_for_status()
                data = resp.json()
                if "models" not in data or not isinstance(data["models"], list):
                    raise ValueError(f"Unexpected response format: {data}")
                models = [m.get("name") for m in data["models"] if m.get("name")]
            except HTTPError as he:
                logging.error("HTTP error refreshing Ollama models: %s", he)
                QMessageBox.critical(self, "Model Refresh Error", f"HTTP error: {he}")
                return
            except Exception as e:
                logging.warning("HTTP API tags failed, will fallback to CLI: %s", e)
            # Fallback to Ollama CLI 'ollama list' if HTTP above failed
            if not models:
                try:
                    result = subprocess.run(["ollama", "list"], capture_output=True, text=True, check=True)
                    cli_lines = result.stdout.strip().splitlines()
                    models = [ln.split()[0] for ln in cli_lines if ln.strip()]
                    if not models:
                        raise ValueError("No models found via CLI")
                    logging.info("Loaded Ollama models via CLI fallback: %s", models)
                except Exception as e:
                    logging.error("Ollama CLI fallback failed: %s", e)
                    QMessageBox.critical(self, "Model Refresh Error", f"Failed to list models via ollama CLI: {e}")
                    return
            self.llm_model_combo.clear()
            self.llm_model_combo.addItems(models)
        elif provider == "OpenRouter":
            url = self.openrouter_url_input.text()
            key = self.openrouter_key_input.text()
            if not url.startswith(("http://", "https://")) or not key:
                QMessageBox.warning(self, "Invalid Configuration", "Please enter a valid OpenRouter URL (including http:// or https://) and API key.")
                return
            try:
                resp = requests.get(url + "/models", headers={"Authorization": f"Bearer {key}"}, timeout=10)
                resp.raise_for_status()
                models = resp.json()
                if not isinstance(models, list):
                    raise ValueError(f"Unexpected response format: {models}")
            except Exception as e:
                logging.error("Failed to refresh OpenRouter models: %s", e)
                QMessageBox.critical(self, "Model Refresh Error", f"Could not retrieve OpenRouter models: {e}")
                return
            self.llm_model_combo.clear()
            self.llm_model_combo.addItems(models)

    def handle_generate_script(self):
        # Gather inputs
        provider = self.llm_provider_combo.currentText()
        model = self.llm_model_combo.currentText()
        num_speakers = self.num_speakers_spin.value()
        speaker_names = [s.strip() for s in self.speaker_names_input.text().split(",") if s.strip()]
        subject = self.subject_input.text().strip()
        if not subject:
            QMessageBox.warning(self, "Missing Subject", "Please enter a subject for the script.")
            return
        # Build prompt
        prompt = (
            "You are an expert podcast script writer. Your SOLE TASK is to generate dialogue lines.\n"
            "EVERY dialogue line you output MUST strictly follow this exact format: "
            "[SPEAKER_NAME, emotion={...}, pitchStd=float_value, speed=integer_value]: Dialogue text.\n"
            "Pay EXTREME attention to the user's detailed rules for the 'emotion' object, especially regarding VALID emotion names and MANDATORY double-quoted keys.\n"
            f"Generate a podcast script for the subject '{subject}'. "
            f"The script must feature {num_speakers} speakers: {', '.join(speaker_names)}. "
            f"Follow these rules VERY STRICTLY:\n"
            f"1. EVERY line of dialogue MUST be in this exact format: [SPEAKER_NAME, emotion={{...}}, pitchStd=float_value, speed=integer_value]: Dialogue text\n"
            f"   Example 1 (complex emotion): [HOST, emotion={{\"happiness\":0.7,\"surprise\":0.3}}, pitchStd=40.0, speed=16]: Wow, that's truly amazing news!\n"
            f"   Example 2 (simple emotion): [GUEST, emotion={{\"sadness\":0.8}}, pitchStd=35.5, speed=12]: I'm still trying to process what happened.\n"
            f"   Example 3 (neutral): [NARRATOR, emotion={{\"neutral\":1.0}}, pitchStd=45.0, speed=14]: The journey continued the next day.\n"
            f"2. The 'emotion' parameter MUST be a JSON-like object string: emotion={{KEY:VALUE, ...}}. THIS IS A CRITICAL RULE.\n"
            f"   - RULE 2A: Emotion keys (e.g., \"happiness\") MUST ALWAYS be enclosed in DOUBLE QUOTES. Example: emotion={{\"happiness\":0.8}}. \n"
            f"     NO OTHER QUOTE TYPE IS ALLOWED FOR KEYS. NO UNQUOTED KEYS. THIS IS MANDATORY.\n"
            f"     INCORRECT EXAMPLES (DO NOT PRODUCE THIS): emotion={{happiness:0.8}}, emotion={{'happiness':0.8}}.\n"
            f"   - RULE 2B: Use ONLY the following VALID emotion names as keys: \"happiness\", \"sadness\", \"disgust\", \"fear\", \"surprise\", \"anger\", \"other\", \"neutral\". \n"
            f"     Do NOT invent or use any other emotion names. If an emotion is not on this VALID list, you MUST use \"other\" or \"neutral\" as the key (e.g., emotion={{\"other\":0.5}}).\n"
            f"   - RULE 2C: Emotion values (intensities) MUST be floats from 0.0 to 1.0. At least one emotion-intensity pair is required in the object.\n"
            f"   - Example of a PERFECTLY formatted complex emotion: emotion={{\"anger\":0.7, \"disgust\":0.4}}.\n"
            f"3. The 'pitchStd' parameter MUST be a float value for pitch standard deviation (e.g., pitchStd=45.0). \n"
            f"   - Recommended range: 30.0 to 60.0. Default to 45.0 if unsure.\n"
            f"4. The 'speed' parameter MUST be an integer value for speaking speed (e.g., speed=14). \n"
            f"   - Recommended range: 10 to 20 (e.g., 10 for slow, 14 for normal, 20 for fast). Default to 14 if unsure.\n"
            f"5. ONLY include speaker dialogue lines. NO scene descriptions, NO stage directions, NO intros/outros, NO announcer lines, NO titles, NO summaries, NO commentary about the script itself.\n"
            f"6. Emphasize important words with *word* or **word**. De-emphasize with _word_ (optional).\n"
            f"7. Ensure the conversation flows naturally and stays on topic: '{subject}'.\n"
            f"Begin the script immediately with the first speaker's line.\n"
        )
        try:
            if provider == "Ollama":
                base = self.ollama_url_input.text().strip().rstrip("/")
                ollama_system_prompt = (
                    "You are an expert podcast script writer. Your SOLE TASK is to generate dialogue lines. "
                    "EVERY dialogue line you output MUST strictly follow this exact format: "
                    "[SPEAKER_NAME, emotion={\"emotion_name\":intensity,...}, pitchStd=float_value, speed=integer_value]: Dialogue text. "
                    "Pay EXTREME attention to the user's detailed rules for the 'emotion' object, especially regarding VALID emotion names (e.g., \"happiness\", \"anger\") and MANDATORY double-quoted keys (e.g., emotion={\"happiness\":0.8}). "
                    "Do NOT invent emotion names. Do NOT use unquoted or single-quoted keys for emotions. Adherence to the specified format is CRITICAL."
                )
                payload = {
                    "model": model, 
                    "prompt": prompt, 
                    "system": ollama_system_prompt, 
                    "stream": False
                }
                resp = requests.post(f"{base}/api/generate", json=payload, timeout=120)
                resp.raise_for_status()
                result = resp.json()
                script = result.get("response", "")
                logging.debug("Raw script from Ollama: %s", script) # Log raw script
                script = self._post_process_ollama_script(script) # Post-process
                logging.debug("Post-processed script: %s", script) # Log processed script
            else:  # OpenRouter
                base = self.openrouter_url_input.text().strip().rstrip("/")
                key = self.openrouter_key_input.text().strip()
                resp = requests.post(
                    f"{base}/v1/chat/completions",
                    headers={"Authorization": f"Bearer {key}"},
                    json={"model": model, "messages":[{"role":"system","content":"You generate podcast scripts."},{"role":"user","content":prompt}], "stream": False},
                    timeout=120
                )
                resp.raise_for_status()
                data = resp.json()
                script = data["choices"][0]["message"]["content"]
            # logging.debug("Generated script content: %s", script) # This log is now more specific (raw/post-processed)
            if not script.strip():
                QMessageBox.warning(self, "Empty Script", "The model returned an empty script. Try another model or check the LLM server.")
                return
            self.script_text.setPlainText(script)
        except ReadTimeout as rt:
            logging.error("Script generation timed out: %s", rt)
            QMessageBox.critical(self, "Script Generation Timeout", f"Script generation timed out after 120 seconds. Pre-load the model with `ollama serve {model}`, or select a smaller model or increase timeout.")
            return
        except Exception as e:
            logging.error("Script generation failed: %s", e)
            QMessageBox.critical(self, "Script Generation Error", f"Could not generate script: {e}")
            return

    def on_speaker_selected(self, current, previous):
        if current:
            speaker = current.text()
            self.populate_voice_combo() # Use the centralized method
            assigned_id = voice_manager.get_speaker_voice(speaker)
            if assigned_id:
                idx = self.voice_combo.findData(assigned_id)
                if idx >= 0:
                    self.voice_combo.setCurrentIndex(idx)
            self.assign_voice_btn.setEnabled(True)
            folder = self.speaker_image_folders.get(speaker, "")
            self.image_folder_input.setText(folder)
            self.image_folder_input.setEnabled(True)
            self.browse_image_folder_btn.setEnabled(True)
        else:
            self.assign_voice_btn.setEnabled(False)
            self.image_folder_input.clear()
            self.image_folder_input.setEnabled(False)
            self.browse_image_folder_btn.setEnabled(False)

    def populate_voice_combo(self):
        self.voice_combo.clear()
        all_voices = voice_manager.get_all_voices() # From voice_manager.py
        for voice in all_voices:
            self.voice_combo.addItem(voice["name"], voice["id"])
        logging.debug("Voice combo box populated.")

    def assign_voice(self):
        item = self.speaker_list.currentItem()
        if not item:
            return
        speaker = item.text()
        voice_id = self.voice_combo.currentData()
        if voice_manager.assign_voice_to_speaker(speaker, voice_id):
            QMessageBox.information(self, "Voice Assigned", f"Assigned voice to {speaker}")
            
            audio_file_path = None
            # Check if the voice_id is for a custom voice
            if voice_id and str(voice_id).startswith("custom_"):
                for custom_voice in voice_manager.voice_profiles["custom_voices"]:
                    if custom_voice.get("id") == voice_id:
                        audio_file_path = custom_voice.get("file_path")
                        break
            
            if audio_file_path and os.path.exists(audio_file_path):
                logging.debug(f"assign_voice: Attempting to map speaker '{speaker}' to audio file: {audio_file_path}")
                self.speaker_audio_files[speaker] = audio_file_path
                logging.info(f"Associated speaker '{speaker}' with custom audio file: {audio_file_path}")
                logging.debug(f"assign_voice: self.speaker_audio_files after update: {self.speaker_audio_files}")
            elif voice_id and not str(voice_id).startswith("custom_"): 
                logging.debug(f"assign_voice: Speaker '{speaker}' assigned system voice '{voice_id}'. Clearing local audio file path if exists.")
                # This is a system voice.
                # For local Zonos TTS, if it strictly requires a file path, this might still be an issue.
                # We clear any existing local file path for this speaker if a system voice is chosen.
                if speaker in self.speaker_audio_files:
                    del self.speaker_audio_files[speaker]
                logging.info(f"Assigned system voice '{voice_id}' to speaker '{speaker}'. No specific local audio file path set for local TTS.")
            else:
                # This case means voice_id was None, or it was custom but no path was found (shouldn't happen if clone_voice works).
                if speaker in self.speaker_audio_files:
                     del self.speaker_audio_files[speaker] # Remove potentially stale entry
                logging.warning(f"Could not retrieve a valid local audio file path for voice_id '{voice_id}' for speaker '{speaker}'.")

        else:
            QMessageBox.critical(self, "Assignment Error", f"Could not assign voice to {speaker}")

    def upload_voice_sample(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Voice Sample", "", "Audio Files (*.wav *.mp3)")
        if file_path:
            voice_name = self.voice_name_input.text().strip()
            if not voice_name:
                QMessageBox.warning(self, "Missing Name", "Please enter a name for the voice.")
                return
            try: # Restore try block
                with open(file_path, "rb") as f:
                    audio_data = f.read()
                voice_id = voice_manager.clone_voice(audio_data, voice_name) # Actual cloning

                if voice_id: # If cloning was successful
                    QMessageBox.information(self, "Voice Cloned", f"Cloned voice '{voice_name}' with ID {voice_id}")
                    
                    self.populate_voice_combo() # Refresh the voice combo box

                    current_speaker_item = self.speaker_list.currentItem()
                    if current_speaker_item:
                        speaker_name = current_speaker_item.text()
                        new_voice_path = None
                        for cv in voice_manager.voice_profiles["custom_voices"]:
                            if cv.get("id") == voice_id:
                                new_voice_path = cv.get("file_path")
                                break
                        
                        if new_voice_path and os.path.exists(new_voice_path):
                            logging.debug(f"upload_voice_sample: Attempting to auto-assign speaker '{speaker_name}' to new voice_id '{voice_id}' with path: {new_voice_path}")
                            voice_manager.assign_voice_to_speaker(speaker_name, voice_id)
                            self.speaker_audio_files[speaker_name] = new_voice_path
                            logging.info(f"Automatically assigned new voice '{voice_name}' ({voice_id}) to speaker '{speaker_name}' with audio file: {new_voice_path}")
                            logging.debug(f"upload_voice_sample: self.speaker_audio_files after auto-assign: {self.speaker_audio_files}")
                            
                            for i in range(self.voice_combo.count()):
                                if self.voice_combo.itemData(i) == voice_id:
                                    self.voice_combo.setCurrentIndex(i)
                                    break
                        else:
                            logging.warning(f"Could not find file_path for newly cloned voice_id '{voice_id}' or path does not exist.")
                    else:
                        logging.info(f"New voice '{voice_name}' ({voice_id}) cloned, but no speaker selected to auto-assign.")
                else: # if voice_id is None (cloning failed)
                    QMessageBox.critical(self, "Clone Error", f"Failed to clone voice '{voice_name}'. Check logs for details.")

            except Exception as e: # Catch exceptions during file reading or cloning
                logging.error(f"Error in upload_voice_sample for '{voice_name}': {e}", exc_info=True)
                QMessageBox.critical(self, "Clone Error", f"An unexpected error occurred while cloning voice '{voice_name}': {e}")
        script = f"[{speaker}, neutral]: This is a voice preview."
        config = {
            "zonos_api_key": self.zonos_api_key_input.text().strip(),
            "tts_output_dir": self.tts_output_dir_input.text().strip()
        }
        try:
            wav_paths = run_tts_pipeline(script, config)
            if wav_paths:
                preview_wav = wav_paths[0]
                self.media_player.setSource(QUrl.fromLocalFile(preview_wav))
                self.media_player.play()
        except Exception as e:
            logging.error(f"Preview failed: {e}")
            QMessageBox.critical(self, "Preview Error", f"Could not preview voice: {e}")

    def _get_startup_info(self):
        """Stub startup info for subprocess calls."""
        if sys.platform == "win32":
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            return startupinfo
        return None

    def _get_media_duration(self, file_path: str) -> str:
        """Stub returning empty duration string."""
        try:
            command = [
                'ffmpeg',
                '-i', file_path,
                '-f', 'null',          # Don't output a file
                '-hide_banner',
                '-'                    # Read until end, output info to stderr
            ]
            result = subprocess.run(
                command,
                capture_output=True,   # Get stdout and stderr
                check=False,           # Don't raise exception on non-zero exit code
                timeout=5,             # Add a timeout (e.g., 5 seconds)
                startupinfo=self._get_startup_info() # Use helper
            )
            stderr_output = result.stderr.decode('utf-8', errors='ignore')

            # Search for duration in stderr using regex
            match = re.search(r"Duration: (\d{2}):(\d{2}):(\d{2})\.(\d+)", stderr_output)
            if match:
                hours, minutes, seconds, _ = match.groups()
                # Format the string: include hours only if > 0
                if int(hours) > 0:
                    return f"{hours}:{minutes}:{seconds}"
                else:
                    return f"{minutes}:{seconds}"
            else:
                # Log a warning if duration couldn't be parsed
                logging.warning(f"Could not parse duration for: {os.path.basename(file_path)}. FFmpeg output:\n{stderr_output[:500]}")
                return "??:??"

        except FileNotFoundError:
            logging.error(f"FFmpeg command not found. Cannot get duration for {os.path.basename(file_path)}.")
            self._ffmpeg_checked = False # Set flag
            return "Err"
        except subprocess.TimeoutExpired:
            logging.warning(f"FFmpeg timed out getting duration for {os.path.basename(file_path)}.")
            # Ensure the process is terminated if it timed out
            if result and result.returncode is None:
                try:
                    result.kill()
                except Exception as cleanup_err:
                    logging.error(f"Error killing ffmpeg duration process: {cleanup_err}")
            return "T/O"
        except Exception as e:
            logging.error(f"Error getting duration for {os.path.basename(file_path)}: {e}")
            return "Err"

    def _generate_video_thumbnail(self, video_path: str, size: QSize = QSize(64, 64)) -> QIcon | None:
        """Generate video thumbnail using OpenCV (preferred) or FFmpeg (fallback)."""
        # Try OpenCV first if available
        if OPENCV_AVAILABLE:
            try:
                return self._generate_thumbnail_opencv(video_path, size)
            except Exception as e:
                logging.warning(f"OpenCV thumbnail generation failed for {os.path.basename(video_path)}: {e}. Falling back to FFmpeg.")

        # Fallback to FFmpeg
        return self._generate_thumbnail_ffmpeg(video_path, size)

    def _generate_thumbnail_opencv(self, video_path: str, size: QSize) -> QIcon | None:
        """Generate video thumbnail using OpenCV."""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise RuntimeError(f"Could not open video file: {video_path}")

        try:
            # Set position to 0.5 seconds or 10% of video duration, whichever is smaller
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)

            if fps > 0 and frame_count > 0:
                duration = frame_count / fps
                target_time = min(0.5, duration * 0.1)  # 0.5s or 10% of duration
                target_frame = int(target_time * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)

            ret, frame = cap.read()
            if not ret or frame is None:
                raise RuntimeError("Could not read frame from video")

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Resize frame maintaining aspect ratio
            h, w = frame_rgb.shape[:2]
            target_w, target_h = size.width(), size.height()

            # Calculate scaling factor to fit within target size
            scale = min(target_w / w, target_h / h)
            new_w, new_h = int(w * scale), int(h * scale)

            resized_frame = cv2.resize(frame_rgb, (new_w, new_h), interpolation=cv2.INTER_AREA)

            # Convert to QImage
            h, w, ch = resized_frame.shape
            bytes_per_line = ch * w
            qt_image = QImage(resized_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)

            # Convert to QPixmap and QIcon
            pixmap = QPixmap.fromImage(qt_image)
            return QIcon(pixmap)

        finally:
            cap.release()

    def _generate_thumbnail_ffmpeg(self, video_path: str, size: QSize) -> QIcon | None:
        """Generate video thumbnail using FFmpeg (fallback method)."""
        try:
            command = [
                'ffmpeg',
                '-i', video_path,
                '-ss', '00:00:00.500', # Extract frame at 0.5 seconds
                '-vframes', '1',
                '-vf', f'scale={size.width()}:{size.height()}:force_original_aspect_ratio=decrease',
                '-c:v', 'png',
                '-f', 'image2pipe',
                '-hide_banner',
                '-loglevel', 'error',
                '-'                    # Output to stdout
            ]
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=self._get_startup_info() # Use helper
            )
            stdout, stderr = process.communicate(timeout=5)

            if process.returncode != 0:
                stderr_str = stderr.decode('utf-8', errors='ignore').strip()
                if stdout or (stderr_str and "Output file does not contain any stream" not in stderr_str):
                    logging.warning(f"FFmpeg error getting thumbnail for {os.path.basename(video_path)}: {stderr_str}")
                return None

            if not stdout:
                return None

            image = QImage()
            if image.loadFromData(stdout, "PNG"):
                pixmap = QPixmap.fromImage(image)
                return QIcon(pixmap)
            else:
                logging.warning(f"Failed to load QImage from FFmpeg output for {os.path.basename(video_path)}.")
                return None

        except FileNotFoundError:
            logging.error("FFmpeg command not found. Cannot generate video thumbnails.")
            self._ffmpeg_checked = False
            return None
        except subprocess.TimeoutExpired:
            logging.warning(f"FFmpeg timed out getting thumbnail for {os.path.basename(video_path)}.")
            return None
        except Exception as e:
            logging.error(f"Error generating thumbnail for {os.path.basename(video_path)}: {e}")
            return None

    def preview_full_audio(self):
        from pydub import AudioSegment
        combined = AudioSegment.empty()
        for video_path, audio_path, trim in self.processing_queue:
            seg = AudioSegment.from_file(audio_path)
            if trim:
                start, end = trim
                seg = seg[int(start*1000):int(end*1000)]
            combined += seg
        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        combined.export(tmp.name, format="wav")
        if hasattr(self, 'combined_player'):
            self.combined_player.stop()
        self.combined_player = QMediaPlayer(self)
        out = QAudioOutput(self)
        self.combined_player.setAudioOutput(out)
        self.combined_player.setSource(QUrl.fromLocalFile(tmp.name))
        self.combined_player.positionChanged.connect(self.audio_slider.setValue)
        self.combined_player.durationChanged.connect(lambda duration: self.audio_slider.setRange(0, duration))
        self.play_all_btn.clicked.connect(self.combined_player.play)
        self.pause_all_btn.clicked.connect(self.combined_player.pause)
        self.stop_all_btn.clicked.connect(self.combined_player.stop)
        self.audio_slider.sliderMoved.connect(self.combined_player.setPosition)
        for w in (self.play_all_btn, self.pause_all_btn, self.stop_all_btn, self.audio_slider):
            w.setEnabled(True)
        self.combined_player.play()
        self.current_audio_player = self.combined_player

    def closeEvent(self, event):
        """Handle application close event."""
        # Save configuration before closing
        self._save_configuration()

        # Stop any running media
        if hasattr(self, 'media_player'):
            self.media_player.stop()

        # Accept the close event
        event.accept()

    def _save_configuration(self):
        """Save current GUI values to configuration."""
        try:
            # Update configuration with current GUI values
            gui_values = {
                'comfyui_url': self.comfyui_url_edit.text(),
                'workflow_path': self.workflow_path_edit.text(),
                'framepack_workflow_path': self.framepack_workflow_path_edit.text(),
                'comfyui_output_base_dir': self.comfyui_output_base_dir_edit.text(),
                'input_dir': self.input_dir_input.text(),
                'output_dir': self.output_dir_input.text(),
                'zonos_api_key': self.zonos_api_key_input.text(),
                'tts_output_dir': self.tts_output_dir_input.text(),
                'use_local_tts': self.use_local_tts_checkbox.isChecked(),
                'seed': self.seed_input.value(),
                'lips_expression': self.lips_expr_input.value(),
                'inference_steps': self.inference_steps_input.value(),
                'crf': self.crf_input.value(),
                'silent_padding_sec': self.silent_padding_input.value(),
                'framepack_prompt': self.framepack_prompt_input.toPlainText(),
                'framepack_seed': self.framepack_seed_input.value(),
                'framepack_random': self.framepack_random_checkbox.isChecked(),
            }

            config_manager.update_from_gui(gui_values)
            config_manager.save_to_file()
            logging.info("Configuration saved successfully")

        except Exception as e:
            logging.error(f"Failed to save configuration: {e}")

    def _initialize_advanced_features(self):
        """Initialize advanced features and integrate them into the existing UI."""
        try:
            print("🚀 Initializing advanced features...")

            # Initialize advanced systems
            self.advanced_features_enabled = True
            print("✅ Advanced features flag set")

            # Theme management
            self.current_theme = "dark"

            # Project management
            if PROJECT_MANAGEMENT_AVAILABLE:
                try:
                    self.project_manager = ProjectManager()
                    self.project_manager.auto_save_manager.enable(True)
                    print("✅ Project Manager initialized")
                except Exception as e:
                    print(f"❌ Error initializing Project Manager: {e}")
                    self.project_manager = None
            else:
                self.project_manager = None
                print("⚠️ Project Management not available")

            # Media library management
            if MEDIA_LIBRARY_AVAILABLE:
                try:
                    self.media_library_database = MediaDatabase("media_library.json")
                    print("✅ Media Library Database initialized")
                except Exception as e:
                    print(f"❌ Error initializing Media Library Database: {e}")
                    self.media_library_database = None
            else:
                self.media_library_database = None
                print("⚠️ Media Library not available")

            # Fallback project data for compatibility
            self.current_project_path = None
            self.project_data = {
                "metadata": {"name": "Untitled Project", "version": "1.0"},
                "timeline": {"clips": [], "tracks": []},
                "media_library": {"items": []},
                "workflow": {"nodes": [], "connections": []},
                "audio": {"tracks": [], "effects": []},
                "settings": {}
            }

            # Undo/Redo system
            self.undo_stack = []
            self.redo_stack = []
            self.max_undo_history = 100

            # Progress tracking
            self.active_progress = {}
            self.progress_counter = 0

            # Add advanced UI components
            print("📋 Adding advanced menu items...")
            self._add_advanced_menu_items()
            print("✅ Menu items added")

            print("📋 Adding advanced panels...")
            self._add_advanced_panels()
            print("✅ Panels added")

            print("📋 Setting up shortcuts...")
            self._setup_advanced_shortcuts()
            print("✅ Shortcuts set up")

            print("✅ Advanced features initialized successfully!")

        except Exception as e:
            print(f"⚠️ Error initializing advanced features: {e}")
            import traceback
            traceback.print_exc()
            self.advanced_features_enabled = False

    def _add_advanced_menu_items(self):
        """Add advanced menu items to the existing menu bar."""
        try:
            menubar = self.menuBar()

            # Add View menu if it doesn't exist
            view_menu = None
            for action in menubar.actions():
                if action.text() == "&View":
                    view_menu = action.menu()
                    break

            if not view_menu:
                view_menu = menubar.addMenu("&View")

            # Add theme switching
            view_menu.addSeparator()
            theme_menu = view_menu.addMenu("Theme")

            light_action = QAction("Light Theme", self)
            light_action.triggered.connect(lambda: self._switch_theme("light"))
            theme_menu.addAction(light_action)

            dark_action = QAction("Dark Theme", self)
            dark_action.triggered.connect(lambda: self._switch_theme("dark"))
            theme_menu.addAction(dark_action)

            # Add Tools menu
            tools_menu = menubar.addMenu("&Tools")

            project_action = QAction("Project Manager", self)
            project_action.triggered.connect(self._show_project_manager)
            tools_menu.addAction(project_action)

            timeline_action = QAction("Timeline Editor", self)
            timeline_action.triggered.connect(self._show_timeline_editor)
            tools_menu.addAction(timeline_action)

            workflow_action = QAction("Workflow Builder", self)
            workflow_action.triggered.connect(self._show_workflow_builder)
            tools_menu.addAction(workflow_action)

            audio_action = QAction("Audio Mixer", self)
            audio_action.triggered.connect(self._show_audio_mixer)
            tools_menu.addAction(audio_action)

            # Add Edit menu enhancements
            edit_menu = None
            for action in menubar.actions():
                if action.text() == "&Edit":
                    edit_menu = action.menu()
                    break

            if not edit_menu:
                edit_menu = menubar.addMenu("&Edit")

            edit_menu.addSeparator()

            undo_action = QAction("Undo", self)
            undo_action.setShortcut("Ctrl+Z")
            undo_action.triggered.connect(self._undo_action)
            edit_menu.addAction(undo_action)

            redo_action = QAction("Redo", self)
            redo_action.setShortcut("Ctrl+Y")
            redo_action.triggered.connect(self._redo_action)
            edit_menu.addAction(redo_action)

        except Exception as e:
            print(f"❌ Error adding advanced menu items: {e}")

    def _add_advanced_panels(self):
        """Add advanced panels to the existing interface."""
        try:
            print("📋 Getting current central widget...")
            current_central = self.centralWidget()

            if current_central is None:
                print("⚠️ No central widget found, skipping panel integration")
                return

            print("📋 Creating enhanced widget...")
            enhanced_widget = QWidget()

            print("📋 Creating main splitter...")
            main_splitter = QSplitter(Qt.Orientation.Horizontal)
            enhanced_layout = QVBoxLayout(enhanced_widget)
            enhanced_layout.addWidget(main_splitter)

            print("📋 Creating left panel...")
            left_panel = self._create_advanced_left_panel()
            main_splitter.addWidget(left_panel)

            print("📋 Adding original interface to center...")
            main_splitter.addWidget(current_central)

            print("📋 Creating right panel...")
            right_panel = self._create_advanced_right_panel()
            main_splitter.addWidget(right_panel)

            print("📋 Setting splitter proportions...")
            main_splitter.setSizes([300, 900, 300])

            print("📋 Setting new central widget...")
            self.setCentralWidget(enhanced_widget)

            print("📋 Enhancing status bar...")
            self._enhance_status_bar()

            print("✅ Advanced panels added successfully")

        except Exception as e:
            print(f"❌ Error adding advanced panels: {e}")
            import traceback
            traceback.print_exc()
            # Restore original central widget on error
            try:
                if 'current_central' in locals() and current_central:
                    self.setCentralWidget(current_central)
                    print("✅ Restored original central widget")
            except Exception as restore_error:
                print(f"❌ Error restoring central widget: {restore_error}")

    def _create_advanced_left_panel(self) -> QWidget:
        """Create the advanced left panel with media library and project info."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Tab widget for different views
        left_tabs = QTabWidget()

        # Media Library tab
        media_tab = self._create_media_library_tab()
        left_tabs.addTab(media_tab, "Media Library")

        # Project Info tab
        project_tab = self._create_project_info_tab()
        left_tabs.addTab(project_tab, "Project")

        layout.addWidget(left_tabs)
        return panel

    def _create_advanced_right_panel(self) -> QWidget:
        """Create the advanced right panel with timeline and audio tools."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Tab widget for different tools
        right_tabs = QTabWidget()

        # Timeline tab
        timeline_tab = self._create_timeline_tab()
        right_tabs.addTab(timeline_tab, "Timeline")

        # Audio Mixer tab
        audio_tab = self._create_audio_mixer_tab()
        right_tabs.addTab(audio_tab, "Audio")

        # Progress tab
        progress_tab = self._create_progress_tab()
        right_tabs.addTab(progress_tab, "Progress")

        layout.addWidget(right_tabs)
        return panel

    def _create_media_library_tab(self) -> QWidget:
        """Create the comprehensive media library tab."""
        if MEDIA_LIBRARY_AVAILABLE and hasattr(self, 'media_library_database') and self.media_library_database:
            try:
                # Create the professional media library widget
                self.media_library_widget = MediaLibraryWidget()

                # Connect media library signals to main window
                self.media_library_widget.media_selected.connect(self._on_media_library_selected)
                self.media_library_widget.media_double_clicked.connect(self._on_media_library_double_clicked)

                print("✅ Professional Media Library integrated")
                return self.media_library_widget

            except Exception as e:
                print(f"❌ Error creating media library widget: {e}")
                # Fall back to basic media library tab
                return self._create_basic_media_library_tab()
        else:
            # Fall back to basic media library tab
            return self._create_basic_media_library_tab()

    def _create_basic_media_library_tab(self) -> QWidget:
        """Create a basic media library tab as fallback."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Search bar
        search_layout = QHBoxLayout()
        search_label = QLabel("Search:")
        self.media_search_input = QLineEdit()
        self.media_search_input.setPlaceholderText("Search media files...")
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.media_search_input)
        layout.addLayout(search_layout)

        # Media list
        self.media_library_list = QListWidget()
        layout.addWidget(self.media_library_list)

        # Buttons
        button_layout = QHBoxLayout()
        add_media_btn = QPushButton("Add Media")
        add_media_btn.clicked.connect(self._add_media_to_library)
        remove_media_btn = QPushButton("Remove")
        remove_media_btn.clicked.connect(self._remove_media_from_library)
        button_layout.addWidget(add_media_btn)
        button_layout.addWidget(remove_media_btn)
        layout.addLayout(button_layout)

        return widget

    def _create_project_info_tab(self) -> QWidget:
        """Create the comprehensive project management tab."""
        if PROJECT_MANAGEMENT_AVAILABLE and hasattr(self, 'project_manager') and self.project_manager:
            try:
                # Create the professional project widget
                self.project_widget = ProjectWidget(self.project_manager)

                # Connect project widget signals to main window
                self.project_widget.project_loaded.connect(self._on_project_loaded)
                self.project_widget.project_saved.connect(self._on_project_saved)
                self.project_widget.project_created.connect(self._on_project_created)

                print("✅ Professional Project Management integrated")
                return self.project_widget

            except Exception as e:
                print(f"❌ Error creating project widget: {e}")
                # Fall back to basic project tab
                return self._create_basic_project_tab()
        else:
            # Fall back to basic project tab
            return self._create_basic_project_tab()

    def _create_basic_project_tab(self) -> QWidget:
        """Create a basic project info tab as fallback."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Project name
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("Project:"))
        self.project_name_label = QLabel("Untitled Project")
        name_layout.addWidget(self.project_name_label)
        layout.addLayout(name_layout)

        # Project buttons
        button_layout = QVBoxLayout()

        new_project_btn = QPushButton("New Project")
        new_project_btn.clicked.connect(self._new_project)
        button_layout.addWidget(new_project_btn)

        save_project_btn = QPushButton("Save Project")
        save_project_btn.clicked.connect(self._save_project)
        button_layout.addWidget(save_project_btn)

        load_project_btn = QPushButton("Load Project")
        load_project_btn.clicked.connect(self._load_project)
        button_layout.addWidget(load_project_btn)

        layout.addLayout(button_layout)

        # Project stats
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_clips_label = QLabel("Clips: 0")
        self.stats_duration_label = QLabel("Duration: 0:00")
        self.stats_size_label = QLabel("Size: 0 MB")

        stats_layout.addWidget(self.stats_clips_label)
        stats_layout.addWidget(self.stats_duration_label)
        stats_layout.addWidget(self.stats_size_label)

        layout.addWidget(stats_group)
        layout.addStretch()

        return widget

    def _create_timeline_tab(self) -> QWidget:
        """Create the professional timeline tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        if TIMELINE_EDITOR_AVAILABLE:
            try:
                # Create professional timeline editor
                self.timeline_editor = TimelineEditor()

                # Connect timeline signals to main window
                self.timeline_editor.clip_selected.connect(self._on_timeline_clip_selected)
                self.timeline_editor.time_changed.connect(self._on_timeline_time_changed)
                self.timeline_editor.playback_state_changed.connect(self._on_timeline_playback_changed)

                layout.addWidget(self.timeline_editor)

                # Add integration controls
                integration_group = QGroupBox("Integration")
                integration_layout = QHBoxLayout(integration_group)

                add_video_btn = QPushButton("Add Selected Video")
                add_video_btn.clicked.connect(self._add_selected_video_to_timeline)
                integration_layout.addWidget(add_video_btn)

                add_audio_btn = QPushButton("Add Selected Audio")
                add_audio_btn.clicked.connect(self._add_selected_audio_to_timeline)
                integration_layout.addWidget(add_audio_btn)

                sync_btn = QPushButton("Sync with Processing")
                sync_btn.clicked.connect(self._sync_timeline_with_processing)
                integration_layout.addWidget(sync_btn)

                layout.addWidget(integration_group)

                print("✅ Professional Timeline Editor integrated")

            except Exception as e:
                print(f"❌ Error creating timeline editor: {e}")
                # Fallback to basic timeline
                layout.addWidget(self._create_basic_timeline())
        else:
            # Fallback to basic timeline
            layout.addWidget(self._create_basic_timeline())

        return widget

    def _create_basic_timeline(self) -> QWidget:
        """Create a basic timeline fallback."""
        timeline_group = QGroupBox("Basic Timeline")
        timeline_layout = QVBoxLayout(timeline_group)

        # Timeline controls
        controls_layout = QHBoxLayout()

        play_btn = QPushButton("▶")
        play_btn.setMaximumWidth(40)
        pause_btn = QPushButton("⏸")
        pause_btn.setMaximumWidth(40)
        stop_btn = QPushButton("⏹")
        stop_btn.setMaximumWidth(40)

        controls_layout.addWidget(play_btn)
        controls_layout.addWidget(pause_btn)
        controls_layout.addWidget(stop_btn)
        controls_layout.addStretch()

        timeline_layout.addLayout(controls_layout)

        # Time ruler
        self.time_ruler = QLabel("0:00 ────────────────── 0:10 ────────────────── 0:20")
        self.time_ruler.setStyleSheet("font-family: monospace; background: #333; color: white; padding: 5px;")
        timeline_layout.addWidget(self.time_ruler)

        # Timeline tracks
        self.timeline_tracks = QListWidget()
        self.timeline_tracks.addItem("🎬 Video Track 1")
        self.timeline_tracks.addItem("🎬 Video Track 2")
        self.timeline_tracks.addItem("🎵 Audio Track 1")
        self.timeline_tracks.addItem("🎵 Audio Track 2")
        timeline_layout.addWidget(self.timeline_tracks)

        # Timeline buttons
        timeline_btn_layout = QHBoxLayout()
        add_track_btn = QPushButton("Add Track")
        remove_track_btn = QPushButton("Remove Track")
        timeline_btn_layout.addWidget(add_track_btn)
        timeline_btn_layout.addWidget(remove_track_btn)
        timeline_layout.addLayout(timeline_btn_layout)

        return timeline_group

    def _on_timeline_clip_selected(self, clip):
        """Handle timeline clip selection."""
        try:
            if hasattr(self, 'timeline_editor'):
                logging.info(f"Timeline clip selected: {clip.name}")
                # Update media preview if possible
                if hasattr(self, 'video_widget') and clip.file_path:
                    self._preview_media_file(clip.file_path)
        except Exception as e:
            logging.error(f"Error handling timeline clip selection: {e}")

    def _on_timeline_time_changed(self, time: float):
        """Handle timeline time change."""
        try:
            if hasattr(self, 'timeline_editor'):
                # Update any time-based displays
                logging.debug(f"Timeline time changed to: {time:.2f}s")
        except Exception as e:
            logging.error(f"Error handling timeline time change: {e}")

    def _on_timeline_playback_changed(self, state):
        """Handle timeline playback state change."""
        try:
            if hasattr(self, 'timeline_editor'):
                logging.info(f"Timeline playback state changed to: {state}")
        except Exception as e:
            logging.error(f"Error handling timeline playback change: {e}")

    def _add_selected_video_to_timeline(self):
        """Add the currently selected video to the timeline."""
        try:
            if hasattr(self, 'timeline_editor') and self.current_selected_video_path:
                success = self.timeline_editor.add_media_clip(
                    self.current_selected_video_path,
                    ClipType.VIDEO_CLIP,
                    track_index=0
                )
                if success:
                    logging.info(f"Added video to timeline: {self.current_selected_video_path}")
                    self.status_bar.showMessage(f"Added video to timeline: {os.path.basename(self.current_selected_video_path)}")
                else:
                    QMessageBox.warning(self, "Timeline", "Could not add video to timeline. Check for conflicts.")
            else:
                QMessageBox.information(self, "Timeline", "Please select a video file first.")
        except Exception as e:
            logging.error(f"Error adding video to timeline: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add video to timeline: {e}")

    def _add_selected_audio_to_timeline(self):
        """Add the currently selected audio to the timeline."""
        try:
            if hasattr(self, 'timeline_editor') and self.current_selected_audio_path:
                success = self.timeline_editor.add_media_clip(
                    self.current_selected_audio_path,
                    ClipType.AUDIO_CLIP,
                    track_index=2  # Audio track
                )
                if success:
                    logging.info(f"Added audio to timeline: {self.current_selected_audio_path}")
                    self.status_bar.showMessage(f"Added audio to timeline: {os.path.basename(self.current_selected_audio_path)}")
                else:
                    QMessageBox.warning(self, "Timeline", "Could not add audio to timeline. Check for conflicts.")
            else:
                QMessageBox.information(self, "Timeline", "Please select an audio file first.")
        except Exception as e:
            logging.error(f"Error adding audio to timeline: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add audio to timeline: {e}")

    def _sync_timeline_with_processing(self):
        """Sync timeline with current processing queue."""
        try:
            if hasattr(self, 'timeline_editor'):
                # Clear existing clips
                for clip in self.timeline_editor.get_all_clips():
                    self.timeline_editor.timeline_view.remove_clip(clip.id)

                # Add clips from processing queue
                for i, item in enumerate(self.processing_queue):
                    if 'video_path' in item:
                        self.timeline_editor.add_media_clip(
                            item['video_path'],
                            ClipType.VIDEO_CLIP,
                            track_index=0
                        )
                    if 'audio_path' in item:
                        self.timeline_editor.add_media_clip(
                            item['audio_path'],
                            ClipType.AUDIO_CLIP,
                            track_index=2
                        )

                logging.info("Timeline synced with processing queue")
                self.status_bar.showMessage("Timeline synced with processing queue")
            else:
                QMessageBox.information(self, "Timeline", "Timeline editor not available.")
        except Exception as e:
            logging.error(f"Error syncing timeline: {e}")
            QMessageBox.critical(self, "Error", f"Failed to sync timeline: {e}")

    def _on_project_loaded(self, project_path: str):
        """Handle project loaded signal from project widget."""
        try:
            logging.info(f"Project loaded: {project_path}")
            self.status_bar.showMessage(f"Project loaded: {os.path.basename(project_path)}")

            # Update window title
            if hasattr(self, 'project_manager') and self.project_manager.current_project:
                project_name = self.project_manager.current_project.metadata.name
                self.setWindowTitle(f"{config.ui.window_title} - {project_name}")

            # Sync with timeline if available
            if hasattr(self, 'timeline_editor'):
                self._sync_timeline_with_project()

        except Exception as e:
            logging.error(f"Error handling project loaded: {e}")

    def _on_project_saved(self, project_path: str):
        """Handle project saved signal from project widget."""
        try:
            logging.info(f"Project saved: {project_path}")
            self.status_bar.showMessage(f"Project saved: {os.path.basename(project_path)}")
        except Exception as e:
            logging.error(f"Error handling project saved: {e}")

    def _on_project_created(self, project_name: str):
        """Handle project created signal from project widget."""
        try:
            logging.info(f"New project created: {project_name}")
            self.status_bar.showMessage(f"New project created: {project_name}")

            # Update window title
            self.setWindowTitle(f"{config.ui.window_title} - {project_name}")

        except Exception as e:
            logging.error(f"Error handling project created: {e}")

    def _sync_timeline_with_project(self):
        """Sync timeline with current project data."""
        try:
            if (hasattr(self, 'timeline_editor') and hasattr(self, 'project_manager')
                and self.project_manager.current_project):

                project = self.project_manager.current_project
                timeline_data = project.timeline

                # Clear existing timeline clips
                for clip in self.timeline_editor.get_all_clips():
                    self.timeline_editor.timeline_view.remove_clip(clip.id)

                # Load clips from project data
                if 'clips' in timeline_data:
                    for clip_data in timeline_data['clips']:
                        # Convert project clip data to timeline clip
                        if 'file_path' in clip_data and os.path.exists(clip_data['file_path']):
                            clip_type = ClipType.VIDEO_CLIP if clip_data.get('type') == 'video' else ClipType.AUDIO_CLIP
                            track_index = clip_data.get('track_index', 0)

                            self.timeline_editor.add_media_clip(
                                clip_data['file_path'],
                                clip_type,
                                track_index
                            )

                logging.info("Timeline synced with project data")

        except Exception as e:
            logging.error(f"Error syncing timeline with project: {e}")

    def _on_media_library_selected(self, file_path: str):
        """Handle media library item selection."""
        try:
            logging.info(f"Media library item selected: {file_path}")

            # Update current selection paths
            if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm')):
                self.current_selected_video_path = file_path
                self.current_selected_audio_path = None
            elif file_path.lower().endswith(('.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a')):
                self.current_selected_audio_path = file_path
                self.current_selected_video_path = None

            # Update preview if possible
            if hasattr(self, 'video_widget'):
                self._preview_media_file(file_path)

            self.status_bar.showMessage(f"Selected: {os.path.basename(file_path)}")

        except Exception as e:
            logging.error(f"Error handling media library selection: {e}")

    def _on_media_library_double_clicked(self, file_path: str):
        """Handle media library item double-click."""
        try:
            logging.info(f"Media library item double-clicked: {file_path}")

            # Add to timeline if available
            if hasattr(self, 'timeline_editor'):
                if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm')):
                    success = self.timeline_editor.add_media_clip(file_path, ClipType.VIDEO_CLIP, track_index=0)
                elif file_path.lower().endswith(('.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a')):
                    success = self.timeline_editor.add_media_clip(file_path, ClipType.AUDIO_CLIP, track_index=2)
                else:
                    success = False

                if success:
                    self.status_bar.showMessage(f"Added to timeline: {os.path.basename(file_path)}")
                else:
                    QMessageBox.warning(self, "Timeline", "Could not add media to timeline. Check for conflicts.")
            else:
                # Fallback: add to processing queue
                self._add_to_processing_queue(file_path)

        except Exception as e:
            logging.error(f"Error handling media library double-click: {e}")

    def _add_to_processing_queue(self, file_path: str):
        """Add media file to processing queue."""
        try:
            if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm')):
                # Add as video
                queue_item = {
                    'video_path': file_path,
                    'audio_path': self.current_selected_audio_path or '',
                    'status': 'pending'
                }
                self.processing_queue.append(queue_item)
                self._update_queue_display()
                self.status_bar.showMessage(f"Added video to queue: {os.path.basename(file_path)}")

            elif file_path.lower().endswith(('.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a')):
                # Add as audio
                queue_item = {
                    'video_path': self.current_selected_video_path or '',
                    'audio_path': file_path,
                    'status': 'pending'
                }
                self.processing_queue.append(queue_item)
                self._update_queue_display()
                self.status_bar.showMessage(f"Added audio to queue: {os.path.basename(file_path)}")

        except Exception as e:
            logging.error(f"Error adding to processing queue: {e}")

    def _create_audio_mixer_tab(self) -> QWidget:
        """Create the audio mixer tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Master controls
        master_group = QGroupBox("Master")
        master_layout = QVBoxLayout(master_group)

        # Master volume
        self.master_volume_slider = QSlider(Qt.Orientation.Vertical)
        self.master_volume_slider.setRange(0, 100)
        self.master_volume_slider.setValue(80)
        self.master_volume_label = QLabel("80%")

        master_vol_layout = QHBoxLayout()
        master_vol_layout.addWidget(self.master_volume_slider)
        master_vol_layout.addWidget(self.master_volume_label)
        master_layout.addLayout(master_vol_layout)

        layout.addWidget(master_group)

        # Audio tracks
        tracks_group = QGroupBox("Audio Tracks")
        tracks_layout = QVBoxLayout(tracks_group)

        # Track 1
        track1_layout = QHBoxLayout()
        track1_layout.addWidget(QLabel("Track 1:"))
        track1_volume = QSlider(Qt.Orientation.Horizontal)
        track1_volume.setRange(0, 100)
        track1_volume.setValue(70)
        track1_layout.addWidget(track1_volume)
        track1_layout.addWidget(QLabel("70%"))
        tracks_layout.addLayout(track1_layout)

        # Track 2
        track2_layout = QHBoxLayout()
        track2_layout.addWidget(QLabel("Track 2:"))
        track2_volume = QSlider(Qt.Orientation.Horizontal)
        track2_volume.setRange(0, 100)
        track2_volume.setValue(60)
        track2_layout.addWidget(track2_volume)
        track2_layout.addWidget(QLabel("60%"))
        tracks_layout.addLayout(track2_layout)

        layout.addWidget(tracks_group)

        # Audio effects
        effects_group = QGroupBox("Effects")
        effects_layout = QVBoxLayout(effects_group)

        effects_combo = QComboBox()
        effects_combo.addItems(["None", "Reverb", "Echo", "Noise Reduction", "Equalizer"])
        effects_layout.addWidget(effects_combo)

        apply_effect_btn = QPushButton("Apply Effect")
        effects_layout.addWidget(apply_effect_btn)

        layout.addWidget(effects_group)
        layout.addStretch()

        return widget

    def _create_progress_tab(self) -> QWidget:
        """Create the progress tracking tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Current progress
        progress_group = QGroupBox("Current Progress")
        progress_layout = QVBoxLayout(progress_group)

        self.current_progress_label = QLabel("Ready")
        progress_layout.addWidget(self.current_progress_label)

        self.current_progress_bar = QProgressBar()
        progress_layout.addWidget(self.current_progress_bar)

        layout.addWidget(progress_group)

        # Progress history
        history_group = QGroupBox("Recent Tasks")
        history_layout = QVBoxLayout(history_group)

        self.progress_history_list = QListWidget()
        history_layout.addWidget(self.progress_history_list)

        layout.addWidget(history_group)

        # System status
        status_group = QGroupBox("System Status")
        status_layout = QVBoxLayout(status_group)

        self.system_status_label = QLabel("System: Ready")
        self.memory_status_label = QLabel("Memory: Normal")
        self.cpu_status_label = QLabel("CPU: Normal")

        status_layout.addWidget(self.system_status_label)
        status_layout.addWidget(self.memory_status_label)
        status_layout.addWidget(self.cpu_status_label)

        layout.addWidget(status_group)
        layout.addStretch()

        return widget

    def _setup_advanced_shortcuts(self):
        """Setup keyboard shortcuts for advanced features."""
        try:
            # Theme switching
            theme_shortcut = QShortcut("Ctrl+T", self)
            theme_shortcut.activated.connect(self._toggle_theme)

            # Project shortcuts
            new_project_shortcut = QShortcut("Ctrl+N", self)
            new_project_shortcut.activated.connect(self._new_project)

            save_project_shortcut = QShortcut("Ctrl+S", self)
            save_project_shortcut.activated.connect(self._save_project)

            load_project_shortcut = QShortcut("Ctrl+O", self)
            load_project_shortcut.activated.connect(self._load_project)

        except Exception as e:
            print(f"❌ Error setting up shortcuts: {e}")

    def _enhance_status_bar(self):
        """Enhance the status bar with advanced information."""
        try:
            status_bar = self.statusBar()

            # Add advanced status widgets
            self.theme_status_label = QLabel(f"Theme: {self.current_theme.title()}")
            status_bar.addPermanentWidget(self.theme_status_label)

            self.project_status_label = QLabel("Project: Untitled")
            status_bar.addPermanentWidget(self.project_status_label)

            self.undo_status_label = QLabel("Ready")
            status_bar.addPermanentWidget(self.undo_status_label)

        except Exception as e:
            print(f"❌ Error enhancing status bar: {e}")

    # Advanced Feature Action Methods
    def _switch_theme(self, theme_name):
        """Switch between light and dark themes."""
        try:
            self.current_theme = theme_name

            if theme_name == "dark":
                self.setStyleSheet("""
                    QMainWindow { background-color: #2b2b2b; color: white; }
                    QWidget { background-color: #2b2b2b; color: white; }
                    QTabWidget::pane { border: 1px solid #555; background-color: #3c3c3c; }
                    QTabBar::tab { background-color: #555; color: white; padding: 8px; }
                    QTabBar::tab:selected { background-color: #777; }
                    QGroupBox { border: 2px solid #555; margin: 5px; padding-top: 10px; }
                    QGroupBox::title { color: white; }
                    QPushButton { background-color: #555; color: white; border: 1px solid #777; padding: 5px; }
                    QPushButton:hover { background-color: #666; }
                    QListWidget { background-color: #3c3c3c; color: white; border: 1px solid #555; }
                    QLineEdit { background-color: #3c3c3c; color: white; border: 1px solid #555; padding: 3px; }
                    QLabel { color: white; }
                """)
            else:
                self.setStyleSheet("")  # Reset to default light theme

            # Update status bar
            if hasattr(self, 'theme_status_label'):
                self.theme_status_label.setText(f"Theme: {theme_name.title()}")

            print(f"✅ Switched to {theme_name} theme")

        except Exception as e:
            print(f"❌ Error switching theme: {e}")

    def _toggle_theme(self):
        """Toggle between light and dark themes."""
        new_theme = "light" if self.current_theme == "dark" else "dark"
        self._switch_theme(new_theme)

    def _new_project(self):
        """Create a new project."""
        try:
            # Reset project data
            self.project_data = {
                "metadata": {"name": "Untitled Project", "version": "1.0"},
                "timeline": {"clips": [], "tracks": []},
                "media_library": {"items": []},
                "workflow": {"nodes": [], "connections": []},
                "audio": {"tracks": [], "effects": []},
                "settings": {}
            }

            self.current_project_path = None

            # Update UI
            if hasattr(self, 'project_name_label'):
                self.project_name_label.setText("Untitled Project")
            if hasattr(self, 'project_status_label'):
                self.project_status_label.setText("Project: Untitled")

            # Clear media library
            if hasattr(self, 'media_library_list'):
                self.media_library_list.clear()

            # Add to undo stack
            self._add_to_undo_stack("New Project", "Created new project")

            print("✅ New project created")

        except Exception as e:
            print(f"❌ Error creating new project: {e}")

    def _save_project(self):
        """Save the current project."""
        try:
            if not self.current_project_path:
                # Show save dialog
                file_path, _ = QFileDialog.getSaveFileName(
                    self, "Save Project", "", "Project Files (*.alvp);;All Files (*)"
                )
                if not file_path:
                    return
                self.current_project_path = file_path

            # Save project data
            import json
            with open(self.current_project_path, 'w') as f:
                json.dump(self.project_data, f, indent=2)

            # Update UI
            project_name = os.path.basename(self.current_project_path).replace('.alvp', '')
            if hasattr(self, 'project_name_label'):
                self.project_name_label.setText(project_name)
            if hasattr(self, 'project_status_label'):
                self.project_status_label.setText(f"Project: {project_name}")

            # Add to undo stack
            self._add_to_undo_stack("Save Project", f"Saved project to {self.current_project_path}")

            print(f"✅ Project saved to {self.current_project_path}")

        except Exception as e:
            print(f"❌ Error saving project: {e}")

    def _load_project(self):
        """Load an existing project."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load Project", "", "Project Files (*.alvp);;All Files (*)"
            )
            if not file_path:
                return

            # Load project data
            import json
            with open(file_path, 'r') as f:
                self.project_data = json.load(f)

            self.current_project_path = file_path

            # Update UI
            project_name = os.path.basename(file_path).replace('.alvp', '')
            if hasattr(self, 'project_name_label'):
                self.project_name_label.setText(project_name)
            if hasattr(self, 'project_status_label'):
                self.project_status_label.setText(f"Project: {project_name}")

            # Load media library items
            if hasattr(self, 'media_library_list'):
                self.media_library_list.clear()
                for item in self.project_data.get('media_library', {}).get('items', []):
                    self.media_library_list.addItem(item.get('name', 'Unknown'))

            # Add to undo stack
            self._add_to_undo_stack("Load Project", f"Loaded project from {file_path}")

            print(f"✅ Project loaded from {file_path}")

        except Exception as e:
            print(f"❌ Error loading project: {e}")

    def _add_media_to_library(self):
        """Add media files to the library."""
        try:
            file_paths, _ = QFileDialog.getOpenFileNames(
                self, "Add Media Files", "",
                "Media Files (*.mp4 *.avi *.mov *.mp3 *.wav *.jpg *.png);;All Files (*)"
            )

            for file_path in file_paths:
                # Add to media library list
                if hasattr(self, 'media_library_list'):
                    filename = os.path.basename(file_path)
                    self.media_library_list.addItem(filename)

                # Add to project data
                media_item = {
                    "name": os.path.basename(file_path),
                    "path": file_path,
                    "type": "video" if file_path.lower().endswith(('.mp4', '.avi', '.mov')) else "audio" if file_path.lower().endswith(('.mp3', '.wav')) else "image",
                    "added_date": str(datetime.now())
                }

                if "media_library" not in self.project_data:
                    self.project_data["media_library"] = {"items": []}
                self.project_data["media_library"]["items"].append(media_item)

            self._add_to_undo_stack("Add Media", f"Added {len(file_paths)} media files")
            print(f"✅ Added {len(file_paths)} media files to library")

        except Exception as e:
            print(f"❌ Error adding media: {e}")

    def _remove_media_from_library(self):
        """Remove selected media from the library."""
        try:
            if hasattr(self, 'media_library_list'):
                current_item = self.media_library_list.currentItem()
                if current_item:
                    # Remove from UI
                    row = self.media_library_list.row(current_item)
                    self.media_library_list.takeItem(row)

                    # Remove from project data
                    if "media_library" in self.project_data and "items" in self.project_data["media_library"]:
                        if row < len(self.project_data["media_library"]["items"]):
                            removed_item = self.project_data["media_library"]["items"].pop(row)
                            self._add_to_undo_stack("Remove Media", f"Removed {removed_item['name']}")
                            print(f"✅ Removed {removed_item['name']} from library")

        except Exception as e:
            print(f"❌ Error removing media: {e}")

    def _add_timeline_track(self):
        """Add a new track to the timeline."""
        try:
            if hasattr(self, 'timeline_tracks'):
                track_count = self.timeline_tracks.count()
                track_name = f"🎵 Audio Track {track_count - 1}"  # -1 because first track is video
                self.timeline_tracks.addItem(track_name)

                # Add to project data
                if "timeline" not in self.project_data:
                    self.project_data["timeline"] = {"tracks": []}

                track_data = {
                    "id": f"track_{track_count}",
                    "name": track_name,
                    "type": "audio",
                    "clips": []
                }
                self.project_data["timeline"]["tracks"].append(track_data)

                self._add_to_undo_stack("Add Track", f"Added {track_name}")
                print(f"✅ Added {track_name}")

        except Exception as e:
            print(f"❌ Error adding timeline track: {e}")

    def _undo_action(self):
        """Undo the last action."""
        try:
            if self.undo_stack:
                action = self.undo_stack.pop()
                self.redo_stack.append(action)

                # Update status
                if hasattr(self, 'undo_status_label'):
                    if self.undo_stack:
                        self.undo_status_label.setText(f"Can undo: {self.undo_stack[-1]['description']}")
                    else:
                        self.undo_status_label.setText("Ready")

                print(f"✅ Undone: {action['description']}")

        except Exception as e:
            print(f"❌ Error undoing action: {e}")

    def _redo_action(self):
        """Redo the last undone action."""
        try:
            if self.redo_stack:
                action = self.redo_stack.pop()
                self.undo_stack.append(action)

                # Update status
                if hasattr(self, 'undo_status_label'):
                    self.undo_status_label.setText(f"Can undo: {action['description']}")

                print(f"✅ Redone: {action['description']}")

        except Exception as e:
            print(f"❌ Error redoing action: {e}")

    def _add_to_undo_stack(self, action_type, description):
        """Add an action to the undo stack."""
        try:
            from datetime import datetime

            action = {
                "type": action_type,
                "description": description,
                "timestamp": datetime.now(),
                "project_state": self.project_data.copy()
            }

            self.undo_stack.append(action)

            # Limit undo history
            if len(self.undo_stack) > self.max_undo_history:
                self.undo_stack.pop(0)

            # Clear redo stack when new action is added
            self.redo_stack.clear()

            # Update status
            if hasattr(self, 'undo_status_label'):
                self.undo_status_label.setText(f"Can undo: {description}")

        except Exception as e:
            print(f"❌ Error adding to undo stack: {e}")

    def _show_project_manager(self):
        """Show the project manager tab."""
        self._switch_to_tab("Project")

    def _show_timeline_editor(self):
        """Show the timeline editor tab."""
        self._switch_to_tab("Timeline")

    def _show_workflow_builder(self):
        """Show the workflow builder (placeholder)."""
        QMessageBox.information(self, "Workflow Builder", "Workflow Builder feature coming soon!")

    def _show_audio_mixer(self):
        """Show the audio mixer tab."""
        self._switch_to_tab("Audio")

    def _switch_to_tab(self, tab_name):
        """Switch to a specific tab in the interface."""
        try:
            # Find and switch to the specified tab
            central_widget = self.centralWidget()
            if central_widget:
                # Look for tab widgets in the interface
                tab_widgets = central_widget.findChildren(QTabWidget)
                for tab_widget in tab_widgets:
                    for i in range(tab_widget.count()):
                        if tab_widget.tabText(i) == tab_name:
                            tab_widget.setCurrentIndex(i)
                            print(f"✅ Switched to {tab_name} tab")
                            return

            print(f"⚠️ Tab '{tab_name}' not found")

        except Exception as e:
            print(f"❌ Error switching to tab: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
