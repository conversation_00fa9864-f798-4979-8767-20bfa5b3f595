"""
Professional Timeline Editor for Auto Latent Video Processor.

This module provides a professional timeline interface for precise video/audio editing,
keyframe management, and visual timeline control.
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QFrame, QLabel,
    QPushButton, QSlider, QSpinBox, QDoubleSpinBox, QComboBox, QGroupBox,
    QSplitter, QTabWidget, QListWidget, QListWidgetItem, QTreeWidget,
    QTreeWidgetItem, QHeaderView, QAbstractItemView, QMenu, QToolBar,
    QSizePolicy, QGraphicsView, QGraphicsScene, QGraphicsItem,
    QGraphicsRectItem, QGraphicsTextItem, QApplication, QColorDialog,
    QFileDialog, QMessageBox, QProgressBar, QCheckBox
)
from PySide6.QtCore import (
    Qt, Signal, Slot, QTimer, QPropertyAnimation, QEasingCurve,
    QRect, QRectF, QPointF, QSizeF, QObject, QThread, QMutex
)
from PySide6.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QPixmap, QIcon,
    QLinearGradient, QPainterPath, QPolygonF,
    QFontMetrics, QCursor, QPalette, QAction
)


class TrackType(Enum):
    """Types of timeline tracks."""
    VIDEO = "video"
    AUDIO = "audio"
    SUBTITLE = "subtitle"
    EFFECT = "effect"


class ClipType(Enum):
    """Types of clips."""
    VIDEO_CLIP = "video_clip"
    AUDIO_CLIP = "audio_clip"
    IMAGE_CLIP = "image_clip"
    GENERATED_CLIP = "generated_clip"
    EFFECT_CLIP = "effect_clip"
    TRANSITION_CLIP = "transition_clip"


class PlaybackState(Enum):
    """Playback states."""
    STOPPED = "stopped"
    PLAYING = "playing"
    PAUSED = "paused"


@dataclass
class TimelineClip:
    """Represents a clip on the timeline."""
    id: str
    name: str
    clip_type: ClipType
    start_time: float  # seconds
    duration: float    # seconds
    track_index: int
    file_path: str = ""
    color: QColor = None
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if self.color is None:
            # Default colors based on clip type
            color_map = {
                ClipType.VIDEO_CLIP: QColor(100, 150, 255),
                ClipType.AUDIO_CLIP: QColor(150, 255, 100),
                ClipType.IMAGE_CLIP: QColor(255, 150, 100),
                ClipType.GENERATED_CLIP: QColor(200, 100, 255),
                ClipType.EFFECT_CLIP: QColor(255, 100, 150),
                ClipType.TRANSITION_CLIP: QColor(255, 200, 100)
            }
            self.color = color_map.get(self.clip_type, QColor(128, 128, 128))
    
    @property
    def end_time(self) -> float:
        """Get the end time of the clip."""
        return self.start_time + self.duration


@dataclass
class TimelineTrack:
    """Represents a track on the timeline."""
    id: str
    name: str
    track_type: TrackType
    index: int = 0
    height: int = 50
    muted: bool = False
    locked: bool = False
    visible: bool = True
    clips: List[TimelineClip] = field(default_factory=list)
    
    def add_clip(self, clip: TimelineClip) -> bool:
        """Add a clip to the track if there's no overlap."""
        # Check for overlaps
        for existing_clip in self.clips:
            if not (clip.end_time <= existing_clip.start_time or 
                    clip.start_time >= existing_clip.end_time):
                return False  # Overlap detected
        
        self.clips.append(clip)
        clip.track_index = self.index
        return True
    
    def remove_clip(self, clip_id: str) -> bool:
        """Remove a clip from the track."""
        for i, clip in enumerate(self.clips):
            if clip.id == clip_id:
                del self.clips[i]
                return True
        return False
    
    def get_clips_at_time(self, time: float) -> List[TimelineClip]:
        """Get all clips active at the given time."""
        active_clips = []
        for clip in self.clips:
            if clip.start_time <= time <= clip.end_time:
                active_clips.append(clip)
        return active_clips


class TimelineClipItem(QGraphicsRectItem):
    """Graphics item representing a clip on the timeline."""
    
    def __init__(self, clip: TimelineClip, pixels_per_second: float):
        super().__init__()
        self.clip = clip
        self.pixels_per_second = pixels_per_second
        self._update_geometry()
        self._setup_appearance()
        
        # Make item selectable and movable
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        
        # Tooltip
        self.setToolTip(f"{clip.name}\nDuration: {clip.duration:.2f}s")
    
    def _update_geometry(self):
        """Update the geometry based on clip timing."""
        x = self.clip.start_time * self.pixels_per_second
        width = self.clip.duration * self.pixels_per_second
        height = 40  # Fixed track height
        y = self.clip.track_index * 50  # Track spacing
        
        self.setRect(0, 0, width, height)
        self.setPos(x, y)
    
    def _setup_appearance(self):
        """Setup the visual appearance of the clip."""
        # Set brush (fill color)
        brush = QBrush(self.clip.color)
        self.setBrush(brush)
        
        # Set pen (border)
        pen = QPen(self.clip.color.darker(150), 2)
        self.setPen(pen)
    
    def paint(self, painter: QPainter, option, widget):
        """Custom paint method for enhanced appearance."""
        super().paint(painter, option, widget)
        
        # Draw clip name
        painter.setPen(QPen(QColor(255, 255, 255)))
        painter.setFont(QFont("Arial", 8))
        
        rect = self.rect()
        text_rect = QRectF(rect.x() + 5, rect.y() + 5, rect.width() - 10, rect.height() - 10)
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop, self.clip.name)
        
        # Draw duration at bottom right
        duration_text = f"{self.clip.duration:.1f}s"
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignBottom, duration_text)
    
    def itemChange(self, change, value):
        """Handle item changes (movement, selection, etc.)."""
        if change == QGraphicsItem.GraphicsItemChange.ItemPositionChange:
            # Snap to timeline grid
            new_pos = value
            snapped_x = round(new_pos.x() / (self.pixels_per_second * 0.1)) * (self.pixels_per_second * 0.1)
            snapped_y = round(new_pos.y() / 50) * 50  # Snap to track
            
            # Update clip timing
            self.clip.start_time = snapped_x / self.pixels_per_second
            self.clip.track_index = int(snapped_y / 50)
            
            return QPointF(snapped_x, snapped_y)
        
        return super().itemChange(change, value)


class TimelineRuler(QWidget):
    """Timeline ruler showing time markers."""
    
    def __init__(self, pixels_per_second: float = 100.0):
        super().__init__()
        self.pixels_per_second = pixels_per_second
        self.duration = 60.0  # Default 60 seconds
        self.current_time = 0.0
        
        self.setFixedHeight(30)
        self.setMinimumWidth(int(self.duration * self.pixels_per_second))
    
    def set_duration(self, duration: float):
        """Set the total duration of the timeline."""
        self.duration = duration
        self.setMinimumWidth(int(self.duration * self.pixels_per_second))
        self.update()
    
    def set_current_time(self, time: float):
        """Set the current playback time."""
        self.current_time = time
        self.update()
    
    def paintEvent(self, event):
        """Paint the timeline ruler."""
        painter = QPainter(self)
        painter.fillRect(self.rect(), QColor(60, 60, 60))
        
        # Draw time markers
        painter.setPen(QPen(QColor(200, 200, 200)))
        painter.setFont(QFont("Arial", 8))
        
        # Major markers (every second)
        for i in range(int(self.duration) + 1):
            x = i * self.pixels_per_second
            painter.drawLine(x, 20, x, 30)
            
            # Time label
            time_text = f"{i}s"
            painter.drawText(x + 2, 15, time_text)
        
        # Minor markers (every 0.1 second)
        painter.setPen(QPen(QColor(150, 150, 150)))
        for i in range(int(self.duration * 10) + 1):
            x = i * self.pixels_per_second * 0.1
            if i % 10 != 0:  # Skip major markers
                painter.drawLine(x, 25, x, 30)
        
        # Current time indicator
        current_x = self.current_time * self.pixels_per_second
        painter.setPen(QPen(QColor(255, 100, 100), 2))
        painter.drawLine(current_x, 0, current_x, 30)


class TimelineView(QGraphicsView):
    """Main timeline view with tracks and clips."""
    
    clip_selected = Signal(TimelineClip)
    clip_moved = Signal(TimelineClip)
    time_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        
        # Setup scene
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # Timeline properties
        self.pixels_per_second = 100.0
        self.track_height = 50
        self.num_tracks = 8
        self.duration = 60.0
        
        # Tracks and clips storage
        self.tracks: List[TimelineTrack] = []
        self.clip_items: Dict[str, TimelineClipItem] = {}
        
        # Setup view
        self._setup_view()
        self._setup_scene()
        self._create_default_tracks()
    
    def _setup_view(self):
        """Setup the graphics view."""
        self.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
    
    def _setup_scene(self):
        """Setup the graphics scene."""
        # Set scene size
        scene_width = self.duration * self.pixels_per_second
        scene_height = self.num_tracks * self.track_height
        self.scene.setSceneRect(0, 0, scene_width, scene_height)
        
        # Draw track backgrounds
        self._draw_track_backgrounds()
    
    def _create_default_tracks(self):
        """Create default tracks."""
        track_configs = [
            ("Video 1", TrackType.VIDEO),
            ("Video 2", TrackType.VIDEO),
            ("Audio 1", TrackType.AUDIO),
            ("Audio 2", TrackType.AUDIO),
            ("Audio 3", TrackType.AUDIO),
            ("Audio 4", TrackType.AUDIO),
            ("Subtitles", TrackType.SUBTITLE),
            ("Effects", TrackType.EFFECT)
        ]
        
        for i, (name, track_type) in enumerate(track_configs):
            track = TimelineTrack(
                id=str(uuid.uuid4()),
                name=name,
                track_type=track_type,
                index=i
            )
            self.tracks.append(track)
    
    def _draw_track_backgrounds(self):
        """Draw alternating track backgrounds."""
        for i in range(self.num_tracks):
            y = i * self.track_height
            color = QColor(40, 40, 40) if i % 2 == 0 else QColor(50, 50, 50)
            
            track_rect = self.scene.addRect(
                0, y, self.duration * self.pixels_per_second, self.track_height,
                QPen(QColor(30, 30, 30)), QBrush(color)
            )
            track_rect.setZValue(-1)  # Behind clips
    
    def add_clip(self, clip: TimelineClip) -> bool:
        """Add a clip to the timeline."""
        # Ensure track index is valid
        if clip.track_index >= len(self.tracks):
            return False
        
        # Try to add to track
        track = self.tracks[clip.track_index]
        if not track.add_clip(clip):
            return False
        
        # Create graphics item
        clip_item = TimelineClipItem(clip, self.pixels_per_second)
        self.clip_items[clip.id] = clip_item
        self.scene.addItem(clip_item)
        
        return True
    
    def remove_clip(self, clip_id: str):
        """Remove a clip from the timeline."""
        # Find and remove from track
        for track in self.tracks:
            if track.remove_clip(clip_id):
                break
        
        # Remove graphics item
        if clip_id in self.clip_items:
            self.scene.removeItem(self.clip_items[clip_id])
            del self.clip_items[clip_id]
    
    def get_clips_at_time(self, time: float) -> List[TimelineClip]:
        """Get all clips that are active at the given time."""
        active_clips = []
        for track in self.tracks:
            active_clips.extend(track.get_clips_at_time(time))
        return active_clips
    
    def set_current_time(self, time: float):
        """Set the current playback time and update display."""
        # Remove old time indicator
        for item in self.scene.items():
            if hasattr(item, 'is_time_indicator'):
                self.scene.removeItem(item)
        
        # Add new time indicator
        x = time * self.pixels_per_second
        time_line = self.scene.addLine(
            x, 0, x, self.num_tracks * self.track_height,
            QPen(QColor(255, 100, 100), 2)
        )
        time_line.is_time_indicator = True
        time_line.setZValue(10)  # On top
        
        self.time_changed.emit(time)


class TimelineEditor(QWidget):
    """Main timeline editor widget."""
    
    # Signals
    clip_selected = Signal(TimelineClip)
    playback_state_changed = Signal(PlaybackState)
    time_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        
        # Timeline state
        self.current_time = 0.0
        self.duration = 60.0
        self.playback_state = PlaybackState.STOPPED
        self.pixels_per_second = 100.0
        
        # Playback timer
        self.playback_timer = QTimer()
        self.playback_timer.timeout.connect(self._update_playback)
        
        # Setup UI
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """Setup the timeline editor UI."""
        layout = QVBoxLayout(self)
        
        # Toolbar
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # Timeline area
        timeline_area = self._create_timeline_area()
        layout.addWidget(timeline_area)
        
        # Controls
        controls = self._create_controls()
        layout.addWidget(controls)
    
    def _create_toolbar(self) -> QToolBar:
        """Create the timeline toolbar."""
        toolbar = QToolBar()
        
        # Playback controls
        self.play_action = QAction("▶", self)
        self.play_action.setToolTip("Play/Pause")
        self.play_action.triggered.connect(self._toggle_playback)
        toolbar.addAction(self.play_action)
        
        self.stop_action = QAction("⏹", self)
        self.stop_action.setToolTip("Stop")
        self.stop_action.triggered.connect(self._stop_playback)
        toolbar.addAction(self.stop_action)
        
        toolbar.addSeparator()
        
        # Zoom controls
        zoom_out_action = QAction("🔍-", self)
        zoom_out_action.setToolTip("Zoom Out")
        zoom_out_action.triggered.connect(self._zoom_out)
        toolbar.addAction(zoom_out_action)
        
        zoom_in_action = QAction("🔍+", self)
        zoom_in_action.setToolTip("Zoom In")
        zoom_in_action.triggered.connect(self._zoom_in)
        toolbar.addAction(zoom_in_action)
        
        return toolbar

    def _create_timeline_area(self) -> QWidget:
        """Create the main timeline area."""
        timeline_widget = QWidget()
        layout = QVBoxLayout(timeline_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # Ruler
        self.ruler = TimelineRuler(self.pixels_per_second)
        layout.addWidget(self.ruler)

        # Timeline view
        self.timeline_view = TimelineView()
        layout.addWidget(self.timeline_view)

        return timeline_widget

    def _create_controls(self) -> QWidget:
        """Create timeline controls."""
        controls = QWidget()
        layout = QHBoxLayout(controls)

        # Time display
        layout.addWidget(QLabel("Time:"))
        self.time_label = QLabel("00:00.0")
        layout.addWidget(self.time_label)

        layout.addStretch()

        # Duration control
        layout.addWidget(QLabel("Duration:"))
        self.duration_spin = QDoubleSpinBox()
        self.duration_spin.setRange(1.0, 3600.0)
        self.duration_spin.setValue(self.duration)
        self.duration_spin.setSuffix(" s")
        self.duration_spin.valueChanged.connect(self._set_duration)
        layout.addWidget(self.duration_spin)

        return controls

    def _connect_signals(self):
        """Connect internal signals."""
        self.timeline_view.time_changed.connect(self._on_time_changed)
        self.timeline_view.clip_selected.connect(self.clip_selected)

    def _toggle_playback(self):
        """Toggle playback state."""
        if self.playback_state == PlaybackState.PLAYING:
            self._pause_playback()
        else:
            self._start_playback()

    def _start_playback(self):
        """Start playback."""
        self.playback_state = PlaybackState.PLAYING
        self.play_action.setText("⏸")
        self.playback_timer.start(33)  # ~30 FPS
        self.playback_state_changed.emit(self.playback_state)

    def _pause_playback(self):
        """Pause playback."""
        self.playback_state = PlaybackState.PAUSED
        self.play_action.setText("▶")
        self.playback_timer.stop()
        self.playback_state_changed.emit(self.playback_state)

    def _stop_playback(self):
        """Stop playback."""
        self.playback_state = PlaybackState.STOPPED
        self.play_action.setText("▶")
        self.playback_timer.stop()
        self.current_time = 0.0
        self._update_time_display()
        self.timeline_view.set_current_time(self.current_time)
        self.playback_state_changed.emit(self.playback_state)

    def _update_playback(self):
        """Update playback position."""
        self.current_time += 0.033  # 33ms increment

        if self.current_time >= self.duration:
            self._stop_playback()
            return

        self._update_time_display()
        self.timeline_view.set_current_time(self.current_time)
        self.ruler.set_current_time(self.current_time)

    def _update_time_display(self):
        """Update the time display."""
        minutes = int(self.current_time // 60)
        seconds = self.current_time % 60
        self.time_label.setText(f"{minutes:02d}:{seconds:05.2f}")
        self.time_changed.emit(self.current_time)

    def _on_time_changed(self, time: float):
        """Handle time change from timeline view."""
        self.current_time = time
        self._update_time_display()

    def _set_duration(self, duration: float):
        """Set the timeline duration."""
        self.duration = duration
        self.ruler.set_duration(duration)
        self.timeline_view.duration = duration
        self.timeline_view._setup_scene()

    def _zoom_in(self):
        """Zoom in on the timeline."""
        self.pixels_per_second *= 1.5
        self._update_zoom()

    def _zoom_out(self):
        """Zoom out on the timeline."""
        self.pixels_per_second /= 1.5
        self._update_zoom()

    def _update_zoom(self):
        """Update zoom level."""
        self.ruler.pixels_per_second = self.pixels_per_second
        self.timeline_view.pixels_per_second = self.pixels_per_second

        # Update all clip items
        for clip_item in self.timeline_view.clip_items.values():
            clip_item.pixels_per_second = self.pixels_per_second
            clip_item._update_geometry()

        self.ruler.update()
        self.timeline_view._setup_scene()

    def add_media_clip(self, file_path: str, clip_type: ClipType, track_index: int = 0) -> bool:
        """Add a media clip to the timeline."""
        clip = TimelineClip(
            id=str(uuid.uuid4()),
            name=os.path.basename(file_path),
            clip_type=clip_type,
            start_time=self.current_time,
            duration=10.0,  # Default duration, should be detected from file
            track_index=track_index,
            file_path=file_path
        )

        return self.timeline_view.add_clip(clip)

    def get_current_clips(self) -> List[TimelineClip]:
        """Get clips active at current time."""
        return self.timeline_view.get_clips_at_time(self.current_time)

    def get_tracks(self) -> List[TimelineTrack]:
        """Get all tracks."""
        return self.timeline_view.tracks

    def get_all_clips(self) -> List[TimelineClip]:
        """Get all clips from all tracks."""
        all_clips = []
        for track in self.timeline_view.tracks:
            all_clips.extend(track.clips)
        return all_clips
