"""
Advanced Audio System for Auto Latent Video Processor.

This module provides comprehensive audio processing including cleanup, music integration,
sound effects, multi-track mixing, and voice modulation capabilities.
"""

import numpy as np
import logging
import json
import os
from typing import Dict, Any, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import threading
import time
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFrame,
    QLabel, QPushButton, QSlider, QSpinBox, QDoubleSpinBox,
    QComboBox, QCheckBox, QGroupBox, QTabWidget, QScrollArea,
    QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem,
    QProgressBar, QSplitter, QDial, QLCDNumber, QTextEdit,
    QDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt, Signal, QObject, QThread, QTimer, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QIcon

# Audio processing imports (would be actual libraries in production)
try:
    import librosa
    import soundfile as sf
    from scipy import signal
    from scipy.io import wavfile
    AUDIO_LIBS_AVAILABLE = True
except ImportError:
    # Mock for testing/development
    librosa = None
    sf = None
    signal = None
    wavfile = None
    AUDIO_LIBS_AVAILABLE = False
    logging.warning("Audio processing libraries not available. Using mock implementations.")


class AudioEffectType(Enum):
    """Types of audio effects."""
    NOISE_REDUCTION = "noise_reduction"
    ECHO_REMOVAL = "echo_removal"
    REVERB = "reverb"
    CHORUS = "chorus"
    DISTORTION = "distortion"
    COMPRESSOR = "compressor"
    EQUALIZER = "equalizer"
    PITCH_SHIFT = "pitch_shift"
    TIME_STRETCH = "time_stretch"
    VOICE_MODULATION = "voice_modulation"


class AudioFormat(Enum):
    """Supported audio formats."""
    WAV = "wav"
    MP3 = "mp3"
    FLAC = "flac"
    AAC = "aac"
    OGG = "ogg"


@dataclass
class AudioClip:
    """Represents an audio clip with metadata and processing information."""
    id: str
    name: str
    file_path: str
    start_time: float = 0.0
    duration: float = 0.0
    volume: float = 1.0
    pan: float = 0.0  # -1.0 (left) to 1.0 (right)
    muted: bool = False
    solo: bool = False
    effects: List[Dict[str, Any]] = field(default_factory=list)
    fade_in: float = 0.0
    fade_out: float = 0.0
    
    # Audio data (loaded when needed)
    audio_data: Optional[np.ndarray] = None
    sample_rate: Optional[int] = None
    
    def load_audio(self) -> bool:
        """Load audio data from file."""
        try:
            if AUDIO_LIBS_AVAILABLE and librosa:
                self.audio_data, self.sample_rate = librosa.load(self.file_path, sr=None)
                if len(self.audio_data.shape) == 1:
                    # Convert mono to stereo
                    self.audio_data = np.stack([self.audio_data, self.audio_data])
                self.duration = len(self.audio_data[0]) / self.sample_rate
                return True
            else:
                # Mock implementation
                self.sample_rate = 44100
                self.duration = 10.0  # Mock 10 second duration
                self.audio_data = np.random.random((2, int(self.sample_rate * self.duration))) * 0.1
                return True
        except Exception as e:
            logging.error(f"Failed to load audio {self.file_path}: {e}")
            return False
    
    def apply_volume(self, data: np.ndarray) -> np.ndarray:
        """Apply volume adjustment to audio data."""
        return data * self.volume
    
    def apply_pan(self, data: np.ndarray) -> np.ndarray:
        """Apply panning to stereo audio data."""
        if len(data.shape) != 2 or data.shape[0] != 2:
            return data
        
        # Pan: -1.0 = full left, 0.0 = center, 1.0 = full right
        left_gain = np.sqrt((1.0 - self.pan) / 2.0) if self.pan >= 0 else 1.0
        right_gain = np.sqrt((1.0 + self.pan) / 2.0) if self.pan <= 0 else 1.0
        
        result = data.copy()
        result[0] *= left_gain
        result[1] *= right_gain
        return result
    
    def apply_fade(self, data: np.ndarray) -> np.ndarray:
        """Apply fade in/out to audio data."""
        if self.fade_in <= 0 and self.fade_out <= 0:
            return data
        
        result = data.copy()
        samples = data.shape[-1]
        
        # Fade in
        if self.fade_in > 0:
            fade_samples = int(self.fade_in * self.sample_rate)
            fade_samples = min(fade_samples, samples // 2)
            fade_curve = np.linspace(0, 1, fade_samples)
            if len(data.shape) == 2:
                result[:, :fade_samples] *= fade_curve
            else:
                result[:fade_samples] *= fade_curve
        
        # Fade out
        if self.fade_out > 0:
            fade_samples = int(self.fade_out * self.sample_rate)
            fade_samples = min(fade_samples, samples // 2)
            fade_curve = np.linspace(1, 0, fade_samples)
            if len(data.shape) == 2:
                result[:, -fade_samples:] *= fade_curve
            else:
                result[-fade_samples:] *= fade_curve
        
        return result


@dataclass
class AudioTrack:
    """Represents an audio track containing multiple clips."""
    id: str
    name: str
    clips: List[AudioClip] = field(default_factory=list)
    volume: float = 1.0
    pan: float = 0.0
    muted: bool = False
    solo: bool = False
    effects: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_clip(self, clip: AudioClip, start_time: float):
        """Add a clip to the track at specified time."""
        clip.start_time = start_time
        self.clips.append(clip)
        self.clips.sort(key=lambda c: c.start_time)
    
    def remove_clip(self, clip_id: str):
        """Remove a clip from the track."""
        self.clips = [c for c in self.clips if c.id != clip_id]
    
    def get_clips_at_time(self, time: float) -> List[AudioClip]:
        """Get all clips playing at a specific time."""
        active_clips = []
        for clip in self.clips:
            if clip.start_time <= time <= clip.start_time + clip.duration:
                active_clips.append(clip)
        return active_clips


class AudioProcessor:
    """Core audio processing engine."""
    
    def __init__(self):
        self.sample_rate = 44100
        self.buffer_size = 1024
        self.effects_library = self._create_effects_library()
    
    def _create_effects_library(self) -> Dict[str, Callable]:
        """Create library of audio effects."""
        return {
            AudioEffectType.NOISE_REDUCTION.value: self._noise_reduction,
            AudioEffectType.ECHO_REMOVAL.value: self._echo_removal,
            AudioEffectType.REVERB.value: self._reverb,
            AudioEffectType.CHORUS.value: self._chorus,
            AudioEffectType.COMPRESSOR.value: self._compressor,
            AudioEffectType.EQUALIZER.value: self._equalizer,
            AudioEffectType.PITCH_SHIFT.value: self._pitch_shift,
            AudioEffectType.TIME_STRETCH.value: self._time_stretch,
            AudioEffectType.VOICE_MODULATION.value: self._voice_modulation
        }
    
    def apply_effect(self, audio_data: np.ndarray, effect_type: str, 
                    parameters: Dict[str, Any]) -> np.ndarray:
        """Apply an audio effect to audio data."""
        if effect_type in self.effects_library:
            return self.effects_library[effect_type](audio_data, parameters)
        else:
            logging.warning(f"Unknown effect type: {effect_type}")
            return audio_data
    
    def _noise_reduction(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Apply noise reduction."""
        strength = params.get('strength', 0.5)
        
        if AUDIO_LIBS_AVAILABLE and librosa:
            # Use spectral gating for noise reduction
            stft = librosa.stft(data)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # Simple noise gate based on magnitude threshold
            threshold = np.percentile(magnitude, 20) * (1 + strength)
            mask = magnitude > threshold
            
            # Apply mask
            cleaned_stft = magnitude * mask * np.exp(1j * phase)
            return librosa.istft(cleaned_stft)
        else:
            # Mock implementation - simple high-pass filter
            if len(data.shape) == 2:
                return data * (1 - strength * 0.1)
            else:
                return data * (1 - strength * 0.1)
    
    def _echo_removal(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Remove echo from audio."""
        strength = params.get('strength', 0.5)
        
        # Mock implementation - simple delay and subtract
        delay_samples = int(0.1 * self.sample_rate)  # 100ms delay
        
        if len(data.shape) == 2:
            result = data.copy()
            if data.shape[1] > delay_samples:
                delayed = np.zeros_like(data)
                delayed[:, delay_samples:] = data[:, :-delay_samples]
                result = data - delayed * strength * 0.3
        else:
            result = data.copy()
            if len(data) > delay_samples:
                delayed = np.zeros_like(data)
                delayed[delay_samples:] = data[:-delay_samples]
                result = data - delayed * strength * 0.3
        
        return result
    
    def _reverb(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Add reverb effect."""
        room_size = params.get('room_size', 0.5)
        damping = params.get('damping', 0.5)
        wet_level = params.get('wet_level', 0.3)
        
        # Simple reverb using multiple delays
        delays = [0.03, 0.05, 0.07, 0.09, 0.11]  # seconds
        gains = [0.6, 0.5, 0.4, 0.3, 0.2]
        
        reverb_signal = np.zeros_like(data)
        
        for delay, gain in zip(delays, gains):
            delay_samples = int(delay * room_size * self.sample_rate)
            if len(data.shape) == 2:
                if data.shape[1] > delay_samples:
                    delayed = np.zeros_like(data)
                    delayed[:, delay_samples:] = data[:, :-delay_samples]
                    reverb_signal += delayed * gain * (1 - damping)
            else:
                if len(data) > delay_samples:
                    delayed = np.zeros_like(data)
                    delayed[delay_samples:] = data[:-delay_samples]
                    reverb_signal += delayed * gain * (1 - damping)
        
        return data + reverb_signal * wet_level
    
    def _chorus(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Add chorus effect."""
        rate = params.get('rate', 1.0)  # Hz
        depth = params.get('depth', 0.002)  # seconds
        mix = params.get('mix', 0.5)
        
        # Create modulated delay
        samples = data.shape[-1] if len(data.shape) == 2 else len(data)
        time_axis = np.arange(samples) / self.sample_rate
        
        # LFO (Low Frequency Oscillator)
        lfo = np.sin(2 * np.pi * rate * time_axis)
        delay_modulation = depth * lfo
        
        # Apply modulated delay (simplified)
        chorus_signal = np.zeros_like(data)
        base_delay = int(0.02 * self.sample_rate)  # 20ms base delay
        
        for i in range(samples):
            delay_samples = base_delay + int(delay_modulation[i] * self.sample_rate)
            if i >= delay_samples:
                if len(data.shape) == 2:
                    chorus_signal[:, i] = data[:, i - delay_samples]
                else:
                    chorus_signal[i] = data[i - delay_samples]
        
        return data * (1 - mix) + chorus_signal * mix
    
    def _compressor(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Apply dynamic range compression."""
        threshold = params.get('threshold', -20)  # dB
        ratio = params.get('ratio', 4.0)
        attack = params.get('attack', 0.003)  # seconds
        release = params.get('release', 0.1)  # seconds
        
        # Convert to dB
        data_db = 20 * np.log10(np.abs(data) + 1e-10)
        
        # Apply compression
        compressed_db = np.where(
            data_db > threshold,
            threshold + (data_db - threshold) / ratio,
            data_db
        )
        
        # Convert back to linear
        gain_reduction = compressed_db - data_db
        gain_linear = 10 ** (gain_reduction / 20)
        
        return data * gain_linear
    
    def _equalizer(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Apply equalizer."""
        bands = params.get('bands', {
            '60': 0,    # Bass
            '170': 0,   # Low mid
            '350': 0,   # Mid
            '1000': 0,  # High mid
            '3500': 0,  # Presence
            '10000': 0  # Treble
        })
        
        if not AUDIO_LIBS_AVAILABLE:
            # Mock implementation
            return data
        
        # Apply EQ bands (simplified)
        result = data.copy()
        for freq_str, gain_db in bands.items():
            if gain_db != 0:
                freq = float(freq_str)
                # Simple peaking filter approximation
                gain_linear = 10 ** (gain_db / 20)
                result *= gain_linear
        
        return result
    
    def _pitch_shift(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Shift pitch without changing tempo."""
        semitones = params.get('semitones', 0)
        
        if semitones == 0:
            return data
        
        if AUDIO_LIBS_AVAILABLE and librosa:
            return librosa.effects.pitch_shift(data, sr=self.sample_rate, n_steps=semitones)
        else:
            # Mock implementation - simple resampling
            shift_factor = 2 ** (semitones / 12)
            return data * shift_factor  # This is not correct but serves as mock
    
    def _time_stretch(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Stretch time without changing pitch."""
        rate = params.get('rate', 1.0)
        
        if rate == 1.0:
            return data
        
        if AUDIO_LIBS_AVAILABLE and librosa:
            return librosa.effects.time_stretch(data, rate=rate)
        else:
            # Mock implementation
            return data
    
    def _voice_modulation(self, data: np.ndarray, params: Dict[str, Any]) -> np.ndarray:
        """Apply voice modulation effects."""
        effect_type = params.get('type', 'robot')
        intensity = params.get('intensity', 0.5)
        
        if effect_type == 'robot':
            # Ring modulation for robot voice
            modulation_freq = 30 + intensity * 70  # 30-100 Hz
            samples = data.shape[-1] if len(data.shape) == 2 else len(data)
            time_axis = np.arange(samples) / self.sample_rate
            modulator = np.sin(2 * np.pi * modulation_freq * time_axis)
            
            if len(data.shape) == 2:
                return data * modulator[np.newaxis, :]
            else:
                return data * modulator
        
        elif effect_type == 'chipmunk':
            # High pitch shift
            return self._pitch_shift(data, {'semitones': 8 * intensity})
        
        elif effect_type == 'demon':
            # Low pitch shift with distortion
            pitched = self._pitch_shift(data, {'semitones': -8 * intensity})
            # Add some distortion
            return np.tanh(pitched * (1 + intensity * 2))
        
        return data


class AudioMixer:
    """Multi-track audio mixer."""
    
    def __init__(self, sample_rate: int = 44100):
        self.sample_rate = sample_rate
        self.tracks: List[AudioTrack] = []
        self.master_volume = 1.0
        self.processor = AudioProcessor()
    
    def add_track(self, track: AudioTrack):
        """Add a track to the mixer."""
        self.tracks.append(track)
    
    def remove_track(self, track_id: str):
        """Remove a track from the mixer."""
        self.tracks = [t for t in self.tracks if t.id != track_id]
    
    def get_track(self, track_id: str) -> Optional[AudioTrack]:
        """Get a track by ID."""
        for track in self.tracks:
            if track.id == track_id:
                return track
        return None
    
    def mix_at_time(self, start_time: float, duration: float) -> np.ndarray:
        """Mix all tracks for a specific time range."""
        samples = int(duration * self.sample_rate)
        mixed_audio = np.zeros((2, samples))
        
        # Check if any track is soloed
        solo_tracks = [t for t in self.tracks if t.solo and not t.muted]
        active_tracks = solo_tracks if solo_tracks else [t for t in self.tracks if not t.muted]
        
        for track in active_tracks:
            track_audio = self._render_track(track, start_time, duration)
            if track_audio is not None:
                mixed_audio += track_audio
        
        # Apply master volume
        mixed_audio *= self.master_volume
        
        # Prevent clipping
        max_val = np.max(np.abs(mixed_audio))
        if max_val > 1.0:
            mixed_audio /= max_val
        
        return mixed_audio
    
    def _render_track(self, track: AudioTrack, start_time: float, duration: float) -> Optional[np.ndarray]:
        """Render a single track for the specified time range."""
        samples = int(duration * self.sample_rate)
        track_audio = np.zeros((2, samples))
        
        # Get clips that overlap with the time range
        end_time = start_time + duration
        active_clips = []
        
        for clip in track.clips:
            clip_end = clip.start_time + clip.duration
            if clip.start_time < end_time and clip_end > start_time:
                active_clips.append(clip)
        
        # Render each active clip
        for clip in active_clips:
            if clip.muted:
                continue
            
            if not clip.audio_data:
                if not clip.load_audio():
                    continue
            
            # Calculate clip timing within the render range
            clip_start_in_range = max(0, clip.start_time - start_time)
            clip_end_in_range = min(duration, clip.start_time + clip.duration - start_time)
            
            if clip_start_in_range >= clip_end_in_range:
                continue
            
            # Calculate sample indices
            start_sample = int(clip_start_in_range * self.sample_rate)
            end_sample = int(clip_end_in_range * self.sample_rate)
            
            # Get clip audio data
            clip_data = clip.audio_data.copy()
            
            # Apply clip effects
            clip_data = clip.apply_volume(clip_data)
            clip_data = clip.apply_pan(clip_data)
            clip_data = clip.apply_fade(clip_data)
            
            # Apply track effects
            for effect in track.effects:
                clip_data = self.processor.apply_effect(
                    clip_data, effect['type'], effect.get('parameters', {})
                )
            
            # Mix into track audio
            clip_samples = end_sample - start_sample
            if clip_data.shape[-1] >= clip_samples:
                track_audio[:, start_sample:end_sample] += clip_data[:, :clip_samples]
        
        # Apply track volume and pan
        track_audio *= track.volume
        
        # Apply track pan
        if track.pan != 0:
            left_gain = np.sqrt((1.0 - track.pan) / 2.0) if track.pan >= 0 else 1.0
            right_gain = np.sqrt((1.0 + track.pan) / 2.0) if track.pan <= 0 else 1.0
            track_audio[0] *= left_gain
            track_audio[1] *= right_gain
        
        return track_audio
    
    def export_audio(self, file_path: str, start_time: float = 0, 
                    duration: Optional[float] = None, format: AudioFormat = AudioFormat.WAV) -> bool:
        """Export mixed audio to file."""
        try:
            if duration is None:
                # Calculate total duration
                max_end_time = 0
                for track in self.tracks:
                    for clip in track.clips:
                        max_end_time = max(max_end_time, clip.start_time + clip.duration)
                duration = max_end_time - start_time
            
            # Mix audio
            mixed_audio = self.mix_at_time(start_time, duration)
            
            # Export based on format
            if format == AudioFormat.WAV:
                if AUDIO_LIBS_AVAILABLE and sf:
                    sf.write(file_path, mixed_audio.T, self.sample_rate)
                else:
                    # Mock export
                    logging.info(f"Mock export to {file_path}")
                return True
            else:
                logging.warning(f"Export format {format.value} not yet implemented")
                return False
                
        except Exception as e:
            logging.error(f"Failed to export audio: {e}")
            return False


class AudioEffectWidget(QWidget):
    """Widget for configuring audio effects."""

    effect_changed = Signal(str, dict)  # effect_type, parameters

    def __init__(self, effect_type: AudioEffectType, parameters: Dict[str, Any] = None):
        super().__init__()
        self.effect_type = effect_type
        self.parameters = parameters or {}
        self.controls = {}

        self._setup_ui()
        self._create_controls()

    def _setup_ui(self):
        """Setup the effect widget UI."""
        layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()

        self.title_label = QLabel(self.effect_type.value.replace('_', ' ').title())
        self.title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        header_layout.addWidget(self.title_label)

        header_layout.addStretch()

        self.enabled_checkbox = QCheckBox("Enabled")
        self.enabled_checkbox.setChecked(True)
        self.enabled_checkbox.stateChanged.connect(self._on_enabled_changed)
        header_layout.addWidget(self.enabled_checkbox)

        layout.addLayout(header_layout)

        # Controls container
        self.controls_widget = QWidget()
        self.controls_layout = QGridLayout(self.controls_widget)
        layout.addWidget(self.controls_widget)

    def _create_controls(self):
        """Create controls based on effect type."""
        if self.effect_type == AudioEffectType.NOISE_REDUCTION:
            self._add_slider_control("strength", "Strength", 0.0, 1.0, 0.5)

        elif self.effect_type == AudioEffectType.ECHO_REMOVAL:
            self._add_slider_control("strength", "Strength", 0.0, 1.0, 0.5)

        elif self.effect_type == AudioEffectType.REVERB:
            self._add_slider_control("room_size", "Room Size", 0.0, 1.0, 0.5)
            self._add_slider_control("damping", "Damping", 0.0, 1.0, 0.5)
            self._add_slider_control("wet_level", "Wet Level", 0.0, 1.0, 0.3)

        elif self.effect_type == AudioEffectType.CHORUS:
            self._add_slider_control("rate", "Rate (Hz)", 0.1, 5.0, 1.0)
            self._add_slider_control("depth", "Depth", 0.001, 0.01, 0.002)
            self._add_slider_control("mix", "Mix", 0.0, 1.0, 0.5)

        elif self.effect_type == AudioEffectType.COMPRESSOR:
            self._add_slider_control("threshold", "Threshold (dB)", -40, 0, -20)
            self._add_slider_control("ratio", "Ratio", 1.0, 10.0, 4.0)
            self._add_slider_control("attack", "Attack (s)", 0.001, 0.1, 0.003)
            self._add_slider_control("release", "Release (s)", 0.01, 1.0, 0.1)

        elif self.effect_type == AudioEffectType.EQUALIZER:
            self._create_equalizer_controls()

        elif self.effect_type == AudioEffectType.PITCH_SHIFT:
            self._add_slider_control("semitones", "Semitones", -12, 12, 0)

        elif self.effect_type == AudioEffectType.VOICE_MODULATION:
            self._create_voice_modulation_controls()

    def _add_slider_control(self, param_name: str, label: str, min_val: float,
                           max_val: float, default_val: float):
        """Add a slider control for a parameter."""
        row = len(self.controls)

        # Label
        label_widget = QLabel(label + ":")
        self.controls_layout.addWidget(label_widget, row, 0)

        # Slider
        slider = QSlider(Qt.Orientation.Horizontal)
        slider.setRange(0, 1000)
        slider.setValue(int((default_val - min_val) / (max_val - min_val) * 1000))
        slider.valueChanged.connect(lambda v, p=param_name: self._on_slider_changed(p, v, min_val, max_val))
        self.controls_layout.addWidget(slider, row, 1)

        # Value display
        value_label = QLabel(f"{default_val:.3f}")
        value_label.setMinimumWidth(60)
        self.controls_layout.addWidget(value_label, row, 2)

        self.controls[param_name] = {
            'slider': slider,
            'label': value_label,
            'min': min_val,
            'max': max_val
        }

        # Set initial parameter value
        self.parameters[param_name] = default_val

    def _create_equalizer_controls(self):
        """Create equalizer band controls."""
        bands = ['60', '170', '350', '1000', '3500', '10000']
        band_names = ['Bass', 'Low Mid', 'Mid', 'High Mid', 'Presence', 'Treble']

        if 'bands' not in self.parameters:
            self.parameters['bands'] = {band: 0 for band in bands}

        for i, (band, name) in enumerate(zip(bands, band_names)):
            row = i

            # Label
            label_widget = QLabel(f"{name} ({band} Hz):")
            self.controls_layout.addWidget(label_widget, row, 0)

            # Slider
            slider = QSlider(Qt.Orientation.Horizontal)
            slider.setRange(-120, 120)  # -12dB to +12dB
            slider.setValue(0)
            slider.valueChanged.connect(lambda v, b=band: self._on_eq_band_changed(b, v))
            self.controls_layout.addWidget(slider, row, 1)

            # Value display
            value_label = QLabel("0.0 dB")
            value_label.setMinimumWidth(60)
            self.controls_layout.addWidget(value_label, row, 2)

            self.controls[f'eq_{band}'] = {
                'slider': slider,
                'label': value_label,
                'band': band
            }

    def _create_voice_modulation_controls(self):
        """Create voice modulation controls."""
        # Type selector
        type_label = QLabel("Type:")
        self.controls_layout.addWidget(type_label, 0, 0)

        type_combo = QComboBox()
        type_combo.addItems(['robot', 'chipmunk', 'demon'])
        type_combo.currentTextChanged.connect(self._on_voice_type_changed)
        self.controls_layout.addWidget(type_combo, 0, 1, 1, 2)

        # Intensity slider
        self._add_slider_control("intensity", "Intensity", 0.0, 1.0, 0.5)

        self.parameters['type'] = 'robot'
        self.controls['voice_type'] = type_combo

    def _on_slider_changed(self, param_name: str, value: int, min_val: float, max_val: float):
        """Handle slider value changes."""
        normalized = value / 1000.0
        actual_value = min_val + normalized * (max_val - min_val)

        self.parameters[param_name] = actual_value
        self.controls[param_name]['label'].setText(f"{actual_value:.3f}")

        self._emit_change()

    def _on_eq_band_changed(self, band: str, value: int):
        """Handle EQ band changes."""
        db_value = value / 10.0  # Convert to dB
        self.parameters['bands'][band] = db_value

        control_key = f'eq_{band}'
        if control_key in self.controls:
            self.controls[control_key]['label'].setText(f"{db_value:.1f} dB")

        self._emit_change()

    def _on_voice_type_changed(self, voice_type: str):
        """Handle voice modulation type changes."""
        self.parameters['type'] = voice_type
        self._emit_change()

    def _on_enabled_changed(self, state: int):
        """Handle enabled state changes."""
        enabled = state == Qt.CheckState.Checked.value
        self.controls_widget.setEnabled(enabled)
        self._emit_change()

    def _emit_change(self):
        """Emit effect changed signal."""
        if self.enabled_checkbox.isChecked():
            self.effect_changed.emit(self.effect_type.value, self.parameters.copy())
        else:
            self.effect_changed.emit(self.effect_type.value, {})

    def is_enabled(self) -> bool:
        """Check if effect is enabled."""
        return self.enabled_checkbox.isChecked()

    def set_parameters(self, parameters: Dict[str, Any]):
        """Set effect parameters."""
        self.parameters.update(parameters)

        # Update UI controls
        for param_name, value in parameters.items():
            if param_name in self.controls:
                control = self.controls[param_name]
                if 'slider' in control:
                    min_val = control['min']
                    max_val = control['max']
                    normalized = (value - min_val) / (max_val - min_val)
                    control['slider'].setValue(int(normalized * 1000))
                    control['label'].setText(f"{value:.3f}")


class AudioMixerWidget(QWidget):
    """Main audio mixer widget with multiple tracks."""

    # Signals
    audio_exported = Signal(str)  # file_path

    def __init__(self):
        super().__init__()

        self.mixer = AudioMixer()
        self.track_widgets: Dict[str, 'AudioTrackWidget'] = {}

        self._setup_ui()
        self._create_default_tracks()

    def _setup_ui(self):
        """Setup the mixer UI."""
        layout = QVBoxLayout(self)

        # Toolbar
        toolbar_layout = QHBoxLayout()

        add_track_btn = QPushButton("Add Track")
        add_track_btn.clicked.connect(self._add_track)
        toolbar_layout.addWidget(add_track_btn)

        toolbar_layout.addStretch()

        export_btn = QPushButton("Export Audio")
        export_btn.clicked.connect(self._export_audio)
        toolbar_layout.addWidget(export_btn)

        layout.addLayout(toolbar_layout)

        # Mixer area
        mixer_scroll = QScrollArea()
        self.mixer_widget = QWidget()
        self.mixer_layout = QHBoxLayout(self.mixer_widget)
        self.mixer_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        mixer_scroll.setWidget(self.mixer_widget)
        mixer_scroll.setWidgetResizable(True)
        mixer_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        mixer_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlways)

        layout.addWidget(mixer_scroll)

        # Master section
        master_layout = self._create_master_section()
        layout.addLayout(master_layout)

    def _create_master_section(self) -> QHBoxLayout:
        """Create the master volume section."""
        layout = QHBoxLayout()

        layout.addStretch()

        # Master volume
        master_group = QGroupBox("Master")
        master_group.setFixedWidth(120)
        master_layout = QVBoxLayout(master_group)

        self.master_slider = QSlider(Qt.Orientation.Vertical)
        self.master_slider.setRange(0, 150)
        self.master_slider.setValue(100)
        self.master_slider.setFixedHeight(150)
        self.master_slider.valueChanged.connect(self._on_master_volume_changed)
        master_layout.addWidget(self.master_slider)

        self.master_label = QLabel("100%")
        self.master_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        master_layout.addWidget(self.master_label)

        layout.addWidget(master_group)

        return layout

    def _create_default_tracks(self):
        """Create default audio tracks."""
        # Main audio track
        main_track = AudioTrack(
            id="main_audio",
            name="Main Audio"
        )
        self._add_track_to_mixer(main_track)

        # Music track
        music_track = AudioTrack(
            id="music",
            name="Music"
        )
        self._add_track_to_mixer(music_track)

        # Effects track
        effects_track = AudioTrack(
            id="effects",
            name="Effects"
        )
        self._add_track_to_mixer(effects_track)

    def _add_track(self):
        """Add a new track to the mixer."""
        track_count = len(self.mixer.tracks)
        track = AudioTrack(
            id=f"track_{track_count + 1}",
            name=f"Track {track_count + 1}"
        )
        self._add_track_to_mixer(track)

    def _add_track_to_mixer(self, track: AudioTrack):
        """Add a track to the mixer and create its widget."""
        self.mixer.add_track(track)

        track_widget = AudioTrackWidget(track)
        track_widget.volume_changed.connect(self._on_track_volume_changed)
        track_widget.pan_changed.connect(self._on_track_pan_changed)
        track_widget.mute_toggled.connect(self._on_track_mute_toggled)
        track_widget.solo_toggled.connect(self._on_track_solo_toggled)

        self.mixer_layout.addWidget(track_widget)
        self.track_widgets[track.id] = track_widget

    def _on_master_volume_changed(self, value: int):
        """Handle master volume changes."""
        volume = value / 100.0
        self.mixer.master_volume = volume
        self.master_label.setText(f"{value}%")

    def _on_track_volume_changed(self, track_id: str, volume: float):
        """Handle track volume changes."""
        track = self.mixer.get_track(track_id)
        if track:
            track.volume = volume

    def _on_track_pan_changed(self, track_id: str, pan: float):
        """Handle track pan changes."""
        track = self.mixer.get_track(track_id)
        if track:
            track.pan = pan

    def _on_track_mute_toggled(self, track_id: str, muted: bool):
        """Handle track mute toggle."""
        track = self.mixer.get_track(track_id)
        if track:
            track.muted = muted

    def _on_track_solo_toggled(self, track_id: str, solo: bool):
        """Handle track solo toggle."""
        track = self.mixer.get_track(track_id)
        if track:
            track.solo = solo

    def _export_audio(self):
        """Export mixed audio to file."""
        from PySide6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Audio", "", "WAV Files (*.wav);;All Files (*)"
        )

        if file_path:
            if self.mixer.export_audio(file_path):
                self.audio_exported.emit(file_path)
                logging.info(f"Audio exported to {file_path}")
            else:
                logging.error("Failed to export audio")

    def add_audio_clip(self, track_id: str, file_path: str, start_time: float = 0.0) -> bool:
        """Add an audio clip to a track."""
        track = self.mixer.get_track(track_id)
        if not track:
            return False

        clip = AudioClip(
            id=f"clip_{len(track.clips) + 1}",
            name=os.path.basename(file_path),
            file_path=file_path
        )

        if clip.load_audio():
            track.add_clip(clip, start_time)
            return True

        return False

    def get_mixer(self) -> AudioMixer:
        """Get the audio mixer instance."""
        return self.mixer


class AudioTrackWidget(QWidget):
    """Widget representing an audio track in the mixer."""

    # Signals
    volume_changed = Signal(str, float)  # track_id, volume
    pan_changed = Signal(str, float)     # track_id, pan
    mute_toggled = Signal(str, bool)     # track_id, muted
    solo_toggled = Signal(str, bool)     # track_id, solo

    def __init__(self, track: AudioTrack):
        super().__init__()
        self.track = track

        self.setFixedWidth(120)
        self.setMinimumHeight(300)

        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Setup the track widget UI."""
        layout = QVBoxLayout(self)
        layout.setSpacing(5)

        # Track name
        self.name_label = QLabel(self.track.name)
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        layout.addWidget(self.name_label)

        # Effects button
        self.effects_btn = QPushButton("FX")
        self.effects_btn.setFixedHeight(25)
        self.effects_btn.clicked.connect(self._show_effects_dialog)
        layout.addWidget(self.effects_btn)

        # Volume fader
        volume_layout = QVBoxLayout()

        volume_label = QLabel("Vol")
        volume_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        volume_layout.addWidget(volume_label)

        self.volume_slider = QSlider(Qt.Orientation.Vertical)
        self.volume_slider.setRange(0, 150)  # 0 to +50% volume
        self.volume_slider.setValue(100)
        self.volume_slider.setFixedHeight(150)
        volume_layout.addWidget(self.volume_slider)

        self.volume_label = QLabel("100%")
        self.volume_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.volume_label.setFixedHeight(20)
        volume_layout.addWidget(self.volume_label)

        layout.addLayout(volume_layout)

        # Pan knob
        pan_layout = QVBoxLayout()

        pan_label = QLabel("Pan")
        pan_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pan_layout.addWidget(pan_label)

        self.pan_dial = QDial()
        self.pan_dial.setRange(-100, 100)
        self.pan_dial.setValue(0)
        self.pan_dial.setFixedSize(50, 50)
        pan_layout.addWidget(self.pan_dial, alignment=Qt.AlignmentFlag.AlignCenter)

        self.pan_label = QLabel("C")
        self.pan_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pan_layout.addWidget(self.pan_label)

        layout.addLayout(pan_layout)

        # Control buttons
        buttons_layout = QVBoxLayout()

        self.mute_btn = QPushButton("M")
        self.mute_btn.setCheckable(True)
        self.mute_btn.setFixedSize(30, 25)
        self.mute_btn.setStyleSheet("QPushButton:checked { background-color: red; color: white; }")
        buttons_layout.addWidget(self.mute_btn)

        self.solo_btn = QPushButton("S")
        self.solo_btn.setCheckable(True)
        self.solo_btn.setFixedSize(30, 25)
        self.solo_btn.setStyleSheet("QPushButton:checked { background-color: yellow; color: black; }")
        buttons_layout.addWidget(self.solo_btn)

        layout.addLayout(buttons_layout)

        layout.addStretch()

    def _connect_signals(self):
        """Connect internal signals."""
        self.volume_slider.valueChanged.connect(self._on_volume_changed)
        self.pan_dial.valueChanged.connect(self._on_pan_changed)
        self.mute_btn.toggled.connect(self._on_mute_toggled)
        self.solo_btn.toggled.connect(self._on_solo_toggled)

    def _on_volume_changed(self, value: int):
        """Handle volume slider changes."""
        volume = value / 100.0
        self.track.volume = volume
        self.volume_label.setText(f"{value}%")
        self.volume_changed.emit(self.track.id, volume)

    def _on_pan_changed(self, value: int):
        """Handle pan dial changes."""
        pan = value / 100.0
        self.track.pan = pan

        if pan < -0.1:
            self.pan_label.setText("L")
        elif pan > 0.1:
            self.pan_label.setText("R")
        else:
            self.pan_label.setText("C")

        self.pan_changed.emit(self.track.id, pan)

    def _on_mute_toggled(self, checked: bool):
        """Handle mute button toggle."""
        self.track.muted = checked
        self.mute_toggled.emit(self.track.id, checked)

    def _on_solo_toggled(self, checked: bool):
        """Handle solo button toggle."""
        self.track.solo = checked
        self.solo_toggled.emit(self.track.id, checked)

    def _show_effects_dialog(self):
        """Show effects configuration dialog."""
        dialog = AudioEffectsDialog(self.track, self)
        dialog.exec()


class AudioEffectsDialog(QDialog):
    """Dialog for configuring track effects."""

    def __init__(self, track: AudioTrack, parent=None):
        super().__init__(parent)
        self.track = track

        self.setWindowTitle(f"Effects - {track.name}")
        self.setModal(True)
        self.resize(500, 600)

        self._setup_ui()
        self._load_effects()

    def _setup_ui(self):
        """Setup the dialog UI."""
        layout = QVBoxLayout(self)

        # Add effect button
        add_layout = QHBoxLayout()

        self.effect_combo = QComboBox()
        for effect_type in AudioEffectType:
            self.effect_combo.addItem(effect_type.value.replace('_', ' ').title(), effect_type)
        add_layout.addWidget(self.effect_combo)

        add_btn = QPushButton("Add Effect")
        add_btn.clicked.connect(self._add_effect)
        add_layout.addWidget(add_btn)

        layout.addLayout(add_layout)

        # Effects list
        self.effects_scroll = QScrollArea()
        self.effects_widget = QWidget()
        self.effects_layout = QVBoxLayout(self.effects_widget)
        self.effects_scroll.setWidget(self.effects_widget)
        self.effects_scroll.setWidgetResizable(True)
        layout.addWidget(self.effects_scroll)

        # Buttons
        button_layout = QHBoxLayout()

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def _load_effects(self):
        """Load existing effects for the track."""
        for effect in self.track.effects:
            effect_type = AudioEffectType(effect['type'])
            parameters = effect.get('parameters', {})
            self._create_effect_widget(effect_type, parameters)

    def _add_effect(self):
        """Add a new effect to the track."""
        effect_type = self.effect_combo.currentData()
        if effect_type:
            self._create_effect_widget(effect_type)

    def _create_effect_widget(self, effect_type: AudioEffectType, parameters: Dict[str, Any] = None):
        """Create and add an effect widget."""
        effect_widget = AudioEffectWidget(effect_type, parameters)
        effect_widget.effect_changed.connect(self._on_effect_changed)

        # Add remove button
        container = QFrame()
        container.setFrameStyle(QFrame.Shape.StyledPanel)
        container_layout = QVBoxLayout(container)

        header_layout = QHBoxLayout()
        header_layout.addStretch()

        remove_btn = QPushButton("Remove")
        remove_btn.clicked.connect(lambda: self._remove_effect(container, effect_widget))
        header_layout.addWidget(remove_btn)

        container_layout.addLayout(header_layout)
        container_layout.addWidget(effect_widget)

        self.effects_layout.addWidget(container)

        # Add to track effects if new
        if parameters is None:
            effect_data = {
                'type': effect_type.value,
                'parameters': effect_widget.parameters.copy()
            }
            self.track.effects.append(effect_data)

    def _remove_effect(self, container: QFrame, effect_widget: AudioEffectWidget):
        """Remove an effect from the track."""
        # Remove from UI
        self.effects_layout.removeWidget(container)
        container.deleteLater()

        # Remove from track
        effect_type = effect_widget.effect_type.value
        self.track.effects = [e for e in self.track.effects if e['type'] != effect_type]

    def _on_effect_changed(self, effect_type: str, parameters: Dict[str, Any]):
        """Handle effect parameter changes."""
        # Update track effects
        for effect in self.track.effects:
            if effect['type'] == effect_type:
                effect['parameters'] = parameters
                break
