"""
Centralized Configuration Management for Auto Latent Video Processor.

This module provides a unified configuration system with validation, defaults,
environment variable support, and configuration file management.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field, asdict
from enum import Enum


class ConfigError(Exception):
    """Custom exception for configuration-related errors."""
    pass


class LogLevel(Enum):
    """Supported logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class ComfyUIConfig:
    """ComfyUI-related configuration."""
    url: str = "127.0.0.1:8188"
    output_base_dir: str = ""
    timeout_seconds: int = 300
    poll_interval_seconds: float = 1.0
    max_retries: int = 3


@dataclass
class WorkflowConfig:
    """Workflow-related configuration."""
    latentsync_workflow_path: str = "workflows/latentsync_workflow_api.json"
    framepack_workflow_path: str = "workflows/framepack_comfyui1.json"
    
    # Node IDs for workflow updates
    video_node_id: str = "1"
    audio_node_id: str = "4"
    output_node_id: str = "5"
    latentsync_node_id: str = "3"
    videocombine_node_id: str = "5"
    videolength_node_id: str = "2"


@dataclass
class WorkflowParameters:
    """Workflow processing parameters."""
    seed: int = 0
    lips_expression: float = 1.0
    inference_steps: int = 20
    crf: int = 19
    silent_padding_sec: float = 0.3
    
    # FramePack parameters
    framepack_prompt: str = "hosting a podcast"
    framepack_seed: int = 0
    framepack_random: bool = False


@dataclass
class TTSConfig:
    """Text-to-Speech configuration."""
    zonos_api_key: str = ""
    output_dir: str = "tts-output"
    use_local_tts: bool = False
    max_concurrent_jobs: int = 3
    timeout_seconds: int = 30
    retry_attempts: int = 2


@dataclass
class MediaConfig:
    """Media file handling configuration."""
    input_dir: str = "input"
    output_dir: str = "output"
    temp_dir: str = "temp"
    voices_dir: str = "voices"
    
    # File extensions
    video_extensions: tuple = ('.mp4', '.avi', '.mov', '.mkv', '.webm')
    image_extensions: tuple = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp', '.tiff')
    audio_extensions: tuple = ('.wav', '.mp3', '.flac', '.ogg', '.aac')
    
    # Thumbnail settings
    thumbnail_size: tuple = (100, 60)
    enable_thumbnails: bool = True


@dataclass
class UIConfig:
    """User interface configuration."""
    window_title: str = "Auto Latent Video Processor"
    window_geometry: tuple = (100, 100, 1200, 800)
    log_level: LogLevel = LogLevel.INFO
    auto_refresh_interval: int = 5000  # milliseconds
    
    # Speaker folder names
    speaker_folders: Dict[str, str] = field(default_factory=lambda: {
        's1_vid': "speaker1_video",
        's2_vid': "speaker2_video", 
        's1_aud': "speaker1_audio",
        's2_aud': "speaker2_audio"
    })


@dataclass
class AppConfig:
    """Main application configuration container."""
    comfyui: ComfyUIConfig = field(default_factory=ComfyUIConfig)
    workflow: WorkflowConfig = field(default_factory=WorkflowConfig)
    parameters: WorkflowParameters = field(default_factory=WorkflowParameters)
    tts: TTSConfig = field(default_factory=TTSConfig)
    media: MediaConfig = field(default_factory=MediaConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    
    # Application metadata
    version: str = "1.0.0"
    debug_mode: bool = False


class ConfigManager:
    """Centralized configuration manager with validation and persistence."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_file: Path to configuration file. If None, uses default.
        """
        self.config_file = Path(config_file or "config.json")
        self.config = AppConfig()
        self._load_environment_variables()
        
        # Load from file if it exists
        if self.config_file.exists():
            self.load_from_file()
        else:
            # Create default config file
            self.save_to_file()
    
    def _load_environment_variables(self):
        """Load configuration from environment variables."""
        env_mappings = {
            'COMFYUI_URL': ('comfyui', 'url'),
            'COMFYUI_OUTPUT_DIR': ('comfyui', 'output_base_dir'),
            'ZONOS_API_KEY': ('tts', 'zonos_api_key'),
            'INPUT_DIR': ('media', 'input_dir'),
            'OUTPUT_DIR': ('media', 'output_dir'),
            'WORKFLOW_PATH': ('workflow', 'latentsync_workflow_path'),
            'FRAMEPACK_WORKFLOW_PATH': ('workflow', 'framepack_workflow_path'),
            'DEBUG_MODE': ('debug_mode',),
            'LOG_LEVEL': ('ui', 'log_level'),
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self._set_nested_value(config_path, value)
    
    def _set_nested_value(self, path: tuple, value: str):
        """Set a nested configuration value from a tuple path."""
        try:
            obj = self.config
            for key in path[:-1]:
                obj = getattr(obj, key)
            
            # Convert value to appropriate type
            final_key = path[-1]
            current_value = getattr(obj, final_key)
            
            if isinstance(current_value, bool):
                value = value.lower() in ('true', '1', 'yes', 'on')
            elif isinstance(current_value, int):
                value = int(value)
            elif isinstance(current_value, float):
                value = float(value)
            elif isinstance(current_value, LogLevel):
                value = LogLevel(value.upper())
            
            setattr(obj, final_key, value)
            logging.debug(f"Set config {'.'.join(path)} = {value}")
            
        except (AttributeError, ValueError, TypeError) as e:
            logging.warning(f"Failed to set config {'.'.join(path)} = {value}: {e}")
    
    def load_from_file(self, file_path: Optional[str] = None):
        """Load configuration from JSON file."""
        config_file = Path(file_path) if file_path else self.config_file
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Update configuration with loaded data
            self._update_config_from_dict(data)
            logging.info(f"Configuration loaded from {config_file}")
            
        except FileNotFoundError:
            logging.warning(f"Configuration file {config_file} not found. Using defaults.")
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in configuration file {config_file}: {e}")
            raise ConfigError(f"Invalid configuration file: {e}")
        except Exception as e:
            logging.error(f"Error loading configuration from {config_file}: {e}")
            raise ConfigError(f"Failed to load configuration: {e}")
    
    def _update_config_from_dict(self, data: Dict[str, Any]):
        """Update configuration from dictionary data."""
        for section_name, section_data in data.items():
            if hasattr(self.config, section_name) and isinstance(section_data, dict):
                section = getattr(self.config, section_name)
                for key, value in section_data.items():
                    if hasattr(section, key):
                        # Handle special types
                        if key == 'log_level' and isinstance(value, str):
                            value = LogLevel(value.upper())
                        setattr(section, key, value)
            elif hasattr(self.config, section_name):
                # Direct attribute
                setattr(self.config, section_name, data[section_name])
    
    def save_to_file(self, file_path: Optional[str] = None):
        """Save configuration to JSON file."""
        config_file = Path(file_path) if file_path else self.config_file
        
        try:
            # Convert config to dictionary
            config_dict = asdict(self.config)
            
            # Handle special types
            if 'ui' in config_dict and 'log_level' in config_dict['ui']:
                config_dict['ui']['log_level'] = config_dict['ui']['log_level'].value
            
            # Ensure directory exists
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Configuration saved to {config_file}")
            
        except Exception as e:
            logging.error(f"Error saving configuration to {config_file}: {e}")
            raise ConfigError(f"Failed to save configuration: {e}")
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate ComfyUI configuration
        if not self.config.comfyui.url:
            errors.append("ComfyUI URL is required")
        
        # Validate workflow paths
        if not Path(self.config.workflow.latentsync_workflow_path).exists():
            errors.append(f"LatentSync workflow file not found: {self.config.workflow.latentsync_workflow_path}")
        
        if not Path(self.config.workflow.framepack_workflow_path).exists():
            errors.append(f"FramePack workflow file not found: {self.config.workflow.framepack_workflow_path}")
        
        # Validate directories
        required_dirs = [
            self.config.media.input_dir,
            self.config.media.output_dir,
        ]
        
        for dir_path in required_dirs:
            if dir_path and not Path(dir_path).exists():
                try:
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    errors.append(f"Cannot create directory {dir_path}: {e}")
        
        # Validate parameter ranges
        if not (0 <= self.config.parameters.lips_expression <= 10):
            errors.append("Lips expression must be between 0 and 10")
        
        if not (1 <= self.config.parameters.inference_steps <= 200):
            errors.append("Inference steps must be between 1 and 200")
        
        if not (0 <= self.config.parameters.crf <= 51):
            errors.append("CRF must be between 0 and 51")
        
        if not (0 <= self.config.parameters.silent_padding_sec <= 5):
            errors.append("Silent padding must be between 0 and 5 seconds")
        
        return errors
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary for backward compatibility."""
        return {
            'comfyui_url': self.config.comfyui.url,
            'workflow_path': self.config.workflow.latentsync_workflow_path,
            'framepack_workflow_path': self.config.workflow.framepack_workflow_path,
            'comfyui_output_base_dir': self.config.comfyui.output_base_dir,
            'input_dir': self.config.media.input_dir,
            'output_dir': self.config.media.output_dir,
            'zonos_api_key': self.config.tts.zonos_api_key,
            'tts_output_dir': self.config.tts.output_dir,
            'use_local_tts': self.config.tts.use_local_tts,
            'seed': self.config.parameters.seed,
            'lips_expression': self.config.parameters.lips_expression,
            'inference_steps': self.config.parameters.inference_steps,
            'crf': self.config.parameters.crf,
            'silent_padding_sec': self.config.parameters.silent_padding_sec,
            'framepack_prompt': self.config.parameters.framepack_prompt,
            'framepack_seed': self.config.parameters.framepack_seed,
            'framepack_random': self.config.parameters.framepack_random,
        }
    
    def update_from_gui(self, gui_values: Dict[str, Any]):
        """Update configuration from GUI values."""
        # Map GUI values to configuration
        mapping = {
            'comfyui_url': ('comfyui', 'url'),
            'workflow_path': ('workflow', 'latentsync_workflow_path'),
            'framepack_workflow_path': ('workflow', 'framepack_workflow_path'),
            'comfyui_output_base_dir': ('comfyui', 'output_base_dir'),
            'input_dir': ('media', 'input_dir'),
            'output_dir': ('media', 'output_dir'),
            'zonos_api_key': ('tts', 'zonos_api_key'),
            'tts_output_dir': ('tts', 'output_dir'),
            'use_local_tts': ('tts', 'use_local_tts'),
            'seed': ('parameters', 'seed'),
            'lips_expression': ('parameters', 'lips_expression'),
            'inference_steps': ('parameters', 'inference_steps'),
            'crf': ('parameters', 'crf'),
            'silent_padding_sec': ('parameters', 'silent_padding_sec'),
            'framepack_prompt': ('parameters', 'framepack_prompt'),
            'framepack_seed': ('parameters', 'framepack_seed'),
            'framepack_random': ('parameters', 'framepack_random'),
        }
        
        for gui_key, config_path in mapping.items():
            if gui_key in gui_values:
                self._set_nested_value(config_path, str(gui_values[gui_key]))


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config() -> AppConfig:
    """Get the current application configuration."""
    return get_config_manager().config
