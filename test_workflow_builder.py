"""
Test script for Workflow Builder integration.
"""

import sys
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
    from PySide6.QtCore import Qt
    
    from workflow_builder import (
        WorkflowBuilderWidget, WorkflowExecutor, NodeFactory,
        VideoInputNode, AudioInputNode, LipSyncNode, OutputNode
    )
    
    class TestWorkflowWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Workflow Builder Test")
            self.setGeometry(100, 100, 1400, 900)
            
            # Central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # Workflow builder
            self.workflow_builder = WorkflowBuilderWidget()
            layout.addWidget(self.workflow_builder)
            
            # Test buttons
            button_layout = QVBoxLayout()
            
            test_execution_btn = QPushButton("Test Workflow Execution")
            test_execution_btn.clicked.connect(self.test_execution)
            button_layout.addWidget(test_execution_btn)
            
            test_validation_btn = QPushButton("Test Workflow Validation")
            test_validation_btn.clicked.connect(self.test_validation)
            button_layout.addWidget(test_validation_btn)
            
            add_custom_node_btn = QPushButton("Add Custom Node")
            add_custom_node_btn.clicked.connect(self.add_custom_node)
            button_layout.addWidget(add_custom_node_btn)
            
            layout.addLayout(button_layout)
            
            # Connect signals
            self.workflow_builder.workflow_changed.connect(self.on_workflow_changed)
            self.workflow_builder.node_selected.connect(self.on_node_selected)
            
            print("✅ Workflow Builder Test Window initialized")
            print("🎬 Default workflow with Video Input → Audio Input → Lip Sync → Output created")
            print("🔧 Use the node palette to add more nodes")
            print("🎯 Try connecting nodes by dragging between ports")
            print("▶️ Use 'Execute Workflow' to test the workflow")
        
        def test_execution(self):
            """Test workflow execution."""
            try:
                print("🧪 Testing workflow execution...")
                
                # Set some test properties
                executor = self.workflow_builder.executor
                
                # Find video input node and set a test path
                for node in executor.nodes.values():
                    if isinstance(node, VideoInputNode):
                        node.properties["file_path"] = "test_video.mp4"
                        print(f"📹 Set video input: {node.properties['file_path']}")
                    elif isinstance(node, AudioInputNode):
                        node.properties["file_path"] = "test_audio.wav"
                        print(f"🎵 Set audio input: {node.properties['file_path']}")
                    elif isinstance(node, OutputNode):
                        node.properties["output_path"] = "test_output.mp4"
                        print(f"📤 Set output path: {node.properties['output_path']}")
                
                # Validate workflow
                errors = executor.validate()
                if errors:
                    print(f"❌ Validation errors: {errors}")
                    return
                
                print("✅ Workflow validation passed")
                print("ℹ️ Note: Actual execution would require real files")
                
            except Exception as e:
                print(f"❌ Error testing execution: {e}")
        
        def test_validation(self):
            """Test workflow validation."""
            try:
                print("🧪 Testing workflow validation...")
                
                executor = self.workflow_builder.executor
                errors = executor.validate()
                
                if errors:
                    print(f"⚠️ Validation issues found:")
                    for error in errors:
                        print(f"  - {error}")
                else:
                    print("✅ Workflow is valid!")
                
                # Print workflow statistics
                print(f"📊 Workflow Statistics:")
                print(f"  - Nodes: {len(executor.nodes)}")
                print(f"  - Connections: {len(executor.connections)}")
                print(f"  - Execution Order: {[executor.nodes[nid].name for nid in executor.execution_order]}")
                
            except Exception as e:
                print(f"❌ Error testing validation: {e}")
        
        def add_custom_node(self):
            """Add a custom node for testing."""
            try:
                print("🧪 Adding custom condition node...")
                
                # Add a condition node
                condition_node = NodeFactory.create_node("condition")
                if condition_node:
                    condition_node.position = (200, 300)
                    condition_node.properties["operator"] = ">"
                    
                    self.workflow_builder.executor.add_node(condition_node)
                    self.workflow_builder.graph_view.add_node_item(condition_node)
                    
                    print(f"✅ Added condition node: {condition_node.name}")
                else:
                    print("❌ Failed to create condition node")
                
            except Exception as e:
                print(f"❌ Error adding custom node: {e}")
        
        def on_workflow_changed(self):
            """Handle workflow change."""
            print("🔄 Workflow changed")
        
        def on_node_selected(self, node_id):
            """Handle node selection."""
            executor = self.workflow_builder.executor
            node = executor.nodes.get(node_id)
            if node:
                print(f"🎯 Node selected: {node.name} ({node.node_type.value})")
    
    def main():
        app = QApplication(sys.argv)
        
        window = TestWorkflowWindow()
        window.show()
        
        print("🚀 Workflow Builder Test started")
        print("📋 Features to test:")
        print("  - Drag nodes around the canvas")
        print("  - Select nodes to see properties")
        print("  - Add new nodes from the palette")
        print("  - Execute and validate workflows")
        print("  - Zoom in/out with mouse wheel")
        print("  - Use toolbar buttons for view controls")
        
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure PySide6 is installed and workflow_builder.py is available")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
