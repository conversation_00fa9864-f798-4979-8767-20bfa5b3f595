"""
Tests for the centralized configuration management system.
"""

import os
import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, Mock

from config_manager import (
    ConfigManager, 
    AppConfig, 
    ComfyUIConfig, 
    WorkflowConfig, 
    WorkflowParameters,
    TTSConfig,
    MediaConfig,
    UIConfig,
    LogLevel,
    ConfigError,
    get_config_manager,
    get_config
)


class TestAppConfig:
    """Test the main application configuration dataclass."""
    
    def test_default_config_creation(self):
        """Test creating default configuration."""
        config = AppConfig()
        
        # Test that all sections are created
        assert isinstance(config.comfyui, ComfyUIConfig)
        assert isinstance(config.workflow, WorkflowConfig)
        assert isinstance(config.parameters, WorkflowParameters)
        assert isinstance(config.tts, TTSConfig)
        assert isinstance(config.media, MediaConfig)
        assert isinstance(config.ui, UIConfig)
        
        # Test some default values
        assert config.version == "1.0.0"
        assert config.debug_mode is False
        assert config.comfyui.url == "127.0.0.1:8188"
        assert config.parameters.seed == 0
        assert config.parameters.lips_expression == 1.0
    
    def test_config_sections_have_expected_attributes(self):
        """Test that configuration sections have expected attributes."""
        config = AppConfig()
        
        # ComfyUI config
        assert hasattr(config.comfyui, 'url')
        assert hasattr(config.comfyui, 'output_base_dir')
        assert hasattr(config.comfyui, 'timeout_seconds')
        
        # Workflow config
        assert hasattr(config.workflow, 'latentsync_workflow_path')
        assert hasattr(config.workflow, 'framepack_workflow_path')
        assert hasattr(config.workflow, 'video_node_id')
        
        # Parameters
        assert hasattr(config.parameters, 'seed')
        assert hasattr(config.parameters, 'lips_expression')
        assert hasattr(config.parameters, 'framepack_prompt')
        
        # TTS config
        assert hasattr(config.tts, 'zonos_api_key')
        assert hasattr(config.tts, 'output_dir')
        assert hasattr(config.tts, 'use_local_tts')
        
        # Media config
        assert hasattr(config.media, 'input_dir')
        assert hasattr(config.media, 'video_extensions')
        assert hasattr(config.media, 'thumbnail_size')
        
        # UI config
        assert hasattr(config.ui, 'window_title')
        assert hasattr(config.ui, 'log_level')
        assert hasattr(config.ui, 'speaker_folders')


class TestConfigManager:
    """Test the configuration manager."""
    
    def test_config_manager_initialization(self, tmp_path):
        """Test configuration manager initialization."""
        config_file = tmp_path / "test_config.json"
        manager = ConfigManager(str(config_file))
        
        assert isinstance(manager.config, AppConfig)
        assert manager.config_file == config_file
        
        # Should create default config file
        assert config_file.exists()
    
    def test_save_and_load_config(self, tmp_path):
        """Test saving and loading configuration."""
        config_file = tmp_path / "test_config.json"
        manager = ConfigManager(str(config_file))
        
        # Modify some values
        manager.config.comfyui.url = "192.168.1.100:8188"
        manager.config.parameters.seed = 12345
        manager.config.tts.zonos_api_key = "test-api-key"
        
        # Save configuration
        manager.save_to_file()
        
        # Create new manager and load
        manager2 = ConfigManager(str(config_file))
        
        # Check values were loaded correctly
        assert manager2.config.comfyui.url == "192.168.1.100:8188"
        assert manager2.config.parameters.seed == 12345
        assert manager2.config.tts.zonos_api_key == "test-api-key"
    
    def test_environment_variable_loading(self):
        """Test loading configuration from environment variables."""
        env_vars = {
            'COMFYUI_URL': '10.0.0.1:8188',
            'ZONOS_API_KEY': 'env-api-key',
            'INPUT_DIR': '/custom/input',
            'DEBUG_MODE': 'true',
            'LOG_LEVEL': 'DEBUG'
        }
        
        with patch.dict(os.environ, env_vars):
            manager = ConfigManager()
            
            assert manager.config.comfyui.url == '10.0.0.1:8188'
            assert manager.config.tts.zonos_api_key == 'env-api-key'
            assert manager.config.media.input_dir == '/custom/input'
            assert manager.config.debug_mode is True
            assert manager.config.ui.log_level == LogLevel.DEBUG
    
    def test_config_validation(self, tmp_path):
        """Test configuration validation."""
        # Create workflow files for validation
        workflow_dir = tmp_path / "workflows"
        workflow_dir.mkdir()
        
        latentsync_file = workflow_dir / "latentsync_workflow_api.json"
        framepack_file = workflow_dir / "framepack_comfyui1.json"
        
        latentsync_file.write_text('{"test": "workflow"}')
        framepack_file.write_text('{"test": "framepack"}')
        
        # Create manager with valid paths
        manager = ConfigManager()
        manager.config.workflow.latentsync_workflow_path = str(latentsync_file)
        manager.config.workflow.framepack_workflow_path = str(framepack_file)
        
        # Should have no validation errors
        errors = manager.validate()
        workflow_errors = [e for e in errors if "workflow file not found" in e.lower()]
        assert len(workflow_errors) == 0
    
    def test_config_validation_errors(self):
        """Test configuration validation with errors."""
        manager = ConfigManager()
        
        # Set invalid values
        manager.config.comfyui.url = ""  # Empty URL
        manager.config.workflow.latentsync_workflow_path = "/nonexistent/workflow.json"
        manager.config.parameters.lips_expression = 15.0  # Out of range
        manager.config.parameters.crf = 100  # Out of range
        
        errors = manager.validate()
        
        # Should have multiple errors
        assert len(errors) > 0
        assert any("ComfyUI URL is required" in error for error in errors)
        assert any("workflow file not found" in error for error in errors)
        assert any("Lips expression must be between" in error for error in errors)
        assert any("CRF must be between" in error for error in errors)
    
    def test_get_config_dict_backward_compatibility(self):
        """Test getting configuration as dictionary for backward compatibility."""
        manager = ConfigManager()
        
        # Set some values
        manager.config.comfyui.url = "test-url"
        manager.config.parameters.seed = 999
        manager.config.tts.zonos_api_key = "test-key"
        
        config_dict = manager.get_config_dict()
        
        # Check expected keys exist
        expected_keys = [
            'comfyui_url', 'workflow_path', 'framepack_workflow_path',
            'input_dir', 'output_dir', 'zonos_api_key', 'seed',
            'lips_expression', 'inference_steps', 'crf'
        ]
        
        for key in expected_keys:
            assert key in config_dict
        
        # Check values
        assert config_dict['comfyui_url'] == "test-url"
        assert config_dict['seed'] == 999
        assert config_dict['zonos_api_key'] == "test-key"
    
    def test_update_from_gui(self):
        """Test updating configuration from GUI values."""
        manager = ConfigManager()
        
        gui_values = {
            'comfyui_url': 'gui-url',
            'seed': '12345',
            'lips_expression': '2.5',
            'use_local_tts': 'true',
            'framepack_random': 'false'
        }
        
        manager.update_from_gui(gui_values)
        
        assert manager.config.comfyui.url == 'gui-url'
        assert manager.config.parameters.seed == 12345
        assert manager.config.parameters.lips_expression == 2.5
        assert manager.config.tts.use_local_tts is True
        assert manager.config.parameters.framepack_random is False
    
    def test_invalid_json_handling(self, tmp_path):
        """Test handling of invalid JSON configuration files."""
        config_file = tmp_path / "invalid_config.json"
        config_file.write_text("{ invalid json }")
        
        with pytest.raises(ConfigError):
            manager = ConfigManager(str(config_file))
            manager.load_from_file()
    
    def test_missing_config_file_handling(self, tmp_path):
        """Test handling of missing configuration files."""
        config_file = tmp_path / "nonexistent_config.json"
        
        # Should not raise exception, should create default config
        manager = ConfigManager(str(config_file))
        
        # Should have default values
        assert manager.config.comfyui.url == "127.0.0.1:8188"
        assert manager.config.parameters.seed == 0
        
        # Should create the config file
        assert config_file.exists()


class TestGlobalConfigManager:
    """Test global configuration manager functions."""
    
    def test_get_config_manager_singleton(self):
        """Test that get_config_manager returns singleton instance."""
        manager1 = get_config_manager()
        manager2 = get_config_manager()
        
        assert manager1 is manager2
        assert isinstance(manager1, ConfigManager)
    
    def test_get_config_returns_app_config(self):
        """Test that get_config returns AppConfig instance."""
        config = get_config()
        
        assert isinstance(config, AppConfig)
        assert hasattr(config, 'comfyui')
        assert hasattr(config, 'parameters')
        assert hasattr(config, 'tts')


class TestConfigurationIntegration:
    """Test configuration integration scenarios."""
    
    def test_full_configuration_workflow(self, tmp_path):
        """Test complete configuration workflow."""
        config_file = tmp_path / "integration_config.json"
        
        # Create manager
        manager = ConfigManager(str(config_file))
        
        # Modify configuration
        manager.config.comfyui.url = "integration-test:8188"
        manager.config.parameters.seed = 54321
        manager.config.tts.zonos_api_key = "integration-key"
        manager.config.ui.log_level = LogLevel.WARNING
        
        # Save configuration
        manager.save_to_file()
        
        # Verify file contents
        with open(config_file, 'r') as f:
            saved_data = json.load(f)
        
        assert saved_data['comfyui']['url'] == "integration-test:8188"
        assert saved_data['parameters']['seed'] == 54321
        assert saved_data['tts']['zonos_api_key'] == "integration-key"
        assert saved_data['ui']['log_level'] == "WARNING"
        
        # Create new manager and verify loading
        manager2 = ConfigManager(str(config_file))
        
        assert manager2.config.comfyui.url == "integration-test:8188"
        assert manager2.config.parameters.seed == 54321
        assert manager2.config.tts.zonos_api_key == "integration-key"
        assert manager2.config.ui.log_level == LogLevel.WARNING
    
    def test_configuration_with_gui_updates(self):
        """Test configuration updates from GUI simulation."""
        manager = ConfigManager()
        
        # Simulate GUI form data
        gui_form_data = {
            'comfyui_url': '************:8188',
            'workflow_path': 'custom/workflow.json',
            'input_dir': '/custom/input',
            'output_dir': '/custom/output',
            'zonos_api_key': 'gui-updated-key',
            'seed': '99999',
            'lips_expression': '3.5',
            'inference_steps': '30',
            'crf': '25',
            'silent_padding_sec': '0.8',
            'framepack_prompt': 'Custom FramePack prompt',
            'framepack_seed': '11111',
            'framepack_random': 'true',
            'use_local_tts': 'false'
        }
        
        # Update from GUI
        manager.update_from_gui(gui_form_data)
        
        # Verify updates
        assert manager.config.comfyui.url == '************:8188'
        assert manager.config.workflow.latentsync_workflow_path == 'custom/workflow.json'
        assert manager.config.media.input_dir == '/custom/input'
        assert manager.config.media.output_dir == '/custom/output'
        assert manager.config.tts.zonos_api_key == 'gui-updated-key'
        assert manager.config.parameters.seed == 99999
        assert manager.config.parameters.lips_expression == 3.5
        assert manager.config.parameters.inference_steps == 30
        assert manager.config.parameters.crf == 25
        assert manager.config.parameters.silent_padding_sec == 0.8
        assert manager.config.parameters.framepack_prompt == 'Custom FramePack prompt'
        assert manager.config.parameters.framepack_seed == 11111
        assert manager.config.parameters.framepack_random is True
        assert manager.config.tts.use_local_tts is False
        
        # Get backward-compatible dictionary
        config_dict = manager.get_config_dict()
        
        # Verify dictionary format
        assert config_dict['comfyui_url'] == '************:8188'
        assert config_dict['seed'] == 99999
        assert config_dict['lips_expression'] == 3.5
        assert config_dict['framepack_random'] is True
