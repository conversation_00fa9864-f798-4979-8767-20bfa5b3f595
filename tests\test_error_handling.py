import os
import tempfile
import pytest
from unittest.mock import Mock, patch, MagicMock
import json
import requests

from backend_processor import (
    load_workflow,
    update_workflow,
    submit_workflow,
    poll_comfyui_status,
    get_comfyui_status
)


class TestNetworkErrorHandling:
    """Test network error handling scenarios."""
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_connection_error(self, mock_post):
        """Test workflow submission with connection error."""
        mock_post.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result is None
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_timeout_error(self, mock_post):
        """Test workflow submission with timeout error."""
        mock_post.side_effect = requests.exceptions.Timeout("Request timed out")
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result is None
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_http_error(self, mock_post):
        """Test workflow submission with HTTP error."""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("500 Server Error")
        mock_post.return_value = mock_response
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result is None
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_json_decode_error(self, mock_post):
        """Test workflow submission with JSON decode error."""
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_response.text = "Invalid response"
        mock_post.return_value = mock_response
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result is None
    
    @patch('backend_processor.requests.get')
    def test_get_comfyui_status_network_error(self, mock_get):
        """Test status retrieval with network error."""
        mock_get.side_effect = requests.exceptions.RequestException("Network error")
        
        result = get_comfyui_status("http://127.0.0.1:8188/history")
        
        assert result is None
    
    @patch('backend_processor.requests.get')
    def test_get_comfyui_status_json_error(self, mock_get):
        """Test status retrieval with JSON error."""
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_get.return_value = mock_response
        
        result = get_comfyui_status("http://127.0.0.1:8188/history")
        
        assert result is None


class TestFileSystemErrorHandling:
    """Test file system error handling scenarios."""
    
    def test_load_workflow_permission_denied(self, tmp_path):
        """Test workflow loading with permission denied."""
        workflow_file = tmp_path / "restricted.json"
        workflow_file.write_text('{"test": "data"}')
        
        # Mock open to raise PermissionError
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = load_workflow(str(workflow_file))
            assert result is None
    
    def test_load_workflow_file_not_found(self):
        """Test workflow loading with non-existent file."""
        result = load_workflow("/nonexistent/path/workflow.json")
        assert result is None
    
    def test_load_workflow_is_directory(self, tmp_path):
        """Test workflow loading when path is a directory."""
        directory = tmp_path / "not_a_file"
        directory.mkdir()
        
        result = load_workflow(str(directory))
        assert result is None
    
    def test_update_workflow_invalid_paths(self):
        """Test workflow updating with invalid file paths."""
        base_workflow = {
            "1": {"class_type": "VHS_LoadVideo", "inputs": {"video": ""}},
            "4": {"class_type": "AudioLoader", "inputs": {"audio": ""}}
        }
        
        config = {}
        
        # Test with non-existent files
        result = update_workflow(base_workflow, "/nonexistent/video.mp4", "/nonexistent/audio.wav", config)
        
        # Should still work (paths are just updated, not validated)
        assert result is not None
        assert "/nonexistent/video.mp4" in result["1"]["inputs"]["video"]
        assert "/nonexistent/audio.wav" in result["4"]["inputs"]["audio"]


class TestWorkflowValidationErrors:
    """Test workflow validation error scenarios."""
    
    def test_update_workflow_malformed_workflow(self):
        """Test updating malformed workflow."""
        malformed_workflow = {
            "1": "not_a_dict",
            "2": {"missing_inputs": True}
        }
        
        config = {'seed': 123}
        
        # Should handle malformed workflow gracefully
        result = update_workflow(malformed_workflow, "video.mp4", "audio.wav", config)
        assert result is not None
    
    def test_update_workflow_missing_required_nodes(self):
        """Test updating workflow with missing required nodes."""
        incomplete_workflow = {
            "999": {"class_type": "UnknownNode", "inputs": {}}
        }
        
        config = {
            'seed': 123,
            'lips_expression': 2.0,
            'inference_steps': 25,
            'crf': 20,
            'silent_padding_sec': 0.4
        }
        
        result = update_workflow(incomplete_workflow, "video.mp4", "audio.wav", config)
        
        # Should return the workflow even if nodes are missing
        assert result is not None
        assert result == incomplete_workflow  # Should be unchanged
    
    def test_update_workflow_none_input(self):
        """Test updating workflow with None input."""
        result = update_workflow(None, "video.mp4", "audio.wav", {})
        assert result is None
    
    def test_update_workflow_exception_handling(self):
        """Test workflow updating with unexpected exceptions."""
        # Create a workflow that will cause an exception during processing
        problematic_workflow = {
            "1": {"inputs": {"video": None}}  # This might cause issues
        }
        
        config = {}
        
        # Should handle exceptions gracefully
        try:
            result = update_workflow(problematic_workflow, "video.mp4", "audio.wav", config)
            # If no exception, result should be valid or None
            assert result is None or isinstance(result, dict)
        except Exception:
            # If exception occurs, it should be handled in the actual implementation
            pytest.fail("update_workflow should handle exceptions gracefully")


class TestPollingErrorHandling:
    """Test polling error handling scenarios."""
    
    @patch('backend_processor.get_comfyui_status')
    @patch('backend_processor.time.sleep')
    def test_poll_comfyui_status_network_failure(self, mock_sleep, mock_get_status):
        """Test polling with intermittent network failures."""
        # Simulate network failures followed by success
        mock_get_status.side_effect = [
            None,  # First call fails
            None,  # Second call fails
            {      # Third call succeeds
                'test-prompt-id': {
                    'status': {'completed': True},
                    'outputs': {'5': {'videos': [{'filename': 'output.mp4'}]}}
                }
            }
        ]
        
        success, files = poll_comfyui_status("127.0.0.1:8188", "test-prompt-id")
        
        assert success is True
        assert files == ['output.mp4']
        assert mock_get_status.call_count == 3
    
    @patch('backend_processor.get_comfyui_status')
    @patch('backend_processor.time.sleep')
    def test_poll_comfyui_status_persistent_failure(self, mock_sleep, mock_get_status):
        """Test polling with persistent network failure."""
        # Always return None (network failure)
        mock_get_status.return_value = None
        
        # Mock time to simulate timeout
        with patch('backend_processor.time.time') as mock_time:
            mock_time.side_effect = [0, 301]  # Immediate timeout
            
            success, files = poll_comfyui_status("127.0.0.1:8188", "test-prompt-id")
            
            assert success is False
            assert files == []
    
    @patch('backend_processor.get_comfyui_status')
    @patch('backend_processor.time.sleep')
    def test_poll_comfyui_status_malformed_response(self, mock_sleep, mock_get_status):
        """Test polling with malformed response."""
        # Return malformed response
        mock_get_status.return_value = {
            'test-prompt-id': {
                'status': 'invalid_status_format',
                'outputs': 'not_a_dict'
            }
        }
        
        # Mock time to prevent infinite loop
        with patch('backend_processor.time.time') as mock_time:
            mock_time.side_effect = [0, 301]  # Immediate timeout
            
            success, files = poll_comfyui_status("127.0.0.1:8188", "test-prompt-id")
            
            assert success is False
            assert files == []
    
    @patch('backend_processor.get_comfyui_status')
    @patch('backend_processor.time.sleep')
    def test_poll_comfyui_status_missing_outputs(self, mock_sleep, mock_get_status):
        """Test polling with missing outputs in response."""
        mock_get_status.return_value = {
            'test-prompt-id': {
                'status': {'completed': True},
                'outputs': {}  # No outputs
            }
        }
        
        success, files = poll_comfyui_status("127.0.0.1:8188", "test-prompt-id")
        
        assert success is True
        assert files == []  # No files found


class TestConfigurationErrorHandling:
    """Test configuration error handling."""
    
    def test_update_workflow_invalid_config_types(self):
        """Test workflow updating with invalid configuration types."""
        base_workflow = {
            "3": {"class_type": "LatentSync", "inputs": {
                "seed": 0, "lips_expression": 1.0, "inference_steps": 20
            }}
        }
        
        # Test with invalid config types
        invalid_configs = [
            {'seed': 'not_a_number'},
            {'lips_expression': 'invalid'},
            {'inference_steps': None},
            {'crf': 'high'},
            {'silent_padding_sec': [1, 2, 3]}
        ]
        
        for config in invalid_configs:
            result = update_workflow(base_workflow, "video.mp4", "audio.wav", config)
            # Should handle invalid types gracefully
            assert result is not None
    
    def test_update_workflow_extreme_values(self):
        """Test workflow updating with extreme configuration values."""
        base_workflow = {
            "3": {"class_type": "LatentSync", "inputs": {
                "seed": 0, "lips_expression": 1.0, "inference_steps": 20
            }}
        }
        
        # Test with extreme values
        extreme_config = {
            'seed': 999999999999,
            'lips_expression': -1000.0,
            'inference_steps': 0,
            'crf': 100,
            'silent_padding_sec': -5.0
        }
        
        result = update_workflow(base_workflow, "video.mp4", "audio.wav", extreme_config)
        
        # Should accept extreme values (validation might be done elsewhere)
        assert result is not None
        assert result["3"]["inputs"]["seed"] == 999999999999


class TestResourceErrorHandling:
    """Test resource-related error handling."""
    
    def test_memory_error_simulation(self):
        """Test handling of memory errors."""
        # Simulate memory error during workflow processing
        with patch('backend_processor.copy.deepcopy', side_effect=MemoryError("Out of memory")):
            base_workflow = {"1": {"inputs": {}}}
            result = update_workflow(base_workflow, "video.mp4", "audio.wav", {})
            
            # Should handle memory error gracefully
            assert result is None
    
    @patch('backend_processor.requests.post')
    def test_large_payload_handling(self, mock_post):
        """Test handling of large payloads."""
        # Create a very large workflow
        large_workflow = {}
        for i in range(1000):
            large_workflow[str(i)] = {
                "class_type": f"Node{i}",
                "inputs": {"data": "x" * 1000}  # Large input data
            }
        
        mock_response = Mock()
        mock_response.json.return_value = {'prompt_id': 'test-id'}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        result = submit_workflow("127.0.0.1:8188", large_workflow)
        
        # Should handle large payloads
        assert result == 'test-id'
        mock_post.assert_called_once()


class TestConcurrencyErrorHandling:
    """Test concurrency-related error handling."""
    
    @patch('backend_processor.requests.post')
    def test_concurrent_submissions(self, mock_post):
        """Test handling of concurrent workflow submissions."""
        import threading
        import time
        
        results = []
        errors = []
        
        def submit_workflow_thread(workflow_id):
            try:
                mock_response = Mock()
                mock_response.json.return_value = {'prompt_id': f'prompt-{workflow_id}'}
                mock_response.raise_for_status.return_value = None
                mock_post.return_value = mock_response
                
                workflow = {"1": {"class_type": f"Node{workflow_id}"}}
                result = submit_workflow("127.0.0.1:8188", workflow)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=submit_workflow_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Should handle concurrent access without errors
        assert len(errors) == 0
        assert len(results) == 5
