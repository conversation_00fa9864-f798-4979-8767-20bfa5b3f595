import os
import tempfile
import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Mock the zp-c-gen imports since they might not be available in test environment
with patch.dict('sys.modules', {
    'app.utils.script_parser': MagicMock(),
    'app.services.zonos_client': MagicMock(),
    'app.services.voice_manager': MagicMock()
}):
    from backend_processor import run_tts_pipeline, auto_pair_audio_images


class MockScriptParser:
    """Mock script parser for testing."""
    
    @staticmethod
    def parse_script(script_text):
        """Mock parse_script method."""
        if not script_text.strip():
            return []
        
        # Return mock segments
        return [
            {
                'speaker': 'SPEAKER1',
                'text': 'Hello world',
                'emotion': {'happiness': 0.8},
                'speed': 20,
                'pause': 0.5
            },
            {
                'speaker': 'SPEAKER2', 
                'text': 'How are you?',
                'emotion': {'neutral': 1.0},
                'speed': 15,
                'pause': 0.3
            }
        ]


class MockZonosClient:
    """Mock Zonos client for testing."""
    
    def __init__(self, api_key):
        self.api_key = api_key
    
    def generate_speech(self, text, **kwargs):
        """Mock speech generation."""
        if not text:
            raise ValueError("Text cannot be empty")
        
        # Return mock audio data
        return b"fake_audio_data_" + text.encode()[:10]


class MockVoiceManager:
    """Mock voice manager for testing."""
    
    @staticmethod
    def get_voice_for_speaker(speaker_name):
        """Mock voice retrieval."""
        return {
            'voice_id': f'voice_{speaker_name.lower()}',
            'voice_name': f'{speaker_name} Voice'
        }


class TestTTSPipeline:
    """Test TTS pipeline functionality."""
    
    @patch('backend_processor.ScriptParser', MockScriptParser)
    @patch('backend_processor.ZonosClient', MockZonosClient)
    @patch('backend_processor.voice_manager', MockVoiceManager)
    def test_run_tts_pipeline_success(self, tmp_path):
        """Test successful TTS pipeline execution."""
        output_dir = tmp_path / "tts_output"
        output_dir.mkdir()
        
        config = {
            'zonos_api_key': 'test_api_key',
            'tts_output_dir': str(output_dir),
            'use_local_tts': False
        }
        
        script_text = "[SPEAKER1]: Hello world\n[SPEAKER2]: How are you?"
        
        with patch('builtins.open', create=True) as mock_open:
            mock_file = MagicMock()
            mock_open.return_value.__enter__.return_value = mock_file
            
            result = run_tts_pipeline(script_text, config)
            
            # Should return list of generated audio files
            assert isinstance(result, list)
            assert len(result) >= 0  # May be empty if mocked
    
    @patch('backend_processor.ScriptParser', MockScriptParser)
    def test_run_tts_pipeline_empty_script(self, tmp_path):
        """Test TTS pipeline with empty script."""
        output_dir = tmp_path / "tts_output"
        output_dir.mkdir()
        
        config = {
            'zonos_api_key': 'test_api_key',
            'tts_output_dir': str(output_dir)
        }
        
        result = run_tts_pipeline("", config)
        assert result == []
    
    @patch('backend_processor.ScriptParser', MockScriptParser)
    def test_run_tts_pipeline_no_api_key(self, tmp_path):
        """Test TTS pipeline without API key."""
        output_dir = tmp_path / "tts_output"
        output_dir.mkdir()
        
        config = {
            'zonos_api_key': None,
            'tts_output_dir': str(output_dir)
        }
        
        result = run_tts_pipeline("[SPEAKER1]: Hello", config)
        assert result == []
    
    @patch('backend_processor.ScriptParser', MockScriptParser)
    @patch('backend_processor.ZonosClient')
    def test_run_tts_pipeline_api_error(self, mock_client_class, tmp_path):
        """Test TTS pipeline with API error."""
        output_dir = tmp_path / "tts_output"
        output_dir.mkdir()
        
        # Mock client to raise exception
        mock_client = Mock()
        mock_client.generate_speech.side_effect = Exception("API Error")
        mock_client_class.return_value = mock_client
        
        config = {
            'zonos_api_key': 'test_api_key',
            'tts_output_dir': str(output_dir)
        }
        
        result = run_tts_pipeline("[SPEAKER1]: Hello", config)
        # Should handle error gracefully
        assert isinstance(result, list)


class TestAudioImagePairing:
    """Test audio-image pairing functionality."""
    
    def test_auto_pair_audio_images_basic(self, tmp_path):
        """Test basic audio-image pairing."""
        # Create test audio files
        audio_files = []
        for i in range(3):
            audio_file = tmp_path / f"{i}_speaker.wav"
            audio_file.write_bytes(b"fake_audio_data")
            audio_files.append(str(audio_file))
        
        # Create test image files
        for i in range(2):
            img_file = tmp_path / f"image_{i}.png"
            img_file.write_bytes(b"fake_image_data")
        
        result = auto_pair_audio_images(audio_files, tmp_path)
        
        assert len(result) == 3  # Should have 3 pairs (same as audio files)
        
        # Check that all audio files are included
        audio_paths_in_result = [audio_path for _, audio_path in result]
        assert set(audio_paths_in_result) == set(audio_files)
        
        # Check that image paths are valid
        for image_path, _ in result:
            assert os.path.exists(image_path)
            assert str(tmp_path) in image_path
    
    def test_auto_pair_audio_images_no_images(self, tmp_path):
        """Test audio-image pairing with no images."""
        # Create test audio files
        audio_files = []
        for i in range(2):
            audio_file = tmp_path / f"{i}_speaker.wav"
            audio_file.write_bytes(b"fake_audio_data")
            audio_files.append(str(audio_file))
        
        # No image files created
        result = auto_pair_audio_images(audio_files, tmp_path)
        
        # Should still return pairs, but with repeated/default images
        assert len(result) == 2
        
        # All audio files should be present
        audio_paths_in_result = [audio_path for _, audio_path in result]
        assert set(audio_paths_in_result) == set(audio_files)
    
    def test_auto_pair_audio_images_more_images_than_audio(self, tmp_path):
        """Test pairing when there are more images than audio files."""
        # Create test audio files
        audio_files = []
        for i in range(2):
            audio_file = tmp_path / f"{i}_speaker.wav"
            audio_file.write_bytes(b"fake_audio_data")
            audio_files.append(str(audio_file))
        
        # Create more image files than audio
        for i in range(5):
            img_file = tmp_path / f"image_{i}.png"
            img_file.write_bytes(b"fake_image_data")
        
        result = auto_pair_audio_images(audio_files, tmp_path)
        
        assert len(result) == 2  # Should match number of audio files
        
        # Check that different images are used (not all the same)
        image_paths_in_result = [image_path for image_path, _ in result]
        # At least some variety in image selection
        assert len(set(image_paths_in_result)) >= 1
    
    def test_auto_pair_audio_images_empty_audio_list(self, tmp_path):
        """Test pairing with empty audio list."""
        # Create some image files
        for i in range(3):
            img_file = tmp_path / f"image_{i}.png"
            img_file.write_bytes(b"fake_image_data")
        
        result = auto_pair_audio_images([], tmp_path)
        assert result == []
    
    def test_auto_pair_audio_images_nonexistent_directory(self):
        """Test pairing with non-existent image directory."""
        audio_files = ["/fake/path/audio.wav"]
        nonexistent_dir = Path("/fake/nonexistent/directory")
        
        result = auto_pair_audio_images(audio_files, nonexistent_dir)
        
        # Should handle gracefully, possibly returning empty or using defaults
        assert isinstance(result, list)


class TestVoiceManagement:
    """Test voice management functionality."""
    
    @patch('backend_processor.voice_manager', MockVoiceManager)
    def test_get_voice_for_speaker(self):
        """Test voice retrieval for speaker."""
        from backend_processor import voice_manager
        
        voice_info = voice_manager.get_voice_for_speaker("JOHN")
        
        assert voice_info is not None
        assert 'voice_id' in voice_info
        assert voice_info['voice_id'] == 'voice_john'
    
    def test_voice_manager_integration(self):
        """Test voice manager integration."""
        # This would test the actual voice manager if available
        # For now, just ensure the import doesn't fail
        try:
            from backend_processor import voice_manager
            assert voice_manager is not None
        except ImportError:
            # Expected if zp-c-gen is not available
            pytest.skip("Voice manager not available in test environment")


class TestScriptParsing:
    """Test script parsing functionality."""
    
    @patch('backend_processor.ScriptParser', MockScriptParser)
    def test_script_parser_integration(self):
        """Test script parser integration."""
        from backend_processor import ScriptParser
        
        script_text = "[SPEAKER1]: Hello world"
        segments = ScriptParser.parse_script(script_text)
        
        assert isinstance(segments, list)
        assert len(segments) > 0
        
        # Check segment structure
        segment = segments[0]
        assert 'speaker' in segment
        assert 'text' in segment
        assert 'emotion' in segment
    
    @patch('backend_processor.ScriptParser', MockScriptParser)
    def test_script_parser_empty_input(self):
        """Test script parser with empty input."""
        from backend_processor import ScriptParser
        
        segments = ScriptParser.parse_script("")
        assert segments == []
    
    def test_script_parser_real_integration(self):
        """Test real script parser if available."""
        try:
            from backend_processor import ScriptParser
            
            # Test with a simple script
            script_text = "[HOST]: Welcome to the show!"
            segments = ScriptParser.parse_script(script_text)
            
            assert isinstance(segments, list)
            # Additional assertions would depend on actual parser implementation
            
        except (ImportError, AttributeError):
            # Expected if ScriptParser is not available or mocked
            pytest.skip("Real ScriptParser not available in test environment")


class TestErrorHandling:
    """Test error handling in TTS components."""
    
    def test_tts_pipeline_missing_output_dir(self):
        """Test TTS pipeline with missing output directory."""
        config = {
            'zonos_api_key': 'test_key',
            'tts_output_dir': '/nonexistent/directory'
        }
        
        # Should handle missing directory gracefully
        result = run_tts_pipeline("[SPEAKER1]: Hello", config)
        assert isinstance(result, list)
    
    @patch('backend_processor.ScriptParser')
    def test_tts_pipeline_parser_error(self, mock_parser):
        """Test TTS pipeline with parser error."""
        mock_parser.parse_script.side_effect = Exception("Parser error")
        
        config = {
            'zonos_api_key': 'test_key',
            'tts_output_dir': '/tmp'
        }
        
        result = run_tts_pipeline("[SPEAKER1]: Hello", config)
        # Should handle parser error gracefully
        assert isinstance(result, list)
