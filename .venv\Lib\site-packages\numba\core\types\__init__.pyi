# This file is provided by @jorenham with minor modifications (e.g. typos).
# See original content at: https://github.com/numba/numba/pull/9945#pullrequestreview-2668923222.
#
# This file has been tested under:
#   - mypy for the use-case in issue #9900
#   - mypy numba/core/types/__init__.pyi
# Testing with mypy.stubtest does not work due to other mypy errors in the code
# base.
from .abstract import *
from .common import Opaque
from .containers import *
from .function_type import *
from .functions import *
from .iterators import *
from .misc import *
from .new_scalars import *
from .npytypes import *
from .scalars import *

__all__ = [
    "b1",
    "bool",
    "bool_",
    "boolean",
    "byte",
    "c8",
    "c16",
    "char",
    "complex64",
    "complex128",
    "deferred_type",
    "double",
    "f4",
    "f8",
    "ffi",
    "ffi_forced_object",
    "float32",
    "float64",
    "i1",
    "i2",
    "i4",
    "i8",
    "int8",
    "int16",
    "int32",
    "int64",
    "int_",
    "intc",
    "intp",
    "long_",
    "longlong",
    "none",
    "optional",
    "short",
    "size_t",
    "ssize_t",
    "u1",
    "u2",
    "u4",
    "u8",
    "uchar",
    "uint",
    "uint8",
    "uint16",
    "uint32",
    "uint64",
    "uintc",
    "uintp",
    "ulong",
    "ulonglong",
    "ushort",
    "void",
]

# TODO: Final

pyobject: PyObject = ...
ffi_forced_object: Opaque = ...
ffi: Opaque = ...
none: NoneType = ...
ellipsis: EllipsisType = ...
Any: Phantom = ...
undefined: Undefined = ...
py2_string_type: Opaque = ...
unicode_type: UnicodeType = ...
string: UnicodeType = ...
unknown: Dummy = ...
npy_rng: NumPyRandomGeneratorType = ...
npy_bitgen: NumPyRandomBitGeneratorType = ...

_undef_var: UndefVar = ...

code_type: Opaque = ...
pyfunc_type: Opaque = ...

voidptr: RawPointer = ...

optional = Optional
deferred_type = DeferredType
slice2_type: SliceType = ...
slice3_type: SliceType = ...
void: NoneType = ...

boolean: Boolean = ...
bool_: Boolean = ...
bool: Boolean = ...  # numpy>=2

int8: Integer = ...
int16: Integer = ...
int32: Integer = ...
int64: Integer = ...
intp: Integer = ...
intc: Integer = ...
ssize_t: Integer = ...
char: Integer = ...
short: Integer = ...
int_: Integer = ...
long_: Integer = ...
longlong: Integer = ...

byte: Integer = ...
uint8: Integer = ...
uint16: Integer = ...
uint32: Integer = ...
uint64: Integer = ...
uintp: Integer = ...
uintc: Integer = ...
size_t: Integer = ...
uchar: Integer = ...
ushort: Integer = ...
uint: Integer = ...
ulong: Integer = ...
ulonglong: Integer = ...

float16: Float = ...
float32: Float = ...
float_ = float32
float64: Float = ...
double = float64

# TODO: make generic in the wrapped `Float` type
complex64: Complex = ...
complex128: Complex = ...

range_iter32_type: RangeIteratorType = ...
range_iter64_type: RangeIteratorType = ...
unsigned_range_iter64_type: RangeIteratorType = ...
range_state32_type: RangeType = ...
range_state64_type: RangeType = ...
unsigned_range_state64_type: RangeType = ...

signed_domain: frozenset[Integer] = ...
unsigned_domain: frozenset[Integer] = ...
integer_domain: frozenset[Integer] = ...
real_domain: frozenset[Float] = ...
complex_domain: frozenset[Complex] = ...
number_domain: frozenset[Integer | Float | Complex] = ...

c_bool: MachineBoolean | Boolean = ...
c_int8: MachineInteger | Integer = ...
c_int16: MachineInteger | Integer = ...
c_int32: MachineInteger | Integer = ...
c_int64: MachineInteger | Integer = ...
c_intp: MachineInteger | Integer = ...
c_uint8: MachineInteger | Integer = ...
c_uint16: MachineInteger | Integer = ...
c_uint32: MachineInteger | Integer = ...
c_uint64: MachineInteger | Integer = ...
c_uintp: MachineInteger | Integer = ...
c_float16: MachineFloat | Float = ...
c_float32: MachineFloat | Float = ...
c_float64: MachineFloat | Float = ...

np_bool_: NumPyBoolean | Boolean = ...
np_int8: NumPyInteger | Integer = ...
np_int16: NumPyInteger | Integer = ...
np_int32: NumPyInteger | Integer = ...
np_int64: NumPyInteger | Integer = ...
np_intp: NumPyInteger | Integer = ...
np_uint8: NumPyInteger | Integer = ...
np_uint16: NumPyInteger | Integer = ...
np_uint32: NumPyInteger | Integer = ...
np_uint64: NumPyInteger | Integer = ...
np_uintp: NumPyInteger | Integer = ...
np_float16: NumPyFloat | Float = ...
np_float32: NumPyFloat | Float = ...
np_float64: NumPyFloat | Float = ...
np_float_ = float32
np_double = np_float64
np_complex64: NumPyComplex | Complex = ...
np_complex128: NumPyComplex | Complex = ...

py_bool: PythonBoolean | Boolean = ...
py_int: PythonInteger | Integer = ...
py_float: PythonFloat | Float = ...
py_complex: PythonComplex | Complex = ...

py_signed_domain: frozenset[PythonInteger] | frozenset[Integer] = ...
py_integer_domain: frozenset[PythonInteger] | frozenset[Integer] = ...
py_real_domain: frozenset[PythonFloat] | frozenset[Float] = ...
py_complex_domain: frozenset[PythonComplex] | frozenset[Complex] = ...
py_number_domain: frozenset[PythonInteger | PythonFloat | PythonComplex] | frozenset[Integer | Float | Complex] = ...

np_signed_domain: frozenset[NumPyInteger] | frozenset[Integer] = ...
np_unsigned_domain: frozenset[NumPyInteger] | frozenset[Integer] = ...
np_integer_domain: frozenset[NumPyInteger] | frozenset[Integer] = ...
np_real_domain: frozenset[NumPyFloat] | frozenset[Float] = ...
np_complex_domain: frozenset[NumPyComplex] | frozenset[Complex] = ...
np_number_domain: frozenset[NumPyInteger | NumPyFloat | NumPyComplex] | frozenset[Integer | Float | Complex] = ...

b1 = bool_
i1 = int8
i2 = int16
i4 = int32
i8 = int64
u1 = uint8
u2 = uint16
u4 = uint32
u8 = uint64
f2 = float16
f4 = float32
f8 = float64
c8 = complex64
c16 = complex128
