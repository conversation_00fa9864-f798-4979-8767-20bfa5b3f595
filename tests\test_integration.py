"""
Integration tests for Auto Latent Video Processor.

These tests verify that all components work together correctly
and that the complete application functions as expected.
"""

import pytest
import sys
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Mock PySide6 for testing
sys.modules['PySide6'] = MagicMock()
sys.modules['PySide6.QtWidgets'] = MagicMock()
sys.modules['PySide6.QtCore'] = MagicMock()
sys.modules['PySide6.QtGui'] = MagicMock()

# Import components after mocking
try:
    from ui_foundation import Theme<PERSON>anager, Layout<PERSON>anager
    from project_management import ProjectManager
    from timeline_editor import TimelineEditor, TimelineClip
    from media_library import MediaLibraryWidget, MediaDatabase
    from visual_workflow_builder import WorkflowBuilderWidget, WorkflowGraph
    from advanced_audio_system import AudioMixer, AudioTrack
    from undo_redo_system import UndoRedoManager, PropertyChangeAction
    from enhanced_progress_feedback import FeedbackSystem, ProgressTracker
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    COMPONENTS_AVAILABLE = False
    pytest.skip(f"Components not available: {e}", allow_module_level=True)


class TestComponentIntegration:
    """Test integration between different components."""
    
    def setup_method(self):
        """Setup test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.project_dir = Path(self.temp_dir) / "test_project"
        self.project_dir.mkdir()
    
    def teardown_method(self):
        """Cleanup test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_theme_manager_integration(self):
        """Test theme manager integration with other components."""
        theme_manager = ThemeManager()
        layout_manager = LayoutManager()
        
        # Test theme changes
        theme_manager.set_theme("dark")
        assert theme_manager.current_theme.value == "dark"
        
        theme_manager.set_theme("light")
        assert theme_manager.current_theme.value == "light"
    
    def test_project_manager_integration(self):
        """Test project manager integration with other components."""
        project_manager = ProjectManager()
        undo_manager = UndoRedoManager()
        
        # Create new project
        project_path = self.project_dir / "test.alvp"
        project_manager.create_new_project(str(project_path))
        
        # Test project data integration
        project_data = project_manager.get_project_data()
        assert "metadata" in project_data
        assert "timeline" in project_data
        assert "media_library" in project_data
        assert "workflow" in project_data
    
    def test_timeline_media_library_integration(self):
        """Test integration between timeline and media library."""
        # Create media library
        media_db = MediaDatabase(":memory:")  # In-memory database for testing
        
        # Create timeline
        timeline = TimelineEditor()
        
        # Add media to library
        media_path = "/test/video.mp4"
        # In real implementation, this would add actual media
        
        # Add media to timeline
        clip = TimelineClip(
            id="clip_1",
            name="Test Clip",
            media_path=media_path,
            start_time=0.0,
            duration=10.0
        )
        
        # Test that clip references media correctly
        assert clip.media_path == media_path
        assert clip.duration == 10.0
    
    def test_workflow_audio_integration(self):
        """Test integration between workflow builder and audio system."""
        workflow_builder = WorkflowBuilderWidget()
        audio_mixer = AudioMixer()
        
        # Create workflow
        workflow = WorkflowGraph()
        
        # Add audio processing nodes
        # In real implementation, this would create actual nodes
        
        # Test that workflow can control audio processing
        track = AudioTrack(id="track_1", name="Main Audio")
        audio_mixer.add_track(track)
        
        assert len(audio_mixer.tracks) == 1
        assert audio_mixer.tracks[0].id == "track_1"
    
    def test_undo_redo_integration(self):
        """Test undo/redo integration across components."""
        undo_manager = UndoRedoManager()
        
        # Create test object
        class TestObject:
            def __init__(self):
                self.value = 42
                self.name = "test"
        
        test_obj = TestObject()
        
        # Create and execute action
        action = PropertyChangeAction(test_obj, "value", 42, 100)
        undo_manager.execute_action(action)
        
        assert test_obj.value == 100
        assert undo_manager.can_undo()
        
        # Test undo
        undo_manager.undo()
        assert test_obj.value == 42
        assert undo_manager.can_redo()
        
        # Test redo
        undo_manager.redo()
        assert test_obj.value == 100
    
    def test_feedback_system_integration(self):
        """Test feedback system integration with other components."""
        feedback_system = FeedbackSystem()
        
        # Test progress tracking
        progress_id = feedback_system.progress_tracker.start_progress(
            "Test Task", "Testing integration", total=100
        )
        
        assert progress_id in feedback_system.progress_tracker.active_progress
        
        # Update progress
        feedback_system.progress_tracker.update_progress(progress_id, 50)
        
        progress_info = feedback_system.progress_tracker.get_progress_info(progress_id)
        assert progress_info.current == 50
        assert progress_info.percentage == 50.0
        
        # Complete progress
        feedback_system.progress_tracker.complete_progress(progress_id)
        
        assert progress_id not in feedback_system.progress_tracker.active_progress
    
    def test_complete_workflow_integration(self):
        """Test a complete workflow using multiple components."""
        # Initialize all systems
        theme_manager = ThemeManager()
        project_manager = ProjectManager()
        undo_manager = UndoRedoManager()
        feedback_system = FeedbackSystem()
        
        # Create project
        project_path = self.project_dir / "integration_test.alvp"
        project_manager.create_new_project(str(project_path))
        
        # Start progress tracking
        progress_id = feedback_system.progress_tracker.start_progress(
            "Integration Test", "Testing complete workflow"
        )
        
        # Create timeline with clips
        timeline = TimelineEditor()
        
        # Create audio mixer
        audio_mixer = AudioMixer()
        track = AudioTrack(id="main", name="Main Track")
        audio_mixer.add_track(track)
        
        # Create workflow
        workflow = WorkflowGraph()
        
        # Test undo/redo with multiple operations
        undo_manager.start_group("Integration Test Operations")
        
        # Simulate some operations
        test_obj = type('TestObj', (), {'value': 0})()
        
        action1 = PropertyChangeAction(test_obj, "value", 0, 25)
        undo_manager.execute_action(action1)
        
        action2 = PropertyChangeAction(test_obj, "value", 25, 50)
        undo_manager.execute_action(action2)
        
        undo_manager.end_group()
        
        # Update progress
        feedback_system.progress_tracker.update_progress(progress_id, 75)
        
        # Test that everything is working together
        assert test_obj.value == 50
        assert undo_manager.can_undo()
        assert len(audio_mixer.tracks) == 1
        
        # Complete the workflow
        feedback_system.progress_tracker.complete_progress(progress_id)
        
        # Save project
        project_manager.save_project()
        
        # Verify project was saved
        assert project_path.exists()


class TestPerformanceIntegration:
    """Test performance aspects of component integration."""
    
    def test_memory_usage(self):
        """Test memory usage when loading all components."""
        import gc
        
        # Force garbage collection
        gc.collect()
        
        # Create all major components
        components = []
        
        try:
            components.append(ThemeManager())
            components.append(LayoutManager())
            components.append(ProjectManager())
            components.append(UndoRedoManager())
            components.append(FeedbackSystem())
            
            # Test that components are created successfully
            assert len(components) == 5
            
            # Test basic functionality
            for component in components:
                assert component is not None
                
        finally:
            # Cleanup
            del components
            gc.collect()
    
    def test_startup_time(self):
        """Test application startup time."""
        import time
        
        start_time = time.time()
        
        # Simulate application startup
        theme_manager = ThemeManager()
        project_manager = ProjectManager()
        undo_manager = UndoRedoManager()
        feedback_system = FeedbackSystem()
        
        end_time = time.time()
        startup_time = end_time - start_time
        
        # Startup should be reasonably fast (< 2 seconds)
        assert startup_time < 2.0
        
        # Cleanup
        del theme_manager, project_manager, undo_manager, feedback_system
    
    def test_concurrent_operations(self):
        """Test concurrent operations across components."""
        import threading
        import time
        
        feedback_system = FeedbackSystem()
        undo_manager = UndoRedoManager()
        
        results = []
        
        def progress_task():
            """Simulate a progress task."""
            progress_id = feedback_system.progress_tracker.start_progress(
                "Concurrent Task", total=10
            )
            
            for i in range(10):
                feedback_system.progress_tracker.update_progress(progress_id, i + 1)
                time.sleep(0.01)  # Small delay
            
            feedback_system.progress_tracker.complete_progress(progress_id)
            results.append("progress_done")
        
        def undo_task():
            """Simulate undo/redo operations."""
            test_obj = type('TestObj', (), {'value': 0})()
            
            for i in range(5):
                action = PropertyChangeAction(test_obj, "value", i, i + 1)
                undo_manager.execute_action(action)
                time.sleep(0.01)  # Small delay
            
            results.append("undo_done")
        
        # Start concurrent tasks
        thread1 = threading.Thread(target=progress_task)
        thread2 = threading.Thread(target=undo_task)
        
        thread1.start()
        thread2.start()
        
        # Wait for completion
        thread1.join(timeout=5.0)
        thread2.join(timeout=5.0)
        
        # Verify both tasks completed
        assert "progress_done" in results
        assert "undo_done" in results


class TestErrorHandling:
    """Test error handling in component integration."""
    
    def test_component_failure_recovery(self):
        """Test recovery when individual components fail."""
        # Test with invalid theme
        theme_manager = ThemeManager()
        
        # Should handle invalid theme gracefully
        try:
            theme_manager.set_theme("invalid_theme")
            # Should either ignore or use default
        except Exception:
            pytest.fail("Theme manager should handle invalid themes gracefully")
    
    def test_project_corruption_handling(self):
        """Test handling of corrupted project files."""
        project_manager = ProjectManager()
        
        # Create corrupted project file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.alvp', delete=False) as f:
            f.write("invalid json content")
            corrupted_file = f.name
        
        try:
            # Should handle corrupted file gracefully
            result = project_manager.load_project(corrupted_file)
            assert result is False  # Should fail gracefully
        finally:
            os.unlink(corrupted_file)
    
    def test_missing_dependencies_handling(self):
        """Test handling when optional dependencies are missing."""
        # This would test graceful degradation when optional libraries
        # like advanced audio processing libraries are not available
        
        # For now, just test that components can be created
        # even with limited functionality
        try:
            audio_mixer = AudioMixer()
            assert audio_mixer is not None
        except ImportError:
            # Should handle missing dependencies gracefully
            pass


class TestDataFlow:
    """Test data flow between components."""
    
    def test_project_data_flow(self):
        """Test data flow in project save/load cycle."""
        project_manager = ProjectManager()
        
        # Create project with test data
        project_path = tempfile.mktemp(suffix='.alvp')
        
        try:
            project_manager.create_new_project(project_path)
            
            # Add some test data
            project_data = project_manager.get_project_data()
            project_data['test_field'] = 'test_value'
            project_manager.set_project_data(project_data)
            
            # Save project
            project_manager.save_project()
            
            # Create new manager and load project
            new_manager = ProjectManager()
            result = new_manager.load_project(project_path)
            
            assert result is True
            
            # Verify data was preserved
            loaded_data = new_manager.get_project_data()
            assert loaded_data.get('test_field') == 'test_value'
            
        finally:
            if os.path.exists(project_path):
                os.unlink(project_path)
    
    def test_undo_redo_data_consistency(self):
        """Test data consistency in undo/redo operations."""
        undo_manager = UndoRedoManager()
        
        # Create test object with complex data
        test_obj = type('TestObj', (), {
            'simple_value': 42,
            'complex_value': {'nested': {'data': [1, 2, 3]}}
        })()
        
        original_complex = test_obj.complex_value.copy()
        new_complex = {'nested': {'data': [4, 5, 6]}}
        
        # Create action with complex data
        action = PropertyChangeAction(
            test_obj, 'complex_value', original_complex, new_complex
        )
        
        # Execute action
        undo_manager.execute_action(action)
        assert test_obj.complex_value == new_complex
        
        # Undo action
        undo_manager.undo()
        assert test_obj.complex_value == original_complex
        
        # Redo action
        undo_manager.redo()
        assert test_obj.complex_value == new_complex


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
