"""
Main Application for Auto Latent Video Processor.

This is the primary entry point that integrates all components into a
cohesive application with proper initialization, error handling, and shutdown.
"""

import sys
import os
import logging
import traceback
from pathlib import Path
from typing import Optional

# Add project root to path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Qt imports
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QMenuBar, QStatusBar, QSplitter, QTabWidget,
    QMessageBox, QProgressDialog
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QIcon, QPixmap, QAction

# Import all our components
try:
    from ui_foundation import ThemeManager, LayoutManager, ResponsiveDesign
    from project_management import ProjectManager, ProjectWidget
    from timeline_editor import TimelineEditor
    from media_library import MediaLibraryWidget
    from visual_workflow_builder import WorkflowBuilderWidget
    from advanced_audio_system import AudioMixerWidget
    from undo_redo_system import UndoRedoManager, UndoRedoWidget
    from enhanced_progress_feedback import FeedbackSystem, FeedbackWidget
    
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.error(f"Failed to import components: {e}")
    COMPONENTS_AVAILABLE = False


class MainApplication(QMainWindow):
    """Main application window integrating all components."""
    
    def __init__(self):
        super().__init__()
        
        # Initialize core systems
        self.theme_manager = ThemeManager()
        self.layout_manager = LayoutManager()
        self.project_manager = ProjectManager()
        self.undo_manager = UndoRedoManager()
        self.feedback_system = FeedbackSystem()
        
        # Component widgets
        self.timeline_editor = None
        self.media_library = None
        self.workflow_builder = None
        self.audio_mixer = None
        self.feedback_widget = None
        
        # Setup application
        self._setup_ui()
        self._setup_menus()
        self._setup_status_bar()
        self._connect_signals()
        self._apply_initial_theme()
        
        # Initialize with default project
        self._create_new_project()
        
        logging.info("Main application initialized successfully")
    
    def _setup_ui(self):
        """Setup the main user interface."""
        self.setWindowTitle("Auto Latent Video Processor")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1000)
        
        # Central widget with splitter layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Main splitter (horizontal)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel: Media Library and Project
        left_panel = self._create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # Center panel: Timeline and Workflow
        center_panel = self._create_center_panel()
        main_splitter.addWidget(center_panel)
        
        # Right panel: Audio Mixer and Feedback
        right_panel = self._create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        main_splitter.setSizes([300, 800, 300])
        
        layout.addWidget(main_splitter)
        
        # Store splitter for layout management
        self.main_splitter = main_splitter
    
    def _create_left_panel(self) -> QWidget:
        """Create the left panel with media library and project management."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for different left panel views
        left_tabs = QTabWidget()
        
        # Media Library tab
        self.media_library = MediaLibraryWidget()
        left_tabs.addTab(self.media_library, "Media Library")
        
        # Project Management tab
        self.project_widget = ProjectWidget(self.project_manager)
        left_tabs.addTab(self.project_widget, "Project")
        
        layout.addWidget(left_tabs)
        return panel
    
    def _create_center_panel(self) -> QWidget:
        """Create the center panel with timeline and workflow."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for main work areas
        center_tabs = QTabWidget()
        
        # Timeline Editor tab
        self.timeline_editor = TimelineEditor()
        center_tabs.addTab(self.timeline_editor, "Timeline")
        
        # Workflow Builder tab
        self.workflow_builder = WorkflowBuilderWidget()
        center_tabs.addTab(self.workflow_builder, "Workflow")
        
        layout.addWidget(center_tabs)
        return panel
    
    def _create_right_panel(self) -> QWidget:
        """Create the right panel with audio mixer and feedback."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for right panel views
        right_tabs = QTabWidget()
        
        # Audio Mixer tab
        self.audio_mixer = AudioMixerWidget()
        right_tabs.addTab(self.audio_mixer, "Audio")
        
        # Feedback System tab
        self.feedback_widget = FeedbackWidget(self.feedback_system)
        right_tabs.addTab(self.feedback_widget, "Progress")
        
        layout.addWidget(right_tabs)
        return panel
    
    def _setup_menus(self):
        """Setup the application menu bar."""
        menubar = self.menuBar()
        
        # File Menu
        file_menu = menubar.addMenu("File")
        
        new_action = QAction("New Project", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self._create_new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction("Open Project", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self._open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction("Save Project", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self._save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit Menu
        edit_menu = menubar.addMenu("Edit")
        
        undo_action = QAction("Undo", self)
        undo_action.setShortcut("Ctrl+Z")
        undo_action.triggered.connect(self.undo_manager.undo)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("Redo", self)
        redo_action.setShortcut("Ctrl+Y")
        redo_action.triggered.connect(self.undo_manager.redo)
        edit_menu.addAction(redo_action)
        
        # View Menu
        view_menu = menubar.addMenu("View")
        
        theme_menu = view_menu.addMenu("Theme")
        
        light_theme_action = QAction("Light Theme", self)
        light_theme_action.triggered.connect(lambda: self._change_theme("light"))
        theme_menu.addAction(light_theme_action)
        
        dark_theme_action = QAction("Dark Theme", self)
        dark_theme_action.triggered.connect(lambda: self._change_theme("dark"))
        theme_menu.addAction(dark_theme_action)
        
        # Tools Menu
        tools_menu = menubar.addMenu("Tools")
        
        preferences_action = QAction("Preferences", self)
        preferences_action.triggered.connect(self._show_preferences)
        tools_menu.addAction(preferences_action)
        
        # Help Menu
        help_menu = menubar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _setup_status_bar(self):
        """Setup the status bar."""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready")
        
        # Add permanent widgets to status bar
        self.status_bar.addPermanentWidget(QWidget(), 1)  # Spacer
    
    def _connect_signals(self):
        """Connect signals between components."""
        # Project management signals
        self.project_manager.project_loaded.connect(self._on_project_loaded)
        self.project_manager.project_saved.connect(self._on_project_saved)
        
        # Feedback system signals
        self.feedback_system.progress_tracker.progress_started.connect(
            lambda pid: self.status_bar.showMessage("Processing...")
        )
        self.feedback_system.progress_tracker.progress_completed.connect(
            lambda pid: self.status_bar.showMessage("Ready")
        )
        
        # Theme manager signals
        self.theme_manager.theme_changed.connect(self._on_theme_changed)
    
    def _apply_initial_theme(self):
        """Apply the initial theme."""
        self.theme_manager.set_theme("dark")  # Default to dark theme
    
    def _create_new_project(self):
        """Create a new project."""
        if self.project_manager.create_new_project():
            self.status_bar.showMessage("New project created")
            logging.info("New project created")
    
    def _open_project(self):
        """Open an existing project."""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Project", "", "Project Files (*.alvp);;All Files (*)"
        )
        
        if file_path:
            if self.project_manager.load_project(file_path):
                self.status_bar.showMessage(f"Opened project: {Path(file_path).name}")
                logging.info(f"Project opened: {file_path}")
            else:
                QMessageBox.warning(self, "Error", "Failed to open project")
    
    def _save_project(self):
        """Save the current project."""
        if self.project_manager.save_project():
            self.status_bar.showMessage("Project saved")
            logging.info("Project saved")
        else:
            QMessageBox.warning(self, "Error", "Failed to save project")
    
    def _change_theme(self, theme_name: str):
        """Change the application theme."""
        self.theme_manager.set_theme(theme_name)
        self.status_bar.showMessage(f"Theme changed to {theme_name}")
    
    def _show_preferences(self):
        """Show preferences dialog."""
        QMessageBox.information(self, "Preferences", "Preferences dialog coming soon!")
    
    def _show_about(self):
        """Show about dialog."""
        about_text = """
        <h2>Auto Latent Video Processor</h2>
        <p>Professional AI-powered video processing suite</p>
        <p>Version: 1.0.0</p>
        <p>Features:</p>
        <ul>
            <li>AI-powered lip sync</li>
            <li>Professional timeline editing</li>
            <li>Advanced audio processing</li>
            <li>Visual workflow builder</li>
            <li>Comprehensive media management</li>
        </ul>
        """
        QMessageBox.about(self, "About", about_text)
    
    def _on_project_loaded(self, project_path: str):
        """Handle project loaded event."""
        self.setWindowTitle(f"Auto Latent Video Processor - {Path(project_path).stem}")
    
    def _on_project_saved(self, project_path: str):
        """Handle project saved event."""
        pass
    
    def _on_theme_changed(self, theme_name: str):
        """Handle theme change event."""
        logging.info(f"Theme changed to: {theme_name}")
    
    def closeEvent(self, event):
        """Handle application close event."""
        # Check for unsaved changes
        if self.project_manager.has_unsaved_changes():
            reply = QMessageBox.question(
                self, "Unsaved Changes",
                "You have unsaved changes. Do you want to save before closing?",
                QMessageBox.StandardButton.Save | 
                QMessageBox.StandardButton.Discard | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Save:
                if not self.project_manager.save_project():
                    event.ignore()
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                event.ignore()
                return
        
        # Save layout and settings
        self.layout_manager.save_layout("default", self.main_splitter.sizes())
        
        logging.info("Application closing")
        event.accept()


def setup_logging():
    """Setup application logging."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "application.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    
    # Check if components are available
    if not COMPONENTS_AVAILABLE:
        print("❌ Required components not available. Please run setup_integration.py first.")
        return 1
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Auto Latent Video Processor")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Auto Latent Video Processor")
    
    try:
        # Create and show main window
        window = MainApplication()
        window.show()
        
        logging.info("Application started successfully")
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        logging.error(f"Application failed to start: {e}")
        logging.error(traceback.format_exc())
        
        # Show error dialog
        QMessageBox.critical(
            None, "Application Error",
            f"Failed to start application:\n{str(e)}\n\nCheck logs for details."
        )
        
        return 1


if __name__ == "__main__":
    sys.exit(main())
