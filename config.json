{"comfyui": {"url": "127.0.0.1:8188", "output_base_dir": "", "timeout_seconds": 300, "poll_interval_seconds": 1.0, "max_retries": 3}, "workflow": {"latentsync_workflow_path": "workflows/latentsync_workflow_api.json", "framepack_workflow_path": "workflows/framepack_comfyui1.json", "video_node_id": "1", "audio_node_id": "4", "output_node_id": "5", "latentsync_node_id": "3", "videocombine_node_id": "5", "videolength_node_id": "2"}, "parameters": {"seed": 685, "lips_expression": 2.1, "inference_steps": 20, "crf": 19, "silent_padding_sec": 0.3, "framepack_prompt": "hosting a podcast", "framepack_seed": 0, "framepack_random": false}, "tts": {"zonos_api_key": "zsk-2c7379c3fac8c452363a4ae086d0e541d54b64935486da8dbd343afbca9597d5", "output_dir": "tts-output", "use_local_tts": false, "max_concurrent_jobs": 3, "timeout_seconds": 30, "retry_attempts": 2}, "media": {"input_dir": "input", "output_dir": "output", "temp_dir": "temp", "voices_dir": "voices", "video_extensions": [".mp4", ".avi", ".mov", ".mkv", ".webm"], "image_extensions": [".jpg", ".jpeg", ".png", ".bmp", ".gif", ".webp", ".tiff"], "audio_extensions": [".wav", ".mp3", ".flac", ".ogg", ".aac"], "thumbnail_size": [100, 60], "enable_thumbnails": true}, "ui": {"window_title": "Auto Latent Video Processor", "window_geometry": [100, 100, 1200, 800], "log_level": "INFO", "auto_refresh_interval": 5000, "speaker_folders": {"s1_vid": "speaker1_video", "s2_vid": "speaker2_video", "s1_aud": "speaker1_audio", "s2_aud": "speaker2_audio"}}, "version": "1.0.0", "debug_mode": false}