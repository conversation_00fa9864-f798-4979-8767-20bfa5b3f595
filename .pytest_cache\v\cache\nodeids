["tests/test_workflow_processing.py::TestComfyUIInteraction::test_get_comfyui_status_error", "tests/test_workflow_processing.py::TestComfyUIInteraction::test_get_comfyui_status_success", "tests/test_workflow_processing.py::TestComfyUIInteraction::test_submit_workflow_http_error", "tests/test_workflow_processing.py::TestComfyUIInteraction::test_submit_workflow_no_prompt_id", "tests/test_workflow_processing.py::TestComfyUIInteraction::test_submit_workflow_success", "tests/test_workflow_processing.py::TestFramePackWorkflow::test_update_framepack_workflow_random_seed", "tests/test_workflow_processing.py::TestFramePackWorkflow::test_update_framepack_workflow_success", "tests/test_workflow_processing.py::TestPollingStatus::test_poll_comfyui_status_success", "tests/test_workflow_processing.py::TestPollingStatus::test_poll_comfyui_status_timeout", "tests/test_workflow_processing.py::TestWorkflowLoading::test_load_workflow_file_not_found", "tests/test_workflow_processing.py::TestWorkflowLoading::test_load_workflow_invalid_json", "tests/test_workflow_processing.py::TestWorkflowLoading::test_load_workflow_permission_error", "tests/test_workflow_processing.py::TestWorkflowLoading::test_load_workflow_success", "tests/test_workflow_processing.py::TestWorkflowUpdating::test_update_workflow_deep_copy", "tests/test_workflow_processing.py::TestWorkflowUpdating::test_update_workflow_missing_nodes", "tests/test_workflow_processing.py::TestWorkflowUpdating::test_update_workflow_success"]