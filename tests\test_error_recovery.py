"""
Tests for the enhanced error recovery system.
"""

import time
import pytest
from unittest.mock import Mock, patch, MagicMock
import threading
from datetime import datetime, timedelta

from error_recovery import (
    ErrorRecoveryManager,
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitBreakerState,
    CircuitBreakerOpenError,
    RetryConfig,
    ErrorSeverity,
    RecoveryStrategy,
    ErrorContext,
    with_retry,
    with_circuit_breaker,
    with_error_recovery,
    get_error_recovery_manager
)


class TestRetryConfig:
    """Test retry configuration."""
    
    def test_default_retry_config(self):
        """Test default retry configuration values."""
        config = RetryConfig()
        
        assert config.max_attempts == 3
        assert config.base_delay == 1.0
        assert config.max_delay == 60.0
        assert config.exponential_base == 2.0
        assert config.jitter is True
        assert config.backoff_strategy == "exponential"
    
    def test_custom_retry_config(self):
        """Test custom retry configuration."""
        config = RetryConfig(
            max_attempts=5,
            base_delay=0.5,
            max_delay=30.0,
            exponential_base=1.5,
            jitter=False,
            backoff_strategy="linear"
        )
        
        assert config.max_attempts == 5
        assert config.base_delay == 0.5
        assert config.max_delay == 30.0
        assert config.exponential_base == 1.5
        assert config.jitter is False
        assert config.backoff_strategy == "linear"


class TestCircuitBreaker:
    """Test circuit breaker functionality."""
    
    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization."""
        config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
        breaker = CircuitBreaker(config, "test")
        
        assert breaker.state == CircuitBreakerState.CLOSED
        assert breaker.failure_count == 0
        assert breaker.success_count == 0
        assert breaker.name == "test"
    
    def test_circuit_breaker_success_flow(self):
        """Test circuit breaker with successful operations."""
        config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
        breaker = CircuitBreaker(config, "test")
        
        def successful_operation():
            return "success"
        
        # Multiple successful calls should work
        for _ in range(5):
            result = breaker.call(successful_operation)
            assert result == "success"
            assert breaker.state == CircuitBreakerState.CLOSED
            assert breaker.failure_count == 0
    
    def test_circuit_breaker_failure_flow(self):
        """Test circuit breaker with failing operations."""
        config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
        breaker = CircuitBreaker(config, "test")
        
        def failing_operation():
            raise ValueError("Test error")
        
        # First few failures should still allow calls
        for i in range(2):
            with pytest.raises(ValueError):
                breaker.call(failing_operation)
            assert breaker.state == CircuitBreakerState.CLOSED
            assert breaker.failure_count == i + 1
        
        # Third failure should open the circuit
        with pytest.raises(ValueError):
            breaker.call(failing_operation)
        assert breaker.state == CircuitBreakerState.OPEN
        assert breaker.failure_count == 3
        
        # Further calls should be blocked
        with pytest.raises(CircuitBreakerOpenError):
            breaker.call(failing_operation)
    
    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after timeout."""
        config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=0.1, success_threshold=2)
        breaker = CircuitBreaker(config, "test")
        
        def failing_operation():
            raise ValueError("Test error")
        
        def successful_operation():
            return "success"
        
        # Trigger circuit breaker to open
        for _ in range(2):
            with pytest.raises(ValueError):
                breaker.call(failing_operation)
        
        assert breaker.state == CircuitBreakerState.OPEN
        
        # Wait for recovery timeout
        time.sleep(0.2)
        
        # First call after timeout should transition to HALF_OPEN
        result = breaker.call(successful_operation)
        assert result == "success"
        assert breaker.state == CircuitBreakerState.HALF_OPEN
        
        # Second successful call should close the circuit
        result = breaker.call(successful_operation)
        assert result == "success"
        assert breaker.state == CircuitBreakerState.CLOSED
        assert breaker.failure_count == 0


class TestErrorRecoveryManager:
    """Test error recovery manager."""
    
    def test_error_recovery_manager_initialization(self):
        """Test error recovery manager initialization."""
        manager = ErrorRecoveryManager()
        
        assert len(manager.retry_configs) > 0
        assert len(manager.circuit_breakers) > 0
        assert "network" in manager.retry_configs
        assert "comfyui" in manager.circuit_breakers
    
    def test_register_error_handler(self):
        """Test registering custom error handlers."""
        manager = ErrorRecoveryManager()
        
        def custom_handler(error):
            return "handled"
        
        manager.register_error_handler("test_operation", custom_handler)
        assert "test_operation" in manager.error_handlers
        assert manager.error_handlers["test_operation"] == custom_handler
    
    def test_register_fallback_handler(self):
        """Test registering fallback handlers."""
        manager = ErrorRecoveryManager()
        
        def fallback_handler(*args, **kwargs):
            return "fallback_result"
        
        manager.register_fallback_handler("test_operation", fallback_handler)
        assert "test_operation" in manager.fallback_handlers
        assert manager.fallback_handlers["test_operation"] == fallback_handler
    
    def test_retry_decorator_success(self):
        """Test retry decorator with successful operation."""
        manager = ErrorRecoveryManager()
        
        call_count = 0
        
        @manager.with_retry("test_operation")
        def test_function():
            nonlocal call_count
            call_count += 1
            return "success"
        
        result = test_function()
        assert result == "success"
        assert call_count == 1
    
    def test_retry_decorator_with_failures(self):
        """Test retry decorator with initial failures."""
        manager = ErrorRecoveryManager()
        
        call_count = 0
        
        @manager.with_retry("test_operation", RetryConfig(max_attempts=3, base_delay=0.01))
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Test error")
            return "success"
        
        result = test_function()
        assert result == "success"
        assert call_count == 3
    
    def test_retry_decorator_exhausted(self):
        """Test retry decorator when all attempts are exhausted."""
        manager = ErrorRecoveryManager()
        
        call_count = 0
        
        @manager.with_retry("test_operation", RetryConfig(max_attempts=2, base_delay=0.01))
        def test_function():
            nonlocal call_count
            call_count += 1
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            test_function()
        assert call_count == 2
    
    def test_circuit_breaker_decorator(self):
        """Test circuit breaker decorator."""
        manager = ErrorRecoveryManager()
        
        # Register a circuit breaker for testing
        config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=0.1)
        manager.circuit_breakers["test_operation"] = CircuitBreaker(config, "test")
        
        call_count = 0
        
        @manager.with_circuit_breaker("test_operation")
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise ValueError("Test error")
            return "success"
        
        # First two calls should fail and open circuit
        with pytest.raises(ValueError):
            test_function()
        with pytest.raises(ValueError):
            test_function()
        
        # Third call should be blocked by circuit breaker
        with pytest.raises(CircuitBreakerOpenError):
            test_function()
    
    def test_error_recovery_decorator_with_fallback(self):
        """Test comprehensive error recovery with fallback."""
        manager = ErrorRecoveryManager()
        
        # Register fallback handler
        def fallback_handler(*args, **kwargs):
            return "fallback_result"
        
        manager.register_fallback_handler("test_operation", fallback_handler)
        
        @manager.with_error_recovery("test_operation", ErrorSeverity.LOW)
        def test_function():
            raise FileNotFoundError("Test file not found")
        
        result = test_function()
        assert result == "fallback_result"
    
    def test_error_statistics(self):
        """Test error statistics collection."""
        manager = ErrorRecoveryManager()
        
        # Simulate some errors
        error1 = ErrorContext(
            operation="test_op1",
            timestamp=datetime.now(),
            severity=ErrorSeverity.MEDIUM,
            error_type="ValueError",
            error_message="Test error 1",
            stack_trace="test stack trace"
        )
        
        error2 = ErrorContext(
            operation="test_op2",
            timestamp=datetime.now(),
            severity=ErrorSeverity.HIGH,
            error_type="ConnectionError",
            error_message="Test error 2",
            stack_trace="test stack trace"
        )
        
        manager._record_error(error1)
        manager._record_error(error2)
        
        stats = manager.get_error_statistics()
        
        assert stats["total_errors"] == 2
        assert stats["recent_errors_24h"] == 2
        assert "test_op1" in stats["errors_by_operation"]
        assert "test_op2" in stats["errors_by_operation"]
        assert "medium" in stats["errors_by_severity"]
        assert "high" in stats["errors_by_severity"]


class TestDelayCalculation:
    """Test delay calculation for retries."""
    
    def test_exponential_backoff(self):
        """Test exponential backoff calculation."""
        manager = ErrorRecoveryManager()
        config = RetryConfig(
            base_delay=1.0,
            exponential_base=2.0,
            max_delay=10.0,
            jitter=False
        )
        
        # Test exponential growth
        delay0 = manager._calculate_delay(0, config)
        delay1 = manager._calculate_delay(1, config)
        delay2 = manager._calculate_delay(2, config)
        
        assert delay0 == 1.0
        assert delay1 == 2.0
        assert delay2 == 4.0
    
    def test_linear_backoff(self):
        """Test linear backoff calculation."""
        manager = ErrorRecoveryManager()
        config = RetryConfig(
            base_delay=1.0,
            backoff_strategy="linear",
            max_delay=10.0,
            jitter=False
        )
        
        delay0 = manager._calculate_delay(0, config)
        delay1 = manager._calculate_delay(1, config)
        delay2 = manager._calculate_delay(2, config)
        
        assert delay0 == 1.0
        assert delay1 == 2.0
        assert delay2 == 3.0
    
    def test_fixed_backoff(self):
        """Test fixed backoff calculation."""
        manager = ErrorRecoveryManager()
        config = RetryConfig(
            base_delay=1.5,
            backoff_strategy="fixed",
            max_delay=10.0,
            jitter=False
        )
        
        delay0 = manager._calculate_delay(0, config)
        delay1 = manager._calculate_delay(1, config)
        delay2 = manager._calculate_delay(2, config)
        
        assert delay0 == 1.5
        assert delay1 == 1.5
        assert delay2 == 1.5
    
    def test_max_delay_limit(self):
        """Test maximum delay limit."""
        manager = ErrorRecoveryManager()
        config = RetryConfig(
            base_delay=1.0,
            exponential_base=2.0,
            max_delay=5.0,
            jitter=False
        )
        
        # Large attempt number should be capped at max_delay
        delay = manager._calculate_delay(10, config)
        assert delay == 5.0
    
    def test_jitter_effect(self):
        """Test jitter effect on delay calculation."""
        manager = ErrorRecoveryManager()
        config = RetryConfig(
            base_delay=2.0,
            exponential_base=2.0,
            max_delay=10.0,
            jitter=True
        )
        
        # With jitter, delays should vary
        delays = [manager._calculate_delay(1, config) for _ in range(10)]
        
        # All delays should be between 1.0 and 4.0 (base_delay * exponential_base with jitter)
        for delay in delays:
            assert 1.0 <= delay <= 4.0
        
        # There should be some variation (not all the same)
        assert len(set(delays)) > 1


class TestGlobalFunctions:
    """Test global convenience functions."""
    
    def test_global_error_recovery_manager(self):
        """Test global error recovery manager access."""
        manager1 = get_error_recovery_manager()
        manager2 = get_error_recovery_manager()
        
        assert manager1 is manager2
        assert isinstance(manager1, ErrorRecoveryManager)
    
    def test_global_retry_decorator(self):
        """Test global retry decorator."""
        call_count = 0
        
        @with_retry("test_operation", RetryConfig(max_attempts=2, base_delay=0.01))
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ValueError("Test error")
            return "success"
        
        result = test_function()
        assert result == "success"
        assert call_count == 2
    
    def test_global_circuit_breaker_decorator(self):
        """Test global circuit breaker decorator."""
        # This test would require setting up a circuit breaker in the global manager
        # For now, just test that the decorator can be applied
        @with_circuit_breaker("test_operation")
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_global_error_recovery_decorator(self):
        """Test global error recovery decorator."""
        @with_error_recovery("test_operation", ErrorSeverity.LOW)
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
