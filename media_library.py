"""
Media Library System for Auto Latent Video Processor.

This module provides comprehensive media asset management including tagging,
metadata, auto-organization, duplicate detection, and advanced search/filter.
"""

import os
import json
import hashlib
import logging
import mimetypes
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set, Tuple, Callable
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum
import threading
import time
import shutil

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QScrollArea,
    QFrame, QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox,
    QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem,
    QTabWidget, QSplitter, QProgressBar, QMenu,
    QFileDialog, QMessageBox, QDialog, QDialogButtonBox,
    QTextEdit, QSpinBox, QSlider, QGroupBox
)
from PySide6.QtCore import Qt, Signal, QObject, QThread, QTimer, QSize
from PySide6.QtGui import QPixmap, QIcon, QFont, QPalette, QColor, QAction


class MediaType(Enum):
    """Media file types."""
    VIDEO = "video"
    AUDIO = "audio"
    IMAGE = "image"
    TEXT = "text"
    PROJECT = "project"
    OTHER = "other"


class SortOrder(Enum):
    """Sort order options."""
    NAME_ASC = "name_asc"
    NAME_DESC = "name_desc"
    DATE_ASC = "date_asc"
    DATE_DESC = "date_desc"
    SIZE_ASC = "size_asc"
    SIZE_DESC = "size_desc"
    TYPE_ASC = "type_asc"
    TYPE_DESC = "type_desc"
    RATING_ASC = "rating_asc"
    RATING_DESC = "rating_desc"


@dataclass
class MediaMetadata:
    """Comprehensive metadata for media files."""
    # Basic file information
    file_path: str
    file_name: str
    file_size: int
    file_type: MediaType
    mime_type: str
    created_date: datetime
    modified_date: datetime
    
    # Media-specific metadata
    duration: Optional[float] = None  # For video/audio
    width: Optional[int] = None       # For video/image
    height: Optional[int] = None      # For video/image
    fps: Optional[float] = None       # For video
    bitrate: Optional[int] = None     # For video/audio
    codec: Optional[str] = None       # For video/audio
    
    # User-defined metadata
    title: str = ""
    description: str = ""
    tags: Set[str] = field(default_factory=set)
    rating: int = 0  # 0-5 stars
    favorite: bool = False
    
    # Organization
    category: str = ""
    project: str = ""
    speaker: str = ""  # For audio files
    
    # Technical metadata
    checksum: str = ""
    thumbnail_path: str = ""
    
    # Usage tracking
    usage_count: int = 0
    last_used: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['tags'] = list(self.tags)
        data['created_date'] = self.created_date.isoformat()
        data['modified_date'] = self.modified_date.isoformat()
        if self.last_used:
            data['last_used'] = self.last_used.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MediaMetadata':
        """Create from dictionary."""
        data = data.copy()
        data['tags'] = set(data.get('tags', []))
        data['created_date'] = datetime.fromisoformat(data['created_date'])
        data['modified_date'] = datetime.fromisoformat(data['modified_date'])
        if data.get('last_used'):
            data['last_used'] = datetime.fromisoformat(data['last_used'])
        return cls(**data)


class MediaScanner(QThread):
    """Background thread for scanning media files."""
    
    # Signals
    file_found = Signal(str)  # file_path
    scan_progress = Signal(int, int)  # current, total
    scan_complete = Signal(int)  # total_files
    
    def __init__(self, directories: List[str], extensions: Set[str]):
        super().__init__()
        self.directories = directories
        self.extensions = extensions
        self.should_stop = False
        
    def run(self):
        """Run the media scanning process."""
        total_files = 0
        processed_files = 0
        
        # First pass: count total files
        for directory in self.directories:
            if self.should_stop:
                return
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in self.extensions):
                        total_files += 1
        
        # Second pass: process files
        for directory in self.directories:
            if self.should_stop:
                return
                
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if self.should_stop:
                        return
                    
                    if any(file.lower().endswith(ext) for ext in self.extensions):
                        file_path = os.path.join(root, file)
                        self.file_found.emit(file_path)
                        processed_files += 1
                        self.scan_progress.emit(processed_files, total_files)
        
        self.scan_complete.emit(total_files)
    
    def stop(self):
        """Stop the scanning process."""
        self.should_stop = True


class DuplicateDetector:
    """Detects duplicate media files based on content hash."""
    
    def __init__(self):
        self.file_hashes: Dict[str, List[str]] = {}  # hash -> [file_paths]
        
    def add_file(self, file_path: str) -> str:
        """Add a file and return its hash."""
        file_hash = self._calculate_hash(file_path)
        
        if file_hash not in self.file_hashes:
            self.file_hashes[file_hash] = []
        
        if file_path not in self.file_hashes[file_hash]:
            self.file_hashes[file_hash].append(file_path)
        
        return file_hash
    
    def get_duplicates(self) -> Dict[str, List[str]]:
        """Get all duplicate file groups."""
        return {hash_val: paths for hash_val, paths in self.file_hashes.items() 
                if len(paths) > 1}
    
    def is_duplicate(self, file_path: str) -> bool:
        """Check if a file is a duplicate."""
        file_hash = self._calculate_hash(file_path)
        return len(self.file_hashes.get(file_hash, [])) > 1
    
    def _calculate_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file content."""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logging.error(f"Failed to calculate hash for {file_path}: {e}")
            return ""


class MediaOrganizer:
    """Automatically organizes media files based on metadata."""
    
    def __init__(self, base_directory: str):
        self.base_directory = Path(base_directory)
        self.organization_rules = self._create_default_rules()
    
    def _create_default_rules(self) -> Dict[str, Callable[[MediaMetadata], str]]:
        """Create default organization rules."""
        return {
            "by_type": lambda meta: f"{meta.file_type.value}s",
            "by_date": lambda meta: f"{meta.created_date.year}/{meta.created_date.month:02d}",
            "by_project": lambda meta: f"projects/{meta.project}" if meta.project else "uncategorized",
            "by_speaker": lambda meta: f"speakers/{meta.speaker}" if meta.speaker else "unknown_speaker",
            "by_category": lambda meta: f"categories/{meta.category}" if meta.category else "uncategorized"
        }
    
    def suggest_organization(self, metadata: MediaMetadata, rule_name: str = "by_type") -> str:
        """Suggest organization path for a file."""
        if rule_name not in self.organization_rules:
            rule_name = "by_type"
        
        rule = self.organization_rules[rule_name]
        relative_path = rule(metadata)
        
        return str(self.base_directory / relative_path / metadata.file_name)
    
    def organize_file(self, metadata: MediaMetadata, rule_name: str = "by_type", 
                     copy_file: bool = False) -> bool:
        """Organize a file according to the specified rule."""
        try:
            new_path = self.suggest_organization(metadata, rule_name)
            new_dir = Path(new_path).parent
            
            # Create directory if it doesn't exist
            new_dir.mkdir(parents=True, exist_ok=True)
            
            # Move or copy file
            if copy_file:
                shutil.copy2(metadata.file_path, new_path)
            else:
                shutil.move(metadata.file_path, new_path)
            
            logging.info(f"Organized file: {metadata.file_path} -> {new_path}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to organize file {metadata.file_path}: {e}")
            return False


class MediaDatabase:
    """Database for storing and querying media metadata."""
    
    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.metadata: Dict[str, MediaMetadata] = {}
        self.tags_index: Dict[str, Set[str]] = {}  # tag -> set of file_paths
        self.load_database()
    
    def add_media(self, metadata: MediaMetadata):
        """Add media metadata to the database."""
        self.metadata[metadata.file_path] = metadata
        self._update_tags_index(metadata)
    
    def remove_media(self, file_path: str):
        """Remove media from the database."""
        if file_path in self.metadata:
            metadata = self.metadata[file_path]
            self._remove_from_tags_index(metadata)
            del self.metadata[file_path]
    
    def get_media(self, file_path: str) -> Optional[MediaMetadata]:
        """Get media metadata by file path."""
        return self.metadata.get(file_path)
    
    def update_media(self, metadata: MediaMetadata):
        """Update existing media metadata."""
        old_metadata = self.metadata.get(metadata.file_path)
        if old_metadata:
            self._remove_from_tags_index(old_metadata)
        
        self.metadata[metadata.file_path] = metadata
        self._update_tags_index(metadata)
    
    def search(self, query: str = "", media_type: Optional[MediaType] = None,
               tags: Optional[Set[str]] = None, rating_min: int = 0,
               date_from: Optional[datetime] = None, date_to: Optional[datetime] = None,
               favorites_only: bool = False) -> List[MediaMetadata]:
        """Search media with various filters."""
        results = []
        
        for metadata in self.metadata.values():
            # Text search in name, title, description
            if query:
                searchable_text = f"{metadata.file_name} {metadata.title} {metadata.description}".lower()
                if query.lower() not in searchable_text:
                    continue
            
            # Media type filter
            if media_type and metadata.file_type != media_type:
                continue
            
            # Tags filter (all specified tags must be present)
            if tags and not tags.issubset(metadata.tags):
                continue
            
            # Rating filter
            if metadata.rating < rating_min:
                continue
            
            # Date range filter
            if date_from and metadata.created_date < date_from:
                continue
            if date_to and metadata.created_date > date_to:
                continue
            
            # Favorites filter
            if favorites_only and not metadata.favorite:
                continue
            
            results.append(metadata)
        
        return results
    
    def get_all_tags(self) -> Set[str]:
        """Get all unique tags in the database."""
        return set(self.tags_index.keys())
    
    def get_files_with_tag(self, tag: str) -> Set[str]:
        """Get all file paths that have a specific tag."""
        return self.tags_index.get(tag, set())
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        stats = {
            'total_files': len(self.metadata),
            'by_type': {},
            'total_size': 0,
            'total_duration': 0,
            'tag_count': len(self.tags_index),
            'favorites_count': 0,
            'rated_count': 0
        }
        
        for metadata in self.metadata.values():
            # Count by type
            type_name = metadata.file_type.value
            stats['by_type'][type_name] = stats['by_type'].get(type_name, 0) + 1
            
            # Total size
            stats['total_size'] += metadata.file_size
            
            # Total duration
            if metadata.duration:
                stats['total_duration'] += metadata.duration
            
            # Favorites and ratings
            if metadata.favorite:
                stats['favorites_count'] += 1
            if metadata.rating > 0:
                stats['rated_count'] += 1
        
        return stats
    
    def _update_tags_index(self, metadata: MediaMetadata):
        """Update the tags index for a media item."""
        for tag in metadata.tags:
            if tag not in self.tags_index:
                self.tags_index[tag] = set()
            self.tags_index[tag].add(metadata.file_path)
    
    def _remove_from_tags_index(self, metadata: MediaMetadata):
        """Remove a media item from the tags index."""
        for tag in metadata.tags:
            if tag in self.tags_index:
                self.tags_index[tag].discard(metadata.file_path)
                if not self.tags_index[tag]:
                    del self.tags_index[tag]
    
    def save_database(self):
        """Save the database to disk."""
        try:
            data = {
                'metadata': {path: meta.to_dict() for path, meta in self.metadata.items()},
                'version': '1.0'
            }
            
            with open(self.db_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Media database saved to {self.db_path}")
            
        except Exception as e:
            logging.error(f"Failed to save media database: {e}")
    
    def load_database(self):
        """Load the database from disk."""
        if not self.db_path.exists():
            return
        
        try:
            with open(self.db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Load metadata
            for path, meta_dict in data.get('metadata', {}).items():
                try:
                    metadata = MediaMetadata.from_dict(meta_dict)
                    self.metadata[path] = metadata
                    self._update_tags_index(metadata)
                except Exception as e:
                    logging.warning(f"Failed to load metadata for {path}: {e}")
            
            logging.info(f"Loaded {len(self.metadata)} media items from database")
            
        except Exception as e:
            logging.error(f"Failed to load media database: {e}")


def detect_media_type(file_path: str) -> MediaType:
    """Detect media type from file extension and MIME type."""
    mime_type, _ = mimetypes.guess_type(file_path)
    
    if mime_type:
        if mime_type.startswith('video/'):
            return MediaType.VIDEO
        elif mime_type.startswith('audio/'):
            return MediaType.AUDIO
        elif mime_type.startswith('image/'):
            return MediaType.IMAGE
        elif mime_type.startswith('text/'):
            return MediaType.TEXT
    
    # Fallback to extension-based detection
    ext = Path(file_path).suffix.lower()
    
    video_exts = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    audio_exts = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma'}
    image_exts = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    text_exts = {'.txt', '.md', '.rtf', '.doc', '.docx'}
    project_exts = {'.alvp', '.json'}  # Auto Latent Video Processor project files
    
    if ext in video_exts:
        return MediaType.VIDEO
    elif ext in audio_exts:
        return MediaType.AUDIO
    elif ext in image_exts:
        return MediaType.IMAGE
    elif ext in text_exts:
        return MediaType.TEXT
    elif ext in project_exts:
        return MediaType.PROJECT
    else:
        return MediaType.OTHER


def extract_basic_metadata(file_path: str) -> MediaMetadata:
    """Extract basic metadata from a file."""
    path = Path(file_path)
    stat = path.stat()
    
    metadata = MediaMetadata(
        file_path=str(path.absolute()),
        file_name=path.name,
        file_size=stat.st_size,
        file_type=detect_media_type(file_path),
        mime_type=mimetypes.guess_type(file_path)[0] or "application/octet-stream",
        created_date=datetime.fromtimestamp(stat.st_ctime),
        modified_date=datetime.fromtimestamp(stat.st_mtime)
    )
    
    return metadata


class MediaLibraryWidget(QWidget):
    """Main media library widget with search, filters, and organization."""

    # Signals
    media_selected = Signal(str)  # file_path
    media_double_clicked = Signal(str)  # file_path

    def __init__(self, database_path: str = "media_library.json"):
        super().__init__()

        # Initialize components
        self.database = MediaDatabase(database_path)
        self.duplicate_detector = DuplicateDetector()
        self.organizer = MediaOrganizer("organized_media")

        # Current view state
        self.current_results: List[MediaMetadata] = []
        self.current_sort_order = SortOrder.NAME_ASC

        # Setup UI
        self._setup_ui()
        self._connect_signals()

        # Load initial data
        self._refresh_view()

    def _setup_ui(self):
        """Setup the main UI layout."""
        layout = QVBoxLayout(self)

        # Search and filter bar
        search_layout = self._create_search_bar()
        layout.addLayout(search_layout)

        # Main content area
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel: Filters and tags
        left_panel = self._create_left_panel()
        content_splitter.addWidget(left_panel)

        # Center panel: Media grid/list
        center_panel = self._create_center_panel()
        content_splitter.addWidget(center_panel)

        # Right panel: Details and metadata
        right_panel = self._create_right_panel()
        content_splitter.addWidget(right_panel)

        content_splitter.setSizes([200, 600, 250])
        layout.addWidget(content_splitter)

        # Status bar
        status_layout = self._create_status_bar()
        layout.addLayout(status_layout)

    def _create_search_bar(self) -> QHBoxLayout:
        """Create the search and filter bar."""
        layout = QHBoxLayout()

        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search media files...")
        self.search_input.textChanged.connect(self._on_search_changed)
        layout.addWidget(self.search_input)

        # Media type filter
        self.type_filter = QComboBox()
        self.type_filter.addItem("All Types", None)
        for media_type in MediaType:
            self.type_filter.addItem(media_type.value.title(), media_type)
        self.type_filter.currentDataChanged.connect(self._on_filter_changed)
        layout.addWidget(self.type_filter)

        # Sort order
        self.sort_combo = QComboBox()
        for sort_order in SortOrder:
            display_name = sort_order.value.replace('_', ' ').title()
            self.sort_combo.addItem(display_name, sort_order)
        self.sort_combo.currentDataChanged.connect(self._on_sort_changed)
        layout.addWidget(self.sort_combo)

        # View mode toggle
        self.view_mode_btn = QPushButton("Grid View")
        self.view_mode_btn.setCheckable(True)
        self.view_mode_btn.clicked.connect(self._toggle_view_mode)
        layout.addWidget(self.view_mode_btn)

        return layout

    def _create_left_panel(self) -> QWidget:
        """Create the left panel with filters and tags."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Quick filters
        filters_group = QGroupBox("Quick Filters")
        filters_layout = QVBoxLayout(filters_group)

        self.favorites_filter = QCheckBox("Favorites Only")
        self.favorites_filter.stateChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.favorites_filter)

        self.duplicates_filter = QCheckBox("Show Duplicates")
        self.duplicates_filter.stateChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.duplicates_filter)

        # Rating filter
        rating_layout = QHBoxLayout()
        rating_layout.addWidget(QLabel("Min Rating:"))
        self.rating_filter = QSpinBox()
        self.rating_filter.setRange(0, 5)
        self.rating_filter.valueChanged.connect(self._on_filter_changed)
        rating_layout.addWidget(self.rating_filter)
        filters_layout.addLayout(rating_layout)

        layout.addWidget(filters_group)

        # Tags list
        tags_group = QGroupBox("Tags")
        tags_layout = QVBoxLayout(tags_group)

        self.tags_list = QListWidget()
        self.tags_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        self.tags_list.itemSelectionChanged.connect(self._on_filter_changed)
        tags_layout.addWidget(self.tags_list)

        layout.addWidget(tags_group)

        # Organization tools
        org_group = QGroupBox("Organization")
        org_layout = QVBoxLayout(org_group)

        scan_btn = QPushButton("Scan for Media")
        scan_btn.clicked.connect(self._scan_for_media)
        org_layout.addWidget(scan_btn)

        organize_btn = QPushButton("Auto-Organize")
        organize_btn.clicked.connect(self._auto_organize)
        org_layout.addWidget(organize_btn)

        duplicates_btn = QPushButton("Find Duplicates")
        duplicates_btn.clicked.connect(self._find_duplicates)
        org_layout.addWidget(duplicates_btn)

        layout.addWidget(org_group)

        layout.addStretch()
        return panel

    def _create_center_panel(self) -> QWidget:
        """Create the center panel with media display."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Media display (will switch between grid and list)
        self.media_display = QListWidget()
        self.media_display.setViewMode(QListWidget.ViewMode.IconMode)
        self.media_display.setIconSize(QSize(120, 90))
        self.media_display.setResizeMode(QListWidget.ResizeMode.Adjust)
        self.media_display.setSpacing(10)
        self.media_display.itemClicked.connect(self._on_media_selected)
        self.media_display.itemDoubleClicked.connect(self._on_media_double_clicked)
        layout.addWidget(self.media_display)

        return panel

    def _create_right_panel(self) -> QWidget:
        """Create the right panel with media details."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Media details
        details_group = QGroupBox("Media Details")
        details_layout = QVBoxLayout(details_group)

        # Thumbnail
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(200, 150)
        self.thumbnail_label.setStyleSheet("border: 1px solid #ccc; background: #f0f0f0;")
        self.thumbnail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.thumbnail_label.setText("No media selected")
        details_layout.addWidget(self.thumbnail_label)

        # Basic info
        self.info_labels = {}
        info_fields = ["Name", "Type", "Size", "Duration", "Created", "Rating"]

        for field in info_fields:
            row_layout = QHBoxLayout()
            label = QLabel(f"{field}:")
            label.setMinimumWidth(60)
            value_label = QLabel("-")
            value_label.setWordWrap(True)
            row_layout.addWidget(label)
            row_layout.addWidget(value_label)
            details_layout.addLayout(row_layout)
            self.info_labels[field.lower()] = value_label

        layout.addWidget(details_group)

        # Metadata editing
        edit_group = QGroupBox("Edit Metadata")
        edit_layout = QVBoxLayout(edit_group)

        # Title
        edit_layout.addWidget(QLabel("Title:"))
        self.title_edit = QLineEdit()
        edit_layout.addWidget(self.title_edit)

        # Description
        edit_layout.addWidget(QLabel("Description:"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        edit_layout.addWidget(self.description_edit)

        # Tags
        edit_layout.addWidget(QLabel("Tags (comma-separated):"))
        self.tags_edit = QLineEdit()
        edit_layout.addWidget(self.tags_edit)

        # Rating
        rating_layout = QHBoxLayout()
        rating_layout.addWidget(QLabel("Rating:"))
        self.rating_edit = QSpinBox()
        self.rating_edit.setRange(0, 5)
        rating_layout.addWidget(self.rating_edit)
        self.favorite_check = QCheckBox("Favorite")
        rating_layout.addWidget(self.favorite_check)
        edit_layout.addLayout(rating_layout)

        # Save button
        save_btn = QPushButton("Save Changes")
        save_btn.clicked.connect(self._save_metadata_changes)
        edit_layout.addWidget(save_btn)

        layout.addWidget(edit_group)

        layout.addStretch()
        return panel

    def _create_status_bar(self) -> QHBoxLayout:
        """Create the status bar."""
        layout = QHBoxLayout()

        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)

        layout.addStretch()

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        self.results_label = QLabel("0 items")
        layout.addWidget(self.results_label)

        return layout

    def _connect_signals(self):
        """Connect internal signals."""
        pass

    def _refresh_view(self):
        """Refresh the media display with current filters."""
        # Get search parameters
        query = self.search_input.text()
        media_type = self.type_filter.currentData()
        favorites_only = self.favorites_filter.isChecked()
        rating_min = self.rating_filter.value()

        # Get selected tags
        selected_tags = set()
        for item in self.tags_list.selectedItems():
            selected_tags.add(item.text())

        # Search database
        self.current_results = self.database.search(
            query=query,
            media_type=media_type,
            tags=selected_tags if selected_tags else None,
            rating_min=rating_min,
            favorites_only=favorites_only
        )

        # Sort results
        self._sort_results()

        # Update display
        self._update_media_display()
        self._update_tags_list()
        self._update_status()

    def _sort_results(self):
        """Sort the current results based on selected sort order."""
        if not self.current_results:
            return

        sort_key_map = {
            SortOrder.NAME_ASC: lambda x: x.file_name.lower(),
            SortOrder.NAME_DESC: lambda x: x.file_name.lower(),
            SortOrder.DATE_ASC: lambda x: x.created_date,
            SortOrder.DATE_DESC: lambda x: x.created_date,
            SortOrder.SIZE_ASC: lambda x: x.file_size,
            SortOrder.SIZE_DESC: lambda x: x.file_size,
            SortOrder.TYPE_ASC: lambda x: x.file_type.value,
            SortOrder.TYPE_DESC: lambda x: x.file_type.value,
            SortOrder.RATING_ASC: lambda x: x.rating,
            SortOrder.RATING_DESC: lambda x: x.rating
        }

        reverse_map = {
            SortOrder.NAME_DESC: True,
            SortOrder.DATE_DESC: True,
            SortOrder.SIZE_DESC: True,
            SortOrder.TYPE_DESC: True,
            SortOrder.RATING_DESC: True
        }

        sort_key = sort_key_map.get(self.current_sort_order, lambda x: x.file_name.lower())
        reverse = reverse_map.get(self.current_sort_order, False)

        self.current_results.sort(key=sort_key, reverse=reverse)

    def _update_media_display(self):
        """Update the media display widget."""
        self.media_display.clear()

        for metadata in self.current_results:
            item = QListWidgetItem()
            item.setText(metadata.file_name)
            item.setData(Qt.ItemDataRole.UserRole, metadata.file_path)

            # Set icon based on media type
            icon_map = {
                MediaType.VIDEO: "🎬",
                MediaType.AUDIO: "🎵",
                MediaType.IMAGE: "🖼️",
                MediaType.TEXT: "📄",
                MediaType.PROJECT: "📁",
                MediaType.OTHER: "📎"
            }

            icon_text = icon_map.get(metadata.file_type, "📎")
            if metadata.favorite:
                icon_text += "⭐"

            # Create a simple text-based icon (would use actual thumbnails in production)
            item.setToolTip(f"{metadata.file_name}\nType: {metadata.file_type.value}\nSize: {self._format_size(metadata.file_size)}")

            self.media_display.addItem(item)

    def _update_tags_list(self):
        """Update the tags list with all available tags."""
        current_selection = {item.text() for item in self.tags_list.selectedItems()}

        self.tags_list.clear()
        all_tags = sorted(self.database.get_all_tags())

        for tag in all_tags:
            item = QListWidgetItem(tag)
            self.tags_list.addItem(item)

            # Restore selection
            if tag in current_selection:
                item.setSelected(True)

    def _update_status(self):
        """Update the status bar."""
        count = len(self.current_results)
        total = len(self.database.metadata)

        if count == total:
            self.results_label.setText(f"{count} items")
        else:
            self.results_label.setText(f"{count} of {total} items")

        self.status_label.setText("Ready")

    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f} TB"

    # Event handlers
    def _on_search_changed(self):
        """Handle search input changes."""
        self._refresh_view()

    def _on_filter_changed(self):
        """Handle filter changes."""
        self._refresh_view()

    def _on_sort_changed(self):
        """Handle sort order changes."""
        self.current_sort_order = self.sort_combo.currentData()
        self._refresh_view()

    def _toggle_view_mode(self):
        """Toggle between grid and list view."""
        if self.view_mode_btn.isChecked():
            self.media_display.setViewMode(QListWidget.ViewMode.ListMode)
            self.view_mode_btn.setText("List View")
        else:
            self.media_display.setViewMode(QListWidget.ViewMode.IconMode)
            self.view_mode_btn.setText("Grid View")

    def _on_media_selected(self, item: QListWidgetItem):
        """Handle media selection."""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        metadata = self.database.get_media(file_path)

        if metadata:
            self._display_media_details(metadata)
            self.media_selected.emit(file_path)

    def _on_media_double_clicked(self, item: QListWidgetItem):
        """Handle media double-click."""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        self.media_double_clicked.emit(file_path)

    def _display_media_details(self, metadata: MediaMetadata):
        """Display media details in the right panel."""
        # Update info labels
        self.info_labels["name"].setText(metadata.file_name)
        self.info_labels["type"].setText(metadata.file_type.value.title())
        self.info_labels["size"].setText(self._format_size(metadata.file_size))

        if metadata.duration:
            duration_str = f"{metadata.duration:.1f}s"
        else:
            duration_str = "-"
        self.info_labels["duration"].setText(duration_str)

        self.info_labels["created"].setText(metadata.created_date.strftime("%Y-%m-%d %H:%M"))
        self.info_labels["rating"].setText("⭐" * metadata.rating if metadata.rating > 0 else "-")

        # Update edit fields
        self.title_edit.setText(metadata.title)
        self.description_edit.setPlainText(metadata.description)
        self.tags_edit.setText(", ".join(sorted(metadata.tags)))
        self.rating_edit.setValue(metadata.rating)
        self.favorite_check.setChecked(metadata.favorite)

        # Update thumbnail (placeholder)
        self.thumbnail_label.setText(f"Thumbnail\n{metadata.file_type.value.upper()}")

    def _save_metadata_changes(self):
        """Save metadata changes for the selected media."""
        current_item = self.media_display.currentItem()
        if not current_item:
            return

        file_path = current_item.data(Qt.ItemDataRole.UserRole)
        metadata = self.database.get_media(file_path)

        if metadata:
            # Update metadata
            metadata.title = self.title_edit.text()
            metadata.description = self.description_edit.toPlainText()
            metadata.tags = {tag.strip() for tag in self.tags_edit.text().split(",") if tag.strip()}
            metadata.rating = self.rating_edit.value()
            metadata.favorite = self.favorite_check.isChecked()

            # Save to database
            self.database.update_media(metadata)
            self.database.save_database()

            # Refresh view
            self._refresh_view()

            self.status_label.setText("Metadata saved")

    def _scan_for_media(self):
        """Scan for media files in selected directories."""
        directories = QFileDialog.getExistingDirectory(self, "Select Directory to Scan")
        if directories:
            self._start_media_scan([directories])

    def _start_media_scan(self, directories: List[str]):
        """Start media scanning process."""
        extensions = {'.mp4', '.avi', '.mov', '.mp3', '.wav', '.jpg', '.png', '.txt'}

        self.scanner = MediaScanner(directories, extensions)
        self.scanner.file_found.connect(self._on_file_found)
        self.scanner.scan_progress.connect(self._on_scan_progress)
        self.scanner.scan_complete.connect(self._on_scan_complete)

        self.progress_bar.setVisible(True)
        self.status_label.setText("Scanning for media files...")
        self.scanner.start()

    def _on_file_found(self, file_path: str):
        """Handle found media file."""
        if file_path not in self.database.metadata:
            metadata = extract_basic_metadata(file_path)
            self.database.add_media(metadata)

    def _on_scan_progress(self, current: int, total: int):
        """Handle scan progress updates."""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.status_label.setText(f"Scanning... {current}/{total}")

    def _on_scan_complete(self, total_files: int):
        """Handle scan completion."""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"Scan complete. Found {total_files} files.")

        # Save database and refresh view
        self.database.save_database()
        self._refresh_view()

    def _auto_organize(self):
        """Auto-organize media files."""
        # This would show a dialog to select organization rules
        # For now, just show a message
        QMessageBox.information(self, "Auto-Organize", "Auto-organization feature coming soon!")

    def _find_duplicates(self):
        """Find and display duplicate files."""
        self.status_label.setText("Finding duplicates...")

        # Add all files to duplicate detector
        for metadata in self.database.metadata.values():
            if os.path.exists(metadata.file_path):
                self.duplicate_detector.add_file(metadata.file_path)

        duplicates = self.duplicate_detector.get_duplicates()

        if duplicates:
            message = f"Found {len(duplicates)} groups of duplicate files:\n\n"
            for i, (hash_val, files) in enumerate(duplicates.items()):
                if i >= 5:  # Limit display
                    message += f"... and {len(duplicates) - 5} more groups"
                    break
                message += f"Group {i+1}: {len(files)} files\n"
                for file_path in files[:3]:  # Show first 3 files
                    message += f"  - {Path(file_path).name}\n"
                if len(files) > 3:
                    message += f"  ... and {len(files) - 3} more\n"
                message += "\n"

            QMessageBox.information(self, "Duplicates Found", message)
        else:
            QMessageBox.information(self, "No Duplicates", "No duplicate files found.")

        self.status_label.setText("Ready")
