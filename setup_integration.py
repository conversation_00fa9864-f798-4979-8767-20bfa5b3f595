"""
Integration Setup Script for Auto Latent Video Processor.

This script handles the complete setup and integration of all components,
including dependency resolution, environment validation, and system checks.
"""

import os
import sys
import subprocess
import importlib
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
import platform

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class IntegrationManager:
    """Manages the integration and setup of all system components."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.conda_env = "audlat"
        self.python_version = "3.11"
        self.required_components = [
            "ui_foundation",
            "project_management", 
            "timeline_editor",
            "media_library",
            "visual_workflow_builder",
            "advanced_audio_system",
            "undo_redo_system",
            "enhanced_progress_feedback"
        ]
        
        # Dependency groups for staged installation
        self.dependency_groups = {
            "core": [
                "PySide6>=6.6.0",
                "numpy>=1.24.0,<2.0.0",  # Ensure NumPy 1.x for compatibility
                "pillow>=10.0.0",
                "requests>=2.31.0",
                "python-dotenv>=1.0.0"
            ],
            "audio": [
                "librosa>=0.10.0",
                "soundfile>=0.12.0",
                "scipy>=1.11.0",
                "pyaudio>=0.2.11"
            ],
            "video": [
                "opencv-python>=4.8.0",
                "imageio>=2.31.0",
                "imageio-ffmpeg>=0.4.8"
            ],
            "ai_ml": [
                "torch>=2.0.0",
                "torchvision>=0.15.0",
                "torchaudio>=2.0.0",
                "transformers>=4.30.0",
                "diffusers>=0.20.0"
            ],
            "dev": [
                "pytest>=7.4.0",
                "pytest-qt>=4.2.0",
                "pytest-cov>=4.1.0",
                "black>=23.0.0",
                "flake8>=6.0.0",
                "mypy>=1.5.0"
            ]
        }
        
        self.system_checks = {
            "python_version": self._check_python_version,
            "conda_environment": self._check_conda_environment,
            "gpu_availability": self._check_gpu_availability,
            "disk_space": self._check_disk_space,
            "memory": self._check_memory
        }
    
    def run_full_integration(self) -> bool:
        """Run the complete integration process."""
        logger.info("🚀 Starting Auto Latent Video Processor Integration")
        
        try:
            # Step 1: System checks
            if not self._run_system_checks():
                logger.error("❌ System checks failed")
                return False
            
            # Step 2: Environment setup
            if not self._setup_environment():
                logger.error("❌ Environment setup failed")
                return False
            
            # Step 3: Install dependencies
            if not self._install_dependencies():
                logger.error("❌ Dependency installation failed")
                return False
            
            # Step 4: Component integration
            if not self._integrate_components():
                logger.error("❌ Component integration failed")
                return False
            
            # Step 5: Run tests
            if not self._run_integration_tests():
                logger.error("❌ Integration tests failed")
                return False
            
            # Step 6: Performance validation
            if not self._validate_performance():
                logger.error("❌ Performance validation failed")
                return False
            
            logger.info("✅ Integration completed successfully!")
            self._generate_integration_report()
            return True
            
        except Exception as e:
            logger.error(f"❌ Integration failed with error: {e}")
            return False
    
    def _run_system_checks(self) -> bool:
        """Run all system compatibility checks."""
        logger.info("🔍 Running system checks...")
        
        results = {}
        for check_name, check_func in self.system_checks.items():
            try:
                results[check_name] = check_func()
                status = "✅" if results[check_name] else "❌"
                logger.info(f"{status} {check_name}: {'PASS' if results[check_name] else 'FAIL'}")
            except Exception as e:
                results[check_name] = False
                logger.error(f"❌ {check_name}: ERROR - {e}")
        
        # All critical checks must pass
        critical_checks = ["python_version", "conda_environment", "disk_space", "memory"]
        return all(results.get(check, False) for check in critical_checks)
    
    def _check_python_version(self) -> bool:
        """Check Python version compatibility."""
        version = sys.version_info
        required = tuple(map(int, self.python_version.split('.')))
        return version >= required
    
    def _check_conda_environment(self) -> bool:
        """Check if conda environment exists and is active."""
        try:
            result = subprocess.run(["conda", "info", "--envs"], 
                                  capture_output=True, text=True, check=True)
            return self.conda_env in result.stdout
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.warning("Conda not found, will use pip for package management")
            return True  # Not critical if conda isn't available
    
    def _check_gpu_availability(self) -> bool:
        """Check for GPU availability for AI processing."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False  # Will be installed later
    
    def _check_disk_space(self) -> bool:
        """Check available disk space (minimum 5GB)."""
        try:
            import shutil
            free_space = shutil.disk_usage(self.project_root).free
            return free_space > 5 * 1024 * 1024 * 1024  # 5GB
        except Exception:
            return True  # Assume sufficient space if check fails
    
    def _check_memory(self) -> bool:
        """Check available memory (minimum 4GB)."""
        try:
            import psutil
            return psutil.virtual_memory().total > 4 * 1024 * 1024 * 1024  # 4GB
        except ImportError:
            return True  # Assume sufficient memory if psutil not available
    
    def _setup_environment(self) -> bool:
        """Setup the development environment."""
        logger.info("🔧 Setting up environment...")
        
        try:
            # Create conda environment if it doesn't exist
            if self._check_conda_environment():
                result = subprocess.run([
                    "conda", "create", "-n", self.conda_env, 
                    f"python={self.python_version}", "-y"
                ], capture_output=True, text=True)
                
                if result.returncode != 0 and "already exists" not in result.stderr:
                    logger.error(f"Failed to create conda environment: {result.stderr}")
                    return False
            
            # Activate environment and update pip
            self._run_in_env(["python", "-m", "pip", "install", "--upgrade", "pip"])
            
            return True
            
        except Exception as e:
            logger.error(f"Environment setup failed: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """Install all required dependencies in stages."""
        logger.info("📦 Installing dependencies...")
        
        # Install in order of dependency
        install_order = ["core", "audio", "video", "ai_ml", "dev"]
        
        for group in install_order:
            logger.info(f"Installing {group} dependencies...")
            
            packages = self.dependency_groups[group]
            for package in packages:
                try:
                    result = self._run_in_env(["pip", "install", package])
                    if result.returncode != 0:
                        logger.warning(f"Failed to install {package}, continuing...")
                except Exception as e:
                    logger.warning(f"Error installing {package}: {e}")
        
        return True
    
    def _integrate_components(self) -> bool:
        """Integrate all system components."""
        logger.info("🔗 Integrating components...")
        
        # Check that all components can be imported
        for component in self.required_components:
            try:
                importlib.import_module(component)
                logger.info(f"✅ {component} imported successfully")
            except ImportError as e:
                logger.error(f"❌ Failed to import {component}: {e}")
                return False
        
        # Test basic component interactions
        return self._test_component_interactions()
    
    def _test_component_interactions(self) -> bool:
        """Test basic interactions between components."""
        logger.info("🧪 Testing component interactions...")
        
        try:
            # Test UI foundation
            from ui_foundation import ThemeManager, LayoutManager
            theme_manager = ThemeManager()
            layout_manager = LayoutManager()
            
            # Test project management
            from project_management import ProjectManager
            project_manager = ProjectManager()
            
            # Test undo/redo system
            from undo_redo_system import UndoRedoManager
            undo_manager = UndoRedoManager()
            
            # Test feedback system
            from enhanced_progress_feedback import FeedbackSystem
            feedback_system = FeedbackSystem()
            
            logger.info("✅ Component interactions working")
            return True
            
        except Exception as e:
            logger.error(f"❌ Component interaction test failed: {e}")
            return False
    
    def _run_integration_tests(self) -> bool:
        """Run the integration test suite."""
        logger.info("🧪 Running integration tests...")
        
        try:
            # Run pytest with coverage
            result = self._run_in_env([
                "python", "-m", "pytest", "tests/", 
                "-v", "--tb=short", "--cov=.", "--cov-report=term-missing"
            ])
            
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"Integration tests failed: {e}")
            return False
    
    def _validate_performance(self) -> bool:
        """Validate system performance."""
        logger.info("⚡ Validating performance...")
        
        try:
            # Test memory usage
            import psutil
            import gc
            
            # Force garbage collection
            gc.collect()
            
            # Get baseline memory
            process = psutil.Process()
            baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Test component loading
            from ui_foundation import ThemeManager
            from project_management import ProjectManager
            from timeline_editor import TimelineEditor
            
            # Check memory after loading
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = current_memory - baseline_memory
            
            logger.info(f"Memory usage: {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
            
            # Memory increase should be reasonable (< 500MB for basic loading)
            return memory_increase < 500
            
        except Exception as e:
            logger.warning(f"Performance validation failed: {e}")
            return True  # Non-critical
    
    def _run_in_env(self, command: List[str]) -> subprocess.CompletedProcess:
        """Run a command in the conda environment."""
        if self._check_conda_environment():
            # Use conda run to execute in environment
            full_command = ["conda", "run", "-n", self.conda_env] + command
        else:
            # Use direct command if conda not available
            full_command = command
        
        return subprocess.run(full_command, capture_output=True, text=True)
    
    def _generate_integration_report(self):
        """Generate a comprehensive integration report."""
        logger.info("📊 Generating integration report...")
        
        report = {
            "timestamp": str(datetime.now()),
            "system_info": {
                "platform": platform.platform(),
                "python_version": sys.version,
                "architecture": platform.architecture()[0]
            },
            "components_status": {},
            "dependencies_status": {},
            "performance_metrics": {}
        }
        
        # Check component status
        for component in self.required_components:
            try:
                importlib.import_module(component)
                report["components_status"][component] = "OK"
            except ImportError:
                report["components_status"][component] = "FAILED"
        
        # Save report
        report_path = self.project_root / "integration_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"📋 Integration report saved to: {report_path}")


def main():
    """Main integration script entry point."""
    manager = IntegrationManager()
    
    print("🎬 Auto Latent Video Processor - Integration & Testing")
    print("=" * 60)
    
    success = manager.run_full_integration()
    
    if success:
        print("\n🎉 Integration completed successfully!")
        print("\nNext steps:")
        print("1. Review integration_report.json for details")
        print("2. Run 'python main_application.py' to test the full application")
        print("3. Check the AI Enhancement Roadmap for next development phase")
    else:
        print("\n❌ Integration failed. Check the logs above for details.")
        print("Please resolve the issues and run the integration again.")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
