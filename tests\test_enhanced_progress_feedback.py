"""
Tests for the Enhanced Progress & Feedback System.
"""

import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import sys

# Mock PySide6 for testing
sys.modules['PySide6'] = MagicMock()
sys.modules['PySide6.QtWidgets'] = MagicMock()
sys.modules['PySide6.QtCore'] = MagicMock()
sys.modules['PySide6.QtGui'] = MagicMock()

from enhanced_progress_feedback import (
    ProgressInfo, NotificationInfo, ProgressTracker, NotificationManager,
    StatusManager, FeedbackSystem, ProgressType, NotificationType,
    FeedbackLevel
)


class TestProgressInfo:
    """Test progress information data structure."""
    
    def test_progress_info_creation(self):
        """Test creating progress info."""
        progress = ProgressInfo(
            id="prog_1",
            title="Test Progress",
            description="Testing progress tracking",
            current=25,
            total=100
        )
        
        assert progress.id == "prog_1"
        assert progress.title == "Test Progress"
        assert progress.description == "Testing progress tracking"
        assert progress.current == 25
        assert progress.total == 100
        assert progress.percentage == 25.0
        assert progress.can_cancel is False
    
    def test_progress_percentage_calculation(self):
        """Test progress percentage calculation."""
        progress = ProgressInfo("prog_1", "Test", current=0, total=100)
        assert progress.percentage == 0.0
        
        progress.current = 50
        assert progress.percentage == 50.0
        
        progress.current = 100
        assert progress.percentage == 100.0
        
        # Test edge cases
        progress.current = 150
        assert progress.percentage == 100.0  # Capped at 100%
        
        progress.total = 0
        assert progress.percentage == 0.0  # Avoid division by zero
    
    def test_progress_time_calculations(self):
        """Test time-related calculations."""
        start_time = datetime.now() - timedelta(seconds=30)
        progress = ProgressInfo("prog_1", "Test", start_time=start_time)
        
        # Elapsed time should be approximately 30 seconds
        elapsed = progress.elapsed_time
        assert 29 <= elapsed.total_seconds() <= 31
        
        # Test estimated remaining time
        completion_time = datetime.now() + timedelta(seconds=60)
        progress.estimated_completion = completion_time
        
        remaining = progress.estimated_remaining
        assert remaining is not None
        assert 59 <= remaining.total_seconds() <= 61


class TestNotificationInfo:
    """Test notification information data structure."""
    
    def test_notification_info_creation(self):
        """Test creating notification info."""
        notification = NotificationInfo(
            id="notif_1",
            title="Test Notification",
            message="This is a test notification",
            notification_type=NotificationType.INFO,
            duration=5.0
        )
        
        assert notification.id == "notif_1"
        assert notification.title == "Test Notification"
        assert notification.message == "This is a test notification"
        assert notification.notification_type == NotificationType.INFO
        assert notification.duration == 5.0
        assert notification.persistent is False
        assert len(notification.actions) == 0
    
    def test_notification_add_action(self):
        """Test adding actions to notifications."""
        notification = NotificationInfo("notif_1", "Test", "Message", NotificationType.INFO)
        
        callback = Mock()
        notification.add_action("Retry", callback, "🔄")
        
        assert len(notification.actions) == 1
        action = notification.actions[0]
        assert action['label'] == "Retry"
        assert action['callback'] is callback
        assert action['icon'] == "🔄"


class TestProgressTracker:
    """Test progress tracking functionality."""
    
    def setup_method(self):
        """Setup test progress tracker."""
        self.tracker = ProgressTracker()
    
    def test_start_progress(self):
        """Test starting a progress operation."""
        progress_id = self.tracker.start_progress(
            "Test Task",
            "Testing progress tracking",
            total=100,
            can_cancel=True
        )
        
        assert progress_id is not None
        assert len(progress_id) > 0
        assert progress_id in self.tracker.active_progress
        
        progress_info = self.tracker.get_progress_info(progress_id)
        assert progress_info is not None
        assert progress_info.title == "Test Task"
        assert progress_info.description == "Testing progress tracking"
        assert progress_info.total == 100
        assert progress_info.can_cancel is True
    
    def test_update_progress(self):
        """Test updating progress."""
        progress_id = self.tracker.start_progress("Test Task", total=100)
        
        # Update progress
        result = self.tracker.update_progress(progress_id, 50)
        assert result is True
        
        progress_info = self.tracker.get_progress_info(progress_id)
        assert progress_info.current == 50
        assert progress_info.percentage == 50.0
        
        # Update with new description
        result = self.tracker.update_progress(progress_id, 75, "Almost done")
        assert result is True
        
        progress_info = self.tracker.get_progress_info(progress_id)
        assert progress_info.current == 75
        assert progress_info.description == "Almost done"
    
    def test_complete_progress(self):
        """Test completing progress."""
        progress_id = self.tracker.start_progress("Test Task", total=100)
        
        # Complete successfully
        result = self.tracker.complete_progress(progress_id, success=True)
        assert result is True
        
        # Should no longer be in active progress
        assert progress_id not in self.tracker.active_progress
        
        # Should be in completed history
        completed = self.tracker.get_completed_progress()
        assert len(completed) == 1
        assert completed[0].id == progress_id
        assert completed[0].current == 100  # Should be set to total
    
    def test_cancel_progress(self):
        """Test cancelling progress."""
        # Test cancellable progress
        progress_id = self.tracker.start_progress("Test Task", can_cancel=True)
        
        result = self.tracker.cancel_progress(progress_id)
        assert result is True
        assert progress_id not in self.tracker.active_progress
        
        # Test non-cancellable progress
        progress_id2 = self.tracker.start_progress("Non-cancellable Task", can_cancel=False)
        
        result = self.tracker.cancel_progress(progress_id2)
        assert result is False
        assert progress_id2 in self.tracker.active_progress
    
    def test_get_all_active_progress(self):
        """Test getting all active progress operations."""
        # Start multiple progress operations
        id1 = self.tracker.start_progress("Task 1")
        id2 = self.tracker.start_progress("Task 2")
        id3 = self.tracker.start_progress("Task 3")
        
        active = self.tracker.get_all_active_progress()
        assert len(active) == 3
        
        active_ids = {p.id for p in active}
        assert id1 in active_ids
        assert id2 in active_ids
        assert id3 in active_ids
        
        # Complete one task
        self.tracker.complete_progress(id2)
        
        active = self.tracker.get_all_active_progress()
        assert len(active) == 2
        assert id2 not in {p.id for p in active}


class TestNotificationManager:
    """Test notification management functionality."""
    
    def setup_method(self):
        """Setup test notification manager."""
        self.manager = NotificationManager()
    
    def test_show_notification(self):
        """Test showing notifications."""
        notification_id = self.manager.show_notification(
            "Test Title",
            "Test message",
            NotificationType.INFO,
            duration=5.0
        )
        
        assert notification_id is not None
        assert notification_id in self.manager.notifications
        
        notification = self.manager.get_notification(notification_id)
        assert notification is not None
        assert notification.title == "Test Title"
        assert notification.message == "Test message"
        assert notification.notification_type == NotificationType.INFO
        assert notification.duration == 5.0
    
    def test_dismiss_notification(self):
        """Test dismissing notifications."""
        notification_id = self.manager.show_notification("Test", "Message", NotificationType.INFO)
        
        # Notification should exist
        assert notification_id in self.manager.notifications
        
        # Dismiss it
        result = self.manager.dismiss_notification(notification_id)
        assert result is True
        assert notification_id not in self.manager.notifications
        
        # Try to dismiss non-existent notification
        result = self.manager.dismiss_notification("nonexistent")
        assert result is False
    
    def test_notification_actions(self):
        """Test notification actions."""
        callback = Mock()
        
        notification_id = self.manager.show_notification(
            "Test", "Message", NotificationType.INFO,
            actions=[{'label': 'Test Action', 'callback': callback}]
        )
        
        # Trigger the action
        result = self.manager.trigger_notification_action(notification_id, "Test Action")
        assert result is True
        callback.assert_called_once()
        
        # Try to trigger non-existent action
        result = self.manager.trigger_notification_action(notification_id, "Nonexistent")
        assert result is False
    
    def test_convenience_methods(self):
        """Test convenience methods for common notification types."""
        # Test info notification
        info_id = self.manager.show_info("Info Title", "Info message")
        info_notif = self.manager.get_notification(info_id)
        assert info_notif.notification_type == NotificationType.INFO
        
        # Test success notification
        success_id = self.manager.show_success("Success Title", "Success message")
        success_notif = self.manager.get_notification(success_id)
        assert success_notif.notification_type == NotificationType.SUCCESS
        
        # Test warning notification
        warning_id = self.manager.show_warning("Warning Title", "Warning message")
        warning_notif = self.manager.get_notification(warning_id)
        assert warning_notif.notification_type == NotificationType.WARNING
        
        # Test error notification
        error_id = self.manager.show_error("Error Title", "Error message")
        error_notif = self.manager.get_notification(error_id)
        assert error_notif.notification_type == NotificationType.ERROR
        assert error_notif.persistent is True
    
    def test_clear_all_notifications(self):
        """Test clearing all notifications."""
        # Create multiple notifications
        self.manager.show_info("Info 1", "Message 1")
        self.manager.show_warning("Warning 1", "Message 2")
        self.manager.show_error("Error 1", "Message 3")
        
        assert len(self.manager.notifications) == 3
        
        # Clear all
        self.manager.clear_all_notifications()
        assert len(self.manager.notifications) == 0


class TestStatusManager:
    """Test status management functionality."""
    
    def setup_method(self):
        """Setup test status manager."""
        self.manager = StatusManager()
    
    def test_set_get_status(self):
        """Test setting and getting component status."""
        # Set status
        self.manager.set_status("audio_processor", "processing", "Processing audio file")
        
        # Get status
        status = self.manager.get_status("audio_processor")
        assert status == "processing"
        
        # Get status for unknown component
        unknown_status = self.manager.get_status("unknown_component")
        assert unknown_status == "unknown"
    
    def test_set_get_state(self):
        """Test setting and getting component state."""
        state_data = {
            "current_file": "/path/to/file.mp3",
            "progress": 0.75,
            "settings": {"quality": "high"}
        }
        
        # Set state
        self.manager.set_state("audio_processor", state_data)
        
        # Get state
        retrieved_state = self.manager.get_state("audio_processor")
        assert retrieved_state == state_data
        
        # Get state for unknown component
        unknown_state = self.manager.get_state("unknown_component")
        assert unknown_state == {}
    
    def test_status_history(self):
        """Test status change history."""
        # Set initial status
        self.manager.set_status("component1", "idle")
        self.manager.set_status("component1", "processing")
        self.manager.set_status("component1", "complete")
        
        # Get history
        history = self.manager.get_status_history("component1")
        assert len(history) == 3
        
        # Check history order (should be chronological)
        assert history[0]['old_status'] == ""
        assert history[0]['new_status'] == "idle"
        assert history[1]['old_status'] == "idle"
        assert history[1]['new_status'] == "processing"
        assert history[2]['old_status'] == "processing"
        assert history[2]['new_status'] == "complete"
    
    def test_get_all_status(self):
        """Test getting status for all components."""
        self.manager.set_status("component1", "active")
        self.manager.set_status("component2", "idle")
        self.manager.set_status("component3", "error")
        
        all_status = self.manager.get_all_status()
        
        assert len(all_status) == 3
        assert all_status["component1"] == "active"
        assert all_status["component2"] == "idle"
        assert all_status["component3"] == "error"


class TestFeedbackSystem:
    """Test the integrated feedback system."""
    
    def setup_method(self):
        """Setup test feedback system."""
        self.system = FeedbackSystem(FeedbackLevel.NORMAL)
    
    def test_feedback_system_creation(self):
        """Test creating a feedback system."""
        assert self.system.feedback_level == FeedbackLevel.NORMAL
        assert self.system.progress_tracker is not None
        assert self.system.notification_manager is not None
        assert self.system.status_manager is not None
    
    def test_set_feedback_level(self):
        """Test setting feedback level."""
        self.system.set_feedback_level(FeedbackLevel.DETAILED)
        assert self.system.feedback_level == FeedbackLevel.DETAILED
        
        self.system.set_feedback_level(FeedbackLevel.MINIMAL)
        assert self.system.feedback_level == FeedbackLevel.MINIMAL
    
    def test_progress_completion_notification(self):
        """Test that progress completion triggers notifications."""
        # Start a progress operation
        progress_id = self.system.progress_tracker.start_progress("Test Task")
        
        # Complete it
        self.system.progress_tracker.complete_progress(progress_id)
        
        # Should have created a success notification
        notifications = self.system.notification_manager.get_all_notifications()
        success_notifications = [n for n in notifications if n.notification_type == NotificationType.SUCCESS]
        assert len(success_notifications) > 0
        
        success_notif = success_notifications[0]
        assert "completed successfully" in success_notif.message.lower()
    
    def test_progress_error_notification(self):
        """Test that progress errors trigger notifications."""
        # Start a progress operation
        progress_id = self.system.progress_tracker.start_progress("Test Task")
        
        # Fail it
        self.system.progress_tracker.complete_progress(progress_id, success=False, message="Test error")
        
        # Should have created an error notification
        notifications = self.system.notification_manager.get_all_notifications()
        error_notifications = [n for n in notifications if n.notification_type == NotificationType.ERROR]
        assert len(error_notifications) > 0
        
        error_notif = error_notifications[0]
        assert "failed" in error_notif.message.lower()
    
    def test_get_system_status(self):
        """Test getting overall system status."""
        # Create some activity
        progress_id = self.system.progress_tracker.start_progress("Test Task")
        notif_id = self.system.notification_manager.show_info("Test", "Message")
        self.system.status_manager.set_status("component1", "active")
        
        status = self.system.get_system_status()
        
        assert status['active_progress_count'] == 1
        assert status['active_notifications_count'] >= 1  # At least the one we created
        assert status['feedback_level'] == FeedbackLevel.NORMAL.value
        assert 'component1' in status['component_status']
        assert status['component_status']['component1'] == "active"


class TestProgressTypes:
    """Test different progress types."""
    
    def test_determinate_progress(self):
        """Test determinate progress (known total)."""
        progress = ProgressInfo(
            "prog_1", "Test", 
            progress_type=ProgressType.DETERMINATE,
            current=25, total=100
        )
        
        assert progress.progress_type == ProgressType.DETERMINATE
        assert progress.percentage == 25.0
    
    def test_indeterminate_progress(self):
        """Test indeterminate progress (unknown total)."""
        progress = ProgressInfo(
            "prog_1", "Test",
            progress_type=ProgressType.INDETERMINATE
        )
        
        assert progress.progress_type == ProgressType.INDETERMINATE
        # Percentage calculation should still work
        assert progress.percentage >= 0.0
    
    def test_stepped_progress(self):
        """Test stepped progress."""
        progress = ProgressInfo(
            "prog_1", "Test",
            progress_type=ProgressType.STEPPED,
            current=3, total=5
        )
        
        assert progress.progress_type == ProgressType.STEPPED
        assert progress.percentage == 60.0


class TestNotificationTypes:
    """Test different notification types."""
    
    def test_all_notification_types(self):
        """Test all notification types."""
        types = [
            NotificationType.INFO,
            NotificationType.SUCCESS,
            NotificationType.WARNING,
            NotificationType.ERROR,
            NotificationType.PROGRESS
        ]
        
        for notif_type in types:
            notification = NotificationInfo(
                "test_id", "Test", "Message", notif_type
            )
            assert notification.notification_type == notif_type


class TestFeedbackLevels:
    """Test different feedback levels."""
    
    def test_all_feedback_levels(self):
        """Test all feedback levels."""
        levels = [
            FeedbackLevel.MINIMAL,
            FeedbackLevel.NORMAL,
            FeedbackLevel.DETAILED,
            FeedbackLevel.VERBOSE
        ]
        
        for level in levels:
            system = FeedbackSystem(level)
            assert system.feedback_level == level


class TestIntegrationScenarios:
    """Test integration scenarios."""
    
    def test_complete_workflow_feedback(self):
        """Test feedback for a complete workflow."""
        system = FeedbackSystem(FeedbackLevel.DETAILED)
        
        # Start a complex operation
        progress_id = system.progress_tracker.start_progress(
            "Processing Video",
            "Applying lip sync to video file",
            total=100,
            can_cancel=True
        )
        
        # Update status
        system.status_manager.set_status("video_processor", "processing")
        
        # Simulate progress updates
        for i in range(0, 101, 25):
            system.progress_tracker.update_progress(
                progress_id, i, f"Step {i//25 + 1} of 5"
            )
        
        # Complete successfully
        system.progress_tracker.complete_progress(progress_id)
        system.status_manager.set_status("video_processor", "complete")
        
        # Verify final state
        status = system.get_system_status()
        assert status['active_progress_count'] == 0
        assert system.status_manager.get_status("video_processor") == "complete"
        
        # Should have completion notification
        notifications = system.notification_manager.get_all_notifications()
        assert len(notifications) > 0
