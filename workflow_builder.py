"""
Visual Workflow Builder for Auto Latent Video Processor.

This module provides a node-based workflow creation system with drag-and-drop interface,
custom nodes, conditional logic, loops, and marketplace integration.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QTextEdit, QListWidget, QListWidgetItem, QGroupBox, QSplitter,
    QTreeWidget, QTreeWidgetItem, QTabWidget, QComboBox, QSpinBox,
    QCheckBox, QProgressBar, QFileDialog, QMessageBox, QInputDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QScrollArea, QFrame, QGridLayout, QSlider, QGraphicsView,
    QGraphicsScene, QGraphicsItem, QGraphicsRectItem, QGraphicsEllipseItem,
    QGraphicsLineItem, QGraphicsTextItem, QGraphicsProxyWidget,
    QMenu, QToolBar, QStatusBar, QDockWidget, QMainWindow
)
from PySide6.QtCore import (
    Qt, Signal, QTimer, QThread, QObject, QPointF, QRectF, QSizeF,
    QPropertyAnimation, QEasingCurve, QParallelAnimationGroup,
    QSequentialAnimationGroup, QMimeData, QByteArray
)
from PySide6.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QPixmap, QIcon,
    QLinearGradient, QPainterPath, QPolygonF, QFontMetrics,
    QCursor, QPalette, QAction, QDrag, QTransform
)


class NodeType(Enum):
    """Types of workflow nodes."""
    INPUT = "input"
    OUTPUT = "output"
    PROCESSING = "processing"
    CONDITION = "condition"
    LOOP = "loop"
    FUNCTION = "function"
    VARIABLE = "variable"
    CONSTANT = "constant"


class DataType(Enum):
    """Data types for node connections."""
    ANY = "any"
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    FILE_PATH = "file_path"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    ARRAY = "array"
    OBJECT = "object"


class ConnectionType(Enum):
    """Types of node connections."""
    DATA = "data"
    EXECUTION = "execution"
    CONDITION = "condition"


@dataclass
class NodePort:
    """Represents an input or output port on a node."""
    id: str
    name: str
    data_type: DataType
    connection_type: ConnectionType
    is_input: bool
    required: bool = True
    default_value: Any = None
    description: str = ""

    def is_compatible_with(self, other: 'NodePort') -> bool:
        """Check if this port can connect to another port."""
        if self.is_input == other.is_input:
            return False  # Can't connect input to input or output to output

        if self.connection_type != other.connection_type:
            return False  # Connection types must match

        # Data type compatibility
        if (self.data_type == DataType.ANY or other.data_type == DataType.ANY or
            self.data_type == other.data_type):
            return True

        return False


@dataclass
class NodeConnection:
    """Represents a connection between two node ports."""
    id: str
    source_node_id: str
    source_port_id: str
    target_node_id: str
    target_port_id: str
    connection_type: ConnectionType

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NodeConnection':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class WorkflowNode:
    """Base class for workflow nodes."""
    id: str
    node_type: NodeType
    name: str
    description: str
    position: Tuple[float, float] = (0.0, 0.0)
    input_ports: List[NodePort] = field(default_factory=list)
    output_ports: List[NodePort] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True

    def __post_init__(self):
        """Initialize default ports if not provided."""
        if not self.input_ports and not self.output_ports:
            self._create_default_ports()

    def _create_default_ports(self):
        """Create default ports for the node."""
        # Most nodes have execution flow
        if self.node_type not in [NodeType.CONSTANT, NodeType.VARIABLE]:
            self.input_ports.append(NodePort(
                id=f"{self.id}_exec_in",
                name="Execute",
                data_type=DataType.ANY,
                connection_type=ConnectionType.EXECUTION,
                is_input=True,
                required=False
            ))

            self.output_ports.append(NodePort(
                id=f"{self.id}_exec_out",
                name="Execute",
                data_type=DataType.ANY,
                connection_type=ConnectionType.EXECUTION,
                is_input=False
            ))

    def get_port(self, port_id: str) -> Optional[NodePort]:
        """Get a port by ID."""
        for port in self.input_ports + self.output_ports:
            if port.id == port_id:
                return port
        return None

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the node with given inputs."""
        # Override in subclasses
        return {}

    def validate(self) -> List[str]:
        """Validate the node configuration."""
        errors = []

        # Check required inputs
        for port in self.input_ports:
            if port.required and port.id not in self.properties:
                errors.append(f"Required input '{port.name}' is not connected")

        return errors

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "node_type": self.node_type.value,
            "name": self.name,
            "description": self.description,
            "position": self.position,
            "input_ports": [asdict(port) for port in self.input_ports],
            "output_ports": [asdict(port) for port in self.output_ports],
            "properties": self.properties,
            "enabled": self.enabled
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowNode':
        """Create from dictionary."""
        node = cls(
            id=data["id"],
            node_type=NodeType(data["node_type"]),
            name=data["name"],
            description=data["description"],
            position=tuple(data["position"]),
            properties=data.get("properties", {}),
            enabled=data.get("enabled", True)
        )

        # Recreate ports
        node.input_ports = [
            NodePort(
                id=port_data["id"],
                name=port_data["name"],
                data_type=DataType(port_data["data_type"]),
                connection_type=ConnectionType(port_data["connection_type"]),
                is_input=port_data["is_input"],
                required=port_data.get("required", True),
                default_value=port_data.get("default_value"),
                description=port_data.get("description", "")
            )
            for port_data in data.get("input_ports", [])
        ]

        node.output_ports = [
            NodePort(
                id=port_data["id"],
                name=port_data["name"],
                data_type=DataType(port_data["data_type"]),
                connection_type=ConnectionType(port_data["connection_type"]),
                is_input=port_data["is_input"],
                required=port_data.get("required", True),
                default_value=port_data.get("default_value"),
                description=port_data.get("description", "")
            )
            for port_data in data.get("output_ports", [])
        ]

        return node


class VideoInputNode(WorkflowNode):
    """Node for video input."""

    def __init__(self, node_id: str = None):
        super().__init__(
            id=node_id or str(uuid.uuid4()),
            node_type=NodeType.INPUT,
            name="Video Input",
            description="Load a video file"
        )

        # Add video output port
        self.output_ports.append(NodePort(
            id=f"{self.id}_video_out",
            name="Video",
            data_type=DataType.VIDEO,
            connection_type=ConnectionType.DATA,
            is_input=False
        ))

        # Add file path property
        self.properties["file_path"] = ""

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute video input node."""
        file_path = self.properties.get("file_path", "")
        if not file_path or not os.path.exists(file_path):
            raise ValueError(f"Video file not found: {file_path}")

        return {
            f"{self.id}_video_out": file_path,
            f"{self.id}_exec_out": True
        }


class AudioInputNode(WorkflowNode):
    """Node for audio input."""

    def __init__(self, node_id: str = None):
        super().__init__(
            id=node_id or str(uuid.uuid4()),
            node_type=NodeType.INPUT,
            name="Audio Input",
            description="Load an audio file"
        )

        # Add audio output port
        self.output_ports.append(NodePort(
            id=f"{self.id}_audio_out",
            name="Audio",
            data_type=DataType.AUDIO,
            connection_type=ConnectionType.DATA,
            is_input=False
        ))

        # Add file path property
        self.properties["file_path"] = ""

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute audio input node."""
        file_path = self.properties.get("file_path", "")
        if not file_path or not os.path.exists(file_path):
            raise ValueError(f"Audio file not found: {file_path}")

        return {
            f"{self.id}_audio_out": file_path,
            f"{self.id}_exec_out": True
        }


class LipSyncNode(WorkflowNode):
    """Node for lip sync processing."""

    def __init__(self, node_id: str = None):
        super().__init__(
            id=node_id or str(uuid.uuid4()),
            node_type=NodeType.PROCESSING,
            name="Lip Sync",
            description="Synchronize lips with audio"
        )

        # Add input ports
        self.input_ports.extend([
            NodePort(
                id=f"{self.id}_video_in",
                name="Video",
                data_type=DataType.VIDEO,
                connection_type=ConnectionType.DATA,
                is_input=True
            ),
            NodePort(
                id=f"{self.id}_audio_in",
                name="Audio",
                data_type=DataType.AUDIO,
                connection_type=ConnectionType.DATA,
                is_input=True
            )
        ])

        # Add output port
        self.output_ports.append(NodePort(
            id=f"{self.id}_video_out",
            name="Synced Video",
            data_type=DataType.VIDEO,
            connection_type=ConnectionType.DATA,
            is_input=False
        ))

        # Add properties
        self.properties.update({
            "seed": 0,
            "lips_expression": 1.0,
            "quality": "high"
        })

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute lip sync processing."""
        video_path = inputs.get(f"{self.id}_video_in")
        audio_path = inputs.get(f"{self.id}_audio_in")

        if not video_path or not audio_path:
            raise ValueError("Both video and audio inputs are required")

        # This would integrate with the actual lip sync processing
        # For now, return the video path (placeholder)
        return {
            f"{self.id}_video_out": video_path,
            f"{self.id}_exec_out": True
        }


class OutputNode(WorkflowNode):
    """Node for output/export."""

    def __init__(self, node_id: str = None):
        super().__init__(
            id=node_id or str(uuid.uuid4()),
            node_type=NodeType.OUTPUT,
            name="Video Output",
            description="Export the final video"
        )

        # Add input port
        self.input_ports.append(NodePort(
            id=f"{self.id}_video_in",
            name="Video",
            data_type=DataType.VIDEO,
            connection_type=ConnectionType.DATA,
            is_input=True
        ))

        # Add properties
        self.properties.update({
            "output_path": "",
            "format": "mp4",
            "quality": "high",
            "resolution": "1920x1080"
        })

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute output node."""
        video_path = inputs.get(f"{self.id}_video_in")
        output_path = self.properties.get("output_path", "")

        if not video_path:
            raise ValueError("Video input is required")

        if not output_path:
            raise ValueError("Output path is required")

        # This would integrate with the actual export processing
        return {
            f"{self.id}_exec_out": True
        }


class ConditionNode(WorkflowNode):
    """Node for conditional logic."""

    def __init__(self, node_id: str = None):
        super().__init__(
            id=node_id or str(uuid.uuid4()),
            node_type=NodeType.CONDITION,
            name="Condition",
            description="Conditional execution based on input"
        )

        # Add input ports
        self.input_ports.extend([
            NodePort(
                id=f"{self.id}_condition_in",
                name="Condition",
                data_type=DataType.BOOLEAN,
                connection_type=ConnectionType.DATA,
                is_input=True
            ),
            NodePort(
                id=f"{self.id}_value_a",
                name="Value A",
                data_type=DataType.ANY,
                connection_type=ConnectionType.DATA,
                is_input=True,
                required=False
            ),
            NodePort(
                id=f"{self.id}_value_b",
                name="Value B",
                data_type=DataType.ANY,
                connection_type=ConnectionType.DATA,
                is_input=True,
                required=False
            )
        ])

        # Add output ports
        self.output_ports.extend([
            NodePort(
                id=f"{self.id}_true_out",
                name="True",
                data_type=DataType.ANY,
                connection_type=ConnectionType.EXECUTION,
                is_input=False
            ),
            NodePort(
                id=f"{self.id}_false_out",
                name="False",
                data_type=DataType.ANY,
                connection_type=ConnectionType.EXECUTION,
                is_input=False
            )
        ])

        # Add properties
        self.properties.update({
            "operator": "==",  # ==, !=, <, >, <=, >=
            "compare_values": True
        })

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute condition node."""
        if self.properties.get("compare_values", True):
            # Compare two values
            value_a = inputs.get(f"{self.id}_value_a")
            value_b = inputs.get(f"{self.id}_value_b")
            operator = self.properties.get("operator", "==")

            if operator == "==":
                condition = value_a == value_b
            elif operator == "!=":
                condition = value_a != value_b
            elif operator == "<":
                condition = value_a < value_b
            elif operator == ">":
                condition = value_a > value_b
            elif operator == "<=":
                condition = value_a <= value_b
            elif operator == ">=":
                condition = value_a >= value_b
            else:
                condition = False
        else:
            # Use direct boolean input
            condition = inputs.get(f"{self.id}_condition_in", False)

        if condition:
            return {f"{self.id}_true_out": True}
        else:
            return {f"{self.id}_false_out": True}


class LoopNode(WorkflowNode):
    """Node for loop logic."""

    def __init__(self, node_id: str = None):
        super().__init__(
            id=node_id or str(uuid.uuid4()),
            node_type=NodeType.LOOP,
            name="Loop",
            description="Execute nodes in a loop"
        )

        # Add input ports
        self.input_ports.extend([
            NodePort(
                id=f"{self.id}_array_in",
                name="Array",
                data_type=DataType.ARRAY,
                connection_type=ConnectionType.DATA,
                is_input=True
            ),
            NodePort(
                id=f"{self.id}_loop_body",
                name="Loop Body",
                data_type=DataType.ANY,
                connection_type=ConnectionType.EXECUTION,
                is_input=True
            )
        ])

        # Add output ports
        self.output_ports.extend([
            NodePort(
                id=f"{self.id}_item_out",
                name="Current Item",
                data_type=DataType.ANY,
                connection_type=ConnectionType.DATA,
                is_input=False
            ),
            NodePort(
                id=f"{self.id}_index_out",
                name="Index",
                data_type=DataType.INTEGER,
                connection_type=ConnectionType.DATA,
                is_input=False
            ),
            NodePort(
                id=f"{self.id}_completed_out",
                name="Completed",
                data_type=DataType.ANY,
                connection_type=ConnectionType.EXECUTION,
                is_input=False
            )
        ])

    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute loop node."""
        array = inputs.get(f"{self.id}_array_in", [])

        # This would need special handling in the workflow executor
        # to actually loop through the array
        return {
            f"{self.id}_completed_out": True
        }


class WorkflowExecutor:
    """Executes workflow graphs."""

    def __init__(self):
        self.nodes: Dict[str, WorkflowNode] = {}
        self.connections: List[NodeConnection] = []
        self.execution_order: List[str] = []
        self.node_outputs: Dict[str, Dict[str, Any]] = {}

    def add_node(self, node: WorkflowNode):
        """Add a node to the workflow."""
        self.nodes[node.id] = node
        self._update_execution_order()

    def remove_node(self, node_id: str):
        """Remove a node from the workflow."""
        if node_id in self.nodes:
            # Remove connections involving this node
            self.connections = [
                conn for conn in self.connections
                if conn.source_node_id != node_id and conn.target_node_id != node_id
            ]
            del self.nodes[node_id]
            self._update_execution_order()

    def add_connection(self, connection: NodeConnection) -> bool:
        """Add a connection between nodes."""
        # Validate connection
        source_node = self.nodes.get(connection.source_node_id)
        target_node = self.nodes.get(connection.target_node_id)

        if not source_node or not target_node:
            return False

        source_port = source_node.get_port(connection.source_port_id)
        target_port = target_node.get_port(connection.target_port_id)

        if not source_port or not target_port:
            return False

        if not source_port.is_compatible_with(target_port):
            return False

        # Remove existing connection to target port (only one input per port)
        self.connections = [
            conn for conn in self.connections
            if not (conn.target_node_id == connection.target_node_id and
                   conn.target_port_id == connection.target_port_id)
        ]

        self.connections.append(connection)
        self._update_execution_order()
        return True

    def remove_connection(self, connection_id: str):
        """Remove a connection."""
        self.connections = [
            conn for conn in self.connections if conn.id != connection_id
        ]
        self._update_execution_order()

    def execute(self) -> Dict[str, Any]:
        """Execute the entire workflow."""
        self.node_outputs.clear()
        results = {}

        try:
            for node_id in self.execution_order:
                node = self.nodes[node_id]
                if not node.enabled:
                    continue

                # Gather inputs for this node
                inputs = self._gather_node_inputs(node_id)

                # Execute the node
                outputs = node.execute(inputs)
                self.node_outputs[node_id] = outputs

                # If this is an output node, add to results
                if node.node_type == NodeType.OUTPUT:
                    results[node_id] = outputs

            return results

        except Exception as e:
            logging.error(f"Workflow execution failed: {e}")
            raise

    def validate(self) -> List[str]:
        """Validate the entire workflow."""
        errors = []

        # Validate individual nodes
        for node in self.nodes.values():
            node_errors = node.validate()
            errors.extend([f"Node {node.name}: {error}" for error in node_errors])

        # Check for cycles
        if self._has_cycles():
            errors.append("Workflow contains cycles")

        # Check for disconnected nodes
        disconnected = self._find_disconnected_nodes()
        if disconnected:
            errors.append(f"Disconnected nodes: {', '.join(disconnected)}")

        return errors

    def _gather_node_inputs(self, node_id: str) -> Dict[str, Any]:
        """Gather inputs for a node from connected outputs."""
        inputs = {}

        for connection in self.connections:
            if connection.target_node_id == node_id:
                source_outputs = self.node_outputs.get(connection.source_node_id, {})
                if connection.source_port_id in source_outputs:
                    inputs[connection.target_port_id] = source_outputs[connection.source_port_id]

        return inputs

    def _update_execution_order(self):
        """Update the execution order using topological sort."""
        # Simple topological sort implementation
        in_degree = {node_id: 0 for node_id in self.nodes}

        # Calculate in-degrees
        for connection in self.connections:
            if connection.connection_type == ConnectionType.EXECUTION:
                in_degree[connection.target_node_id] += 1

        # Find nodes with no incoming edges
        queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            node_id = queue.pop(0)
            result.append(node_id)

            # Remove edges from this node
            for connection in self.connections:
                if (connection.source_node_id == node_id and
                    connection.connection_type == ConnectionType.EXECUTION):
                    in_degree[connection.target_node_id] -= 1
                    if in_degree[connection.target_node_id] == 0:
                        queue.append(connection.target_node_id)

        self.execution_order = result

    def _has_cycles(self) -> bool:
        """Check if the workflow has cycles."""
        return len(self.execution_order) != len(self.nodes)

    def _find_disconnected_nodes(self) -> List[str]:
        """Find nodes that are not connected to the main workflow."""
        connected = set()

        # Find all nodes reachable from input nodes
        input_nodes = [node_id for node_id, node in self.nodes.items()
                      if node.node_type == NodeType.INPUT]

        def dfs(node_id):
            if node_id in connected:
                return
            connected.add(node_id)

            for connection in self.connections:
                if connection.source_node_id == node_id:
                    dfs(connection.target_node_id)

        for input_node in input_nodes:
            dfs(input_node)

        return [node_id for node_id in self.nodes if node_id not in connected]

    def to_dict(self) -> Dict[str, Any]:
        """Convert workflow to dictionary for serialization."""
        return {
            "nodes": {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            "connections": [conn.to_dict() for conn in self.connections]
        }

    def from_dict(self, data: Dict[str, Any]):
        """Load workflow from dictionary."""
        self.nodes.clear()
        self.connections.clear()

        # Load nodes
        for node_id, node_data in data.get("nodes", {}).items():
            node = WorkflowNode.from_dict(node_data)
            self.nodes[node_id] = node

        # Load connections
        for conn_data in data.get("connections", []):
            connection = NodeConnection.from_dict(conn_data)
            self.connections.append(connection)

        self._update_execution_order()


class NodeFactory:
    """Factory for creating workflow nodes."""

    _node_types = {
        "video_input": VideoInputNode,
        "audio_input": AudioInputNode,
        "lip_sync": LipSyncNode,
        "output": OutputNode,
        "condition": ConditionNode,
        "loop": LoopNode
    }

    @classmethod
    def create_node(cls, node_type: str, node_id: str = None) -> Optional[WorkflowNode]:
        """Create a node of the specified type."""
        if node_type in cls._node_types:
            return cls._node_types[node_type](node_id)
        return None

    @classmethod
    def get_available_types(cls) -> List[str]:
        """Get list of available node types."""
        return list(cls._node_types.keys())

    @classmethod
    def register_node_type(cls, type_name: str, node_class):
        """Register a new node type."""
        cls._node_types[type_name] = node_class


class NodeGraphicsItem(QGraphicsRectItem):
    """Graphics item for displaying workflow nodes."""

    def __init__(self, node: WorkflowNode, parent=None):
        super().__init__(parent)
        self.node = node
        self.width = 150
        self.height = 100
        self.port_radius = 8
        self.port_spacing = 20

        self._setup_appearance()
        self._setup_ports()

        # Make item movable and selectable
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)

        # Set position
        self.setPos(node.position[0], node.position[1])

    def _setup_appearance(self):
        """Setup the visual appearance of the node."""
        # Set size
        self.setRect(0, 0, self.width, self.height)

        # Set colors based on node type
        color_map = {
            NodeType.INPUT: QColor(100, 150, 255),
            NodeType.OUTPUT: QColor(255, 100, 100),
            NodeType.PROCESSING: QColor(150, 255, 100),
            NodeType.CONDITION: QColor(255, 200, 100),
            NodeType.LOOP: QColor(255, 150, 255),
            NodeType.FUNCTION: QColor(200, 200, 255),
            NodeType.VARIABLE: QColor(255, 255, 100),
            NodeType.CONSTANT: QColor(200, 200, 200)
        }

        color = color_map.get(self.node.node_type, QColor(128, 128, 128))

        # Set brush and pen
        self.setBrush(QBrush(color))
        self.setPen(QPen(color.darker(150), 2))

        # Tooltip
        self.setToolTip(f"{self.node.name}\n{self.node.description}")

    def _setup_ports(self):
        """Setup port graphics."""
        self.input_port_items = []
        self.output_port_items = []

        # Create input port graphics
        for i, port in enumerate(self.node.input_ports):
            port_item = QGraphicsEllipseItem(
                -self.port_radius,
                20 + i * self.port_spacing - self.port_radius,
                self.port_radius * 2,
                self.port_radius * 2,
                self
            )
            port_item.setBrush(QBrush(QColor(100, 100, 100)))
            port_item.setPen(QPen(QColor(50, 50, 50), 1))
            port_item.setToolTip(f"{port.name} ({port.data_type.value})")
            self.input_port_items.append(port_item)

        # Create output port graphics
        for i, port in enumerate(self.node.output_ports):
            port_item = QGraphicsEllipseItem(
                self.width - self.port_radius,
                20 + i * self.port_spacing - self.port_radius,
                self.port_radius * 2,
                self.port_radius * 2,
                self
            )
            port_item.setBrush(QBrush(QColor(200, 200, 200)))
            port_item.setPen(QPen(QColor(150, 150, 150), 1))
            port_item.setToolTip(f"{port.name} ({port.data_type.value})")
            self.output_port_items.append(port_item)

    def paint(self, painter: QPainter, option, widget):
        """Custom paint method."""
        super().paint(painter, option, widget)

        # Draw node name
        painter.setPen(QPen(QColor(255, 255, 255)))
        painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))

        rect = self.rect()
        text_rect = QRectF(rect.x() + 5, rect.y() + 5, rect.width() - 10, 15)
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, self.node.name)

        # Draw port labels
        painter.setFont(QFont("Arial", 8))

        # Input port labels
        for i, port in enumerate(self.node.input_ports):
            y = 20 + i * self.port_spacing
            painter.drawText(self.port_radius + 5, y + 4, port.name)

        # Output port labels
        for i, port in enumerate(self.node.output_ports):
            y = 20 + i * self.port_spacing
            text_width = painter.fontMetrics().horizontalAdvance(port.name)
            painter.drawText(self.width - self.port_radius - text_width - 5, y + 4, port.name)

    def itemChange(self, change, value):
        """Handle item changes."""
        if change == QGraphicsItem.GraphicsItemChange.ItemPositionChange:
            # Update node position
            new_pos = value
            self.node.position = (new_pos.x(), new_pos.y())

        return super().itemChange(change, value)

    def get_input_port_pos(self, port_index: int) -> QPointF:
        """Get the scene position of an input port."""
        if port_index < len(self.input_port_items):
            port_item = self.input_port_items[port_index]
            return self.mapToScene(port_item.rect().center())
        return QPointF()

    def get_output_port_pos(self, port_index: int) -> QPointF:
        """Get the scene position of an output port."""
        if port_index < len(self.output_port_items):
            port_item = self.output_port_items[port_index]
            return self.mapToScene(port_item.rect().center())
        return QPointF()


class ConnectionGraphicsItem(QGraphicsLineItem):
    """Graphics item for displaying connections between nodes."""

    def __init__(self, connection: NodeConnection, start_pos: QPointF, end_pos: QPointF, parent=None):
        super().__init__(parent)
        self.connection = connection
        self.start_pos = start_pos
        self.end_pos = end_pos

        self._setup_appearance()
        self._update_line()

    def _setup_appearance(self):
        """Setup the visual appearance of the connection."""
        # Set colors based on connection type
        color_map = {
            ConnectionType.DATA: QColor(100, 150, 255),
            ConnectionType.EXECUTION: QColor(255, 255, 255),
            ConnectionType.CONDITION: QColor(255, 200, 100)
        }

        color = color_map.get(self.connection.connection_type, QColor(128, 128, 128))
        self.setPen(QPen(color, 3))

    def _update_line(self):
        """Update the line geometry."""
        # Create a curved line for better visual appeal
        control_offset = abs(self.end_pos.x() - self.start_pos.x()) * 0.5

        path = QPainterPath()
        path.moveTo(self.start_pos)
        path.cubicTo(
            self.start_pos.x() + control_offset, self.start_pos.y(),
            self.end_pos.x() - control_offset, self.end_pos.y(),
            self.end_pos.x(), self.end_pos.y()
        )

        # Convert path to line for simplicity (could be enhanced to use path)
        self.setLine(self.start_pos.x(), self.start_pos.y(),
                    self.end_pos.x(), self.end_pos.y())

    def update_positions(self, start_pos: QPointF, end_pos: QPointF):
        """Update connection positions."""
        self.start_pos = start_pos
        self.end_pos = end_pos
        self._update_line()


class WorkflowGraphView(QGraphicsView):
    """Graphics view for the workflow graph."""

    node_selected = Signal(str)  # node_id
    connection_created = Signal(str, str, str, str)  # source_node, source_port, target_node, target_port

    def __init__(self):
        super().__init__()

        # Setup scene
        self.scene = QGraphicsScene()
        self.setScene(self.scene)

        # Setup view
        self.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Node and connection storage
        self.node_items: Dict[str, NodeGraphicsItem] = {}
        self.connection_items: Dict[str, ConnectionGraphicsItem] = {}

        # Connection creation state
        self.creating_connection = False
        self.connection_start_node = None
        self.connection_start_port = None
        self.temp_connection_line = None

    def add_node_item(self, node: WorkflowNode) -> NodeGraphicsItem:
        """Add a node to the view."""
        node_item = NodeGraphicsItem(node)
        self.scene.addItem(node_item)
        self.node_items[node.id] = node_item
        return node_item

    def remove_node_item(self, node_id: str):
        """Remove a node from the view."""
        if node_id in self.node_items:
            self.scene.removeItem(self.node_items[node_id])
            del self.node_items[node_id]

    def add_connection_item(self, connection: NodeConnection) -> Optional[ConnectionGraphicsItem]:
        """Add a connection to the view."""
        source_item = self.node_items.get(connection.source_node_id)
        target_item = self.node_items.get(connection.target_node_id)

        if not source_item or not target_item:
            return None

        # Find port indices
        source_port_index = -1
        for i, port in enumerate(source_item.node.output_ports):
            if port.id == connection.source_port_id:
                source_port_index = i
                break

        target_port_index = -1
        for i, port in enumerate(target_item.node.input_ports):
            if port.id == connection.target_port_id:
                target_port_index = i
                break

        if source_port_index == -1 or target_port_index == -1:
            return None

        # Get port positions
        start_pos = source_item.get_output_port_pos(source_port_index)
        end_pos = target_item.get_input_port_pos(target_port_index)

        # Create connection item
        connection_item = ConnectionGraphicsItem(connection, start_pos, end_pos)
        self.scene.addItem(connection_item)
        self.connection_items[connection.id] = connection_item

        return connection_item

    def remove_connection_item(self, connection_id: str):
        """Remove a connection from the view."""
        if connection_id in self.connection_items:
            self.scene.removeItem(self.connection_items[connection_id])
            del self.connection_items[connection_id]

    def update_connections(self):
        """Update all connection positions."""
        for connection_id, connection_item in self.connection_items.items():
            connection = connection_item.connection

            source_item = self.node_items.get(connection.source_node_id)
            target_item = self.node_items.get(connection.target_node_id)

            if source_item and target_item:
                # Find port indices and update positions
                source_port_index = -1
                for i, port in enumerate(source_item.node.output_ports):
                    if port.id == connection.source_port_id:
                        source_port_index = i
                        break

                target_port_index = -1
                for i, port in enumerate(target_item.node.input_ports):
                    if port.id == connection.target_port_id:
                        target_port_index = i
                        break

                if source_port_index != -1 and target_port_index != -1:
                    start_pos = source_item.get_output_port_pos(source_port_index)
                    end_pos = target_item.get_input_port_pos(target_port_index)
                    connection_item.update_positions(start_pos, end_pos)

    def mousePressEvent(self, event):
        """Handle mouse press events."""
        super().mousePressEvent(event)

        # Check if clicked on a node
        item = self.itemAt(event.pos())
        if isinstance(item, NodeGraphicsItem):
            self.node_selected.emit(item.node.id)

    def mouseMoveEvent(self, event):
        """Handle mouse move events."""
        super().mouseMoveEvent(event)

        # Update connections when nodes are moved
        self.update_connections()

    def wheelEvent(self, event):
        """Handle wheel events for zooming."""
        # Zoom in/out
        zoom_factor = 1.15
        if event.angleDelta().y() < 0:
            zoom_factor = 1.0 / zoom_factor

        self.scale(zoom_factor, zoom_factor)


class WorkflowBuilderWidget(QWidget):
    """Main workflow builder widget."""

    workflow_changed = Signal()
    node_selected = Signal(str)

    def __init__(self):
        super().__init__()

        # Workflow executor
        self.executor = WorkflowExecutor()

        # Setup UI
        self._setup_ui()
        self._connect_signals()

        # Create default workflow
        self._create_default_workflow()

    def _setup_ui(self):
        """Setup the workflow builder UI."""
        layout = QHBoxLayout(self)

        # Left panel - Node palette
        left_panel = self._create_node_palette()
        layout.addWidget(left_panel, 0)

        # Center - Workflow graph
        center_panel = self._create_graph_panel()
        layout.addWidget(center_panel, 1)

        # Right panel - Properties
        right_panel = self._create_properties_panel()
        layout.addWidget(right_panel, 0)

    def _create_node_palette(self) -> QWidget:
        """Create the node palette panel."""
        palette_widget = QWidget()
        palette_widget.setFixedWidth(200)
        layout = QVBoxLayout(palette_widget)

        # Title
        title_label = QLabel("Node Palette")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # Node categories
        categories = {
            "Input/Output": ["video_input", "audio_input", "output"],
            "Processing": ["lip_sync"],
            "Logic": ["condition", "loop"]
        }

        for category, node_types in categories.items():
            # Category group
            group = QGroupBox(category)
            group_layout = QVBoxLayout(group)

            for node_type in node_types:
                btn = QPushButton(node_type.replace("_", " ").title())
                btn.clicked.connect(lambda checked, nt=node_type: self._add_node(nt))
                group_layout.addWidget(btn)

            layout.addWidget(group)

        layout.addStretch()

        # Workflow controls
        controls_group = QGroupBox("Workflow")
        controls_layout = QVBoxLayout(controls_group)

        execute_btn = QPushButton("Execute Workflow")
        execute_btn.clicked.connect(self._execute_workflow)
        controls_layout.addWidget(execute_btn)

        validate_btn = QPushButton("Validate")
        validate_btn.clicked.connect(self._validate_workflow)
        controls_layout.addWidget(validate_btn)

        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self._clear_workflow)
        controls_layout.addWidget(clear_btn)

        layout.addWidget(controls_group)

        return palette_widget

    def _create_graph_panel(self) -> QWidget:
        """Create the main graph panel."""
        graph_widget = QWidget()
        layout = QVBoxLayout(graph_widget)

        # Toolbar
        toolbar = QToolBar()

        zoom_in_action = QAction("Zoom In", self)
        zoom_in_action.triggered.connect(self._zoom_in)
        toolbar.addAction(zoom_in_action)

        zoom_out_action = QAction("Zoom Out", self)
        zoom_out_action.triggered.connect(self._zoom_out)
        toolbar.addAction(zoom_out_action)

        fit_action = QAction("Fit to View", self)
        fit_action.triggered.connect(self._fit_to_view)
        toolbar.addAction(fit_action)

        layout.addWidget(toolbar)

        # Graph view
        self.graph_view = WorkflowGraphView()
        layout.addWidget(self.graph_view)

        return graph_widget

    def _create_properties_panel(self) -> QWidget:
        """Create the properties panel."""
        properties_widget = QWidget()
        properties_widget.setFixedWidth(250)
        layout = QVBoxLayout(properties_widget)

        # Title
        title_label = QLabel("Properties")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # Properties area
        self.properties_scroll = QScrollArea()
        self.properties_content = QWidget()
        self.properties_layout = QVBoxLayout(self.properties_content)
        self.properties_scroll.setWidget(self.properties_content)
        self.properties_scroll.setWidgetResizable(True)
        layout.addWidget(self.properties_scroll)

        # Initially show workflow properties
        self._show_workflow_properties()

        return properties_widget

    def _connect_signals(self):
        """Connect internal signals."""
        self.graph_view.node_selected.connect(self._on_node_selected)
        self.graph_view.connection_created.connect(self._on_connection_created)

    def _add_node(self, node_type: str):
        """Add a new node to the workflow."""
        node = NodeFactory.create_node(node_type)
        if node:
            # Position node in center of view
            view_center = self.graph_view.mapToScene(self.graph_view.rect().center())
            node.position = (view_center.x(), view_center.y())

            # Add to executor and view
            self.executor.add_node(node)
            self.graph_view.add_node_item(node)

            self.workflow_changed.emit()

    def _execute_workflow(self):
        """Execute the current workflow."""
        try:
            # Validate first
            errors = self.executor.validate()
            if errors:
                QMessageBox.warning(self, "Validation Errors", "\n".join(errors))
                return

            # Execute
            results = self.executor.execute()

            # Show results
            result_text = "Workflow executed successfully!\n\nResults:\n"
            for node_id, outputs in results.items():
                node = self.executor.nodes[node_id]
                result_text += f"\n{node.name}: {outputs}"

            QMessageBox.information(self, "Execution Complete", result_text)

        except Exception as e:
            QMessageBox.critical(self, "Execution Error", f"Workflow execution failed:\n{e}")

    def _validate_workflow(self):
        """Validate the current workflow."""
        errors = self.executor.validate()
        if errors:
            QMessageBox.warning(self, "Validation Errors", "\n".join(errors))
        else:
            QMessageBox.information(self, "Validation", "Workflow is valid!")

    def _clear_workflow(self):
        """Clear the entire workflow."""
        reply = QMessageBox.question(
            self, "Clear Workflow",
            "Are you sure you want to clear the entire workflow?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Clear executor
            self.executor.nodes.clear()
            self.executor.connections.clear()

            # Clear view
            self.graph_view.scene.clear()
            self.graph_view.node_items.clear()
            self.graph_view.connection_items.clear()

            self.workflow_changed.emit()

    def _zoom_in(self):
        """Zoom in on the graph."""
        self.graph_view.scale(1.2, 1.2)

    def _zoom_out(self):
        """Zoom out on the graph."""
        self.graph_view.scale(0.8, 0.8)

    def _fit_to_view(self):
        """Fit the workflow to the view."""
        self.graph_view.fitInView(self.graph_view.scene.itemsBoundingRect(), Qt.AspectRatioMode.KeepAspectRatio)

    def _on_node_selected(self, node_id: str):
        """Handle node selection."""
        self.node_selected.emit(node_id)
        self._show_node_properties(node_id)

    def _on_connection_created(self, source_node: str, source_port: str, target_node: str, target_port: str):
        """Handle connection creation."""
        connection = NodeConnection(
            id=str(uuid.uuid4()),
            source_node_id=source_node,
            source_port_id=source_port,
            target_node_id=target_node,
            target_port_id=target_port,
            connection_type=ConnectionType.DATA  # Default, could be determined from ports
        )

        if self.executor.add_connection(connection):
            self.graph_view.add_connection_item(connection)
            self.workflow_changed.emit()

    def _show_workflow_properties(self):
        """Show workflow-level properties."""
        # Clear existing properties
        for i in reversed(range(self.properties_layout.count())):
            self.properties_layout.itemAt(i).widget().setParent(None)

        # Add workflow properties
        workflow_group = QGroupBox("Workflow Properties")
        workflow_layout = QVBoxLayout(workflow_group)

        # Node count
        node_count_label = QLabel(f"Nodes: {len(self.executor.nodes)}")
        workflow_layout.addWidget(node_count_label)

        # Connection count
        connection_count_label = QLabel(f"Connections: {len(self.executor.connections)}")
        workflow_layout.addWidget(connection_count_label)

        # Execution order
        order_label = QLabel("Execution Order:")
        workflow_layout.addWidget(order_label)

        order_list = QListWidget()
        for node_id in self.executor.execution_order:
            node = self.executor.nodes.get(node_id)
            if node:
                order_list.addItem(node.name)
        workflow_layout.addWidget(order_list)

        self.properties_layout.addWidget(workflow_group)
        self.properties_layout.addStretch()

    def _show_node_properties(self, node_id: str):
        """Show properties for a specific node."""
        node = self.executor.nodes.get(node_id)
        if not node:
            return

        # Clear existing properties
        for i in reversed(range(self.properties_layout.count())):
            self.properties_layout.itemAt(i).widget().setParent(None)

        # Add node properties
        node_group = QGroupBox(f"Node: {node.name}")
        node_layout = QVBoxLayout(node_group)

        # Basic info
        type_label = QLabel(f"Type: {node.node_type.value}")
        node_layout.addWidget(type_label)

        desc_label = QLabel(f"Description: {node.description}")
        desc_label.setWordWrap(True)
        node_layout.addWidget(desc_label)

        # Enabled checkbox
        enabled_cb = QCheckBox("Enabled")
        enabled_cb.setChecked(node.enabled)
        enabled_cb.toggled.connect(lambda checked: setattr(node, 'enabled', checked))
        node_layout.addWidget(enabled_cb)

        # Properties
        if node.properties:
            props_group = QGroupBox("Properties")
            props_layout = QVBoxLayout(props_group)

            for key, value in node.properties.items():
                prop_layout = QHBoxLayout()
                prop_layout.addWidget(QLabel(f"{key}:"))

                if isinstance(value, bool):
                    prop_widget = QCheckBox()
                    prop_widget.setChecked(value)
                    prop_widget.toggled.connect(
                        lambda checked, k=key: node.properties.update({k: checked})
                    )
                elif isinstance(value, (int, float)):
                    prop_widget = QLineEdit(str(value))
                    prop_widget.textChanged.connect(
                        lambda text, k=key: node.properties.update({k: float(text) if '.' in text else int(text)})
                    )
                else:
                    prop_widget = QLineEdit(str(value))
                    prop_widget.textChanged.connect(
                        lambda text, k=key: node.properties.update({k: text})
                    )

                prop_layout.addWidget(prop_widget)
                props_layout.addLayout(prop_layout)

            node_layout.addWidget(props_group)

        self.properties_layout.addWidget(node_group)
        self.properties_layout.addStretch()

    def _create_default_workflow(self):
        """Create a default workflow for demonstration."""
        # Create nodes
        video_input = VideoInputNode()
        video_input.position = (50, 100)

        audio_input = AudioInputNode()
        audio_input.position = (50, 250)

        lip_sync = LipSyncNode()
        lip_sync.position = (300, 175)

        output = OutputNode()
        output.position = (550, 175)

        # Add nodes to executor
        for node in [video_input, audio_input, lip_sync, output]:
            self.executor.add_node(node)
            self.graph_view.add_node_item(node)

        # Create connections
        connections = [
            NodeConnection(
                id=str(uuid.uuid4()),
                source_node_id=video_input.id,
                source_port_id=f"{video_input.id}_video_out",
                target_node_id=lip_sync.id,
                target_port_id=f"{lip_sync.id}_video_in",
                connection_type=ConnectionType.DATA
            ),
            NodeConnection(
                id=str(uuid.uuid4()),
                source_node_id=audio_input.id,
                source_port_id=f"{audio_input.id}_audio_out",
                target_node_id=lip_sync.id,
                target_port_id=f"{lip_sync.id}_audio_in",
                connection_type=ConnectionType.DATA
            ),
            NodeConnection(
                id=str(uuid.uuid4()),
                source_node_id=lip_sync.id,
                source_port_id=f"{lip_sync.id}_video_out",
                target_node_id=output.id,
                target_port_id=f"{output.id}_video_in",
                connection_type=ConnectionType.DATA
            )
        ]

        # Add connections
        for connection in connections:
            if self.executor.add_connection(connection):
                self.graph_view.add_connection_item(connection)

        # Fit to view
        self._fit_to_view()

        self.workflow_changed.emit()