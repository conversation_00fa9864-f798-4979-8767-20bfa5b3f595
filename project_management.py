"""
Project Management System for Auto Latent Video Processor.

This module provides comprehensive project management including save/load,
version control, auto-save, recent files, and project templates.
"""

import os
import json
import shutil
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum
import threading
import time

from PySide6.QtCore import QObject, Signal, QTimer, QSettings, Qt
from PySide6.QtWidgets import (
    QMessageBox, QFileDialog, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QListWidget,
    QListWidgetItem, QGroupBox, QComboBox, QCheckBox, QProgressBar,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QInputDialog, QSplitter, QTabWidget, QTreeWidget, QTreeWidgetItem
)
from PySide6.QtGui import QFont, QIcon, QColor


class ProjectStatus(Enum):
    """Project status enumeration."""
    NEW = "new"
    MODIFIED = "modified"
    SAVED = "saved"
    PROCESSING = "processing"
    ERROR = "error"


@dataclass
class ProjectMetadata:
    """Project metadata information."""
    name: str
    description: str = ""
    created_date: datetime = field(default_factory=datetime.now)
    modified_date: datetime = field(default_factory=datetime.now)
    version: str = "1.0.0"
    author: str = ""
    tags: List[str] = field(default_factory=list)
    thumbnail_path: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['created_date'] = self.created_date.isoformat()
        data['modified_date'] = self.modified_date.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectMetadata':
        """Create from dictionary."""
        if 'created_date' in data:
            data['created_date'] = datetime.fromisoformat(data['created_date'])
        if 'modified_date' in data:
            data['modified_date'] = datetime.fromisoformat(data['modified_date'])
        return cls(**data)


@dataclass
class ProjectData:
    """Complete project data structure."""
    metadata: ProjectMetadata
    workflow_config: Dict[str, Any] = field(default_factory=dict)
    media_files: List[Dict[str, Any]] = field(default_factory=list)
    timeline_data: Dict[str, Any] = field(default_factory=dict)
    audio_tracks: List[Dict[str, Any]] = field(default_factory=list)
    processing_queue: List[Dict[str, Any]] = field(default_factory=list)
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'metadata': self.metadata.to_dict(),
            'workflow_config': self.workflow_config,
            'media_files': self.media_files,
            'timeline_data': self.timeline_data,
            'audio_tracks': self.audio_tracks,
            'processing_queue': self.processing_queue,
            'custom_settings': self.custom_settings
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectData':
        """Create from dictionary."""
        metadata = ProjectMetadata.from_dict(data.get('metadata', {}))
        return cls(
            metadata=metadata,
            workflow_config=data.get('workflow_config', {}),
            media_files=data.get('media_files', []),
            timeline_data=data.get('timeline_data', {}),
            audio_tracks=data.get('audio_tracks', []),
            processing_queue=data.get('processing_queue', []),
            custom_settings=data.get('custom_settings', {})
        )


class VersionControl:
    """Simple version control system for projects."""
    
    def __init__(self, project_dir: Path):
        self.project_dir = project_dir
        self.versions_dir = project_dir / ".versions"
        self.versions_dir.mkdir(exist_ok=True)
    
    def create_version(self, project_data: ProjectData, comment: str = "") -> str:
        """Create a new version of the project."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_id = f"v_{timestamp}"
        
        version_file = self.versions_dir / f"{version_id}.json"
        version_info = {
            'version_id': version_id,
            'timestamp': timestamp,
            'comment': comment,
            'data': project_data.to_dict()
        }
        
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
        
        logging.info(f"Created project version: {version_id}")
        return version_id
    
    def get_versions(self) -> List[Dict[str, Any]]:
        """Get list of all versions."""
        versions = []
        
        for version_file in self.versions_dir.glob("v_*.json"):
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_info = json.load(f)
                    versions.append({
                        'version_id': version_info['version_id'],
                        'timestamp': version_info['timestamp'],
                        'comment': version_info.get('comment', ''),
                        'file_path': str(version_file)
                    })
            except Exception as e:
                logging.warning(f"Failed to read version file {version_file}: {e}")
        
        # Sort by timestamp (newest first)
        versions.sort(key=lambda x: x['timestamp'], reverse=True)
        return versions
    
    def restore_version(self, version_id: str) -> Optional[ProjectData]:
        """Restore a specific version."""
        version_file = self.versions_dir / f"{version_id}.json"
        
        if not version_file.exists():
            logging.error(f"Version {version_id} not found")
            return None
        
        try:
            with open(version_file, 'r', encoding='utf-8') as f:
                version_info = json.load(f)
                project_data = ProjectData.from_dict(version_info['data'])
                logging.info(f"Restored project version: {version_id}")
                return project_data
        except Exception as e:
            logging.error(f"Failed to restore version {version_id}: {e}")
            return None
    
    def cleanup_old_versions(self, keep_count: int = 10):
        """Clean up old versions, keeping only the most recent ones."""
        versions = self.get_versions()
        
        if len(versions) <= keep_count:
            return
        
        # Remove old versions
        for version in versions[keep_count:]:
            try:
                os.remove(version['file_path'])
                logging.info(f"Removed old version: {version['version_id']}")
            except Exception as e:
                logging.warning(f"Failed to remove version {version['version_id']}: {e}")


class AutoSaveManager(QObject):
    """Manages automatic saving of projects."""
    
    auto_saved = Signal(str)  # Emitted when auto-save occurs
    
    def __init__(self, interval_minutes: int = 5):
        super().__init__()
        self.interval_minutes = interval_minutes
        self.enabled = True
        self.project_manager = None
        
        # Setup auto-save timer
        self.timer = QTimer()
        self.timer.timeout.connect(self._auto_save)
        self.timer.start(interval_minutes * 60 * 1000)  # Convert to milliseconds
    
    def set_project_manager(self, project_manager):
        """Set the project manager to auto-save."""
        self.project_manager = project_manager
    
    def set_interval(self, minutes: int):
        """Set auto-save interval in minutes."""
        self.interval_minutes = minutes
        self.timer.setInterval(minutes * 60 * 1000)
    
    def enable(self, enabled: bool = True):
        """Enable or disable auto-save."""
        self.enabled = enabled
        if enabled:
            self.timer.start()
        else:
            self.timer.stop()
    
    def _auto_save(self):
        """Perform auto-save if conditions are met."""
        if not self.enabled or not self.project_manager:
            return
        
        if self.project_manager.has_unsaved_changes():
            try:
                self.project_manager.auto_save()
                self.auto_saved.emit("Auto-save completed")
                logging.info("Auto-save completed successfully")
            except Exception as e:
                logging.error(f"Auto-save failed: {e}")


class ProjectManager(QObject):
    """Main project management system."""
    
    # Signals
    project_loaded = Signal(str)  # project_path
    project_saved = Signal(str)   # project_path
    project_modified = Signal()
    status_changed = Signal(str)  # status
    
    def __init__(self):
        super().__init__()
        
        self.current_project: Optional[ProjectData] = None
        self.current_project_path: Optional[Path] = None
        self.project_status = ProjectStatus.NEW
        self.unsaved_changes = False
        
        # Version control
        self.version_control: Optional[VersionControl] = None
        
        # Auto-save
        self.auto_save_manager = AutoSaveManager()
        self.auto_save_manager.set_project_manager(self)
        
        # Recent projects
        self.recent_projects: List[str] = []
        self._load_recent_projects()
        
        # Project templates
        self.templates: Dict[str, Dict[str, Any]] = {}
        self._load_templates()
        
        # Connect signals
        self.auto_save_manager.auto_saved.connect(self._on_auto_saved)
    
    def new_project(self, name: str, template: Optional[str] = None) -> bool:
        """Create a new project."""
        try:
            # Create metadata
            metadata = ProjectMetadata(
                name=name,
                description="",
                author=os.getenv('USERNAME', 'Unknown')
            )
            
            # Create project data
            if template and template in self.templates:
                # Use template
                template_data = self.templates[template].copy()
                template_data['metadata'] = metadata.to_dict()
                self.current_project = ProjectData.from_dict(template_data)
            else:
                # Create empty project
                self.current_project = ProjectData(metadata=metadata)
            
            # Reset state
            self.current_project_path = None
            self.unsaved_changes = True
            self._set_status(ProjectStatus.NEW)
            
            logging.info(f"Created new project: {name}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to create new project: {e}")
            return False
    
    def load_project(self, project_path: str) -> bool:
        """Load an existing project."""
        try:
            path = Path(project_path)
            
            if not path.exists():
                logging.error(f"Project file not found: {project_path}")
                return False
            
            # Load project data
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.current_project = ProjectData.from_dict(data)
            
            # Setup paths and version control
            self.current_project_path = path
            project_dir = path.parent
            self.version_control = VersionControl(project_dir)
            
            # Update state
            self.unsaved_changes = False
            self._set_status(ProjectStatus.SAVED)
            
            # Add to recent projects
            self._add_to_recent(project_path)
            
            self.project_loaded.emit(project_path)
            logging.info(f"Loaded project: {project_path}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to load project {project_path}: {e}")
            return False
    
    def save_project(self, project_path: Optional[str] = None) -> bool:
        """Save the current project."""
        if not self.current_project:
            logging.warning("No project to save")
            return False
        
        try:
            # Determine save path
            if project_path:
                save_path = Path(project_path)
            elif self.current_project_path:
                save_path = self.current_project_path
            else:
                logging.error("No save path specified")
                return False
            
            # Update metadata
            self.current_project.metadata.modified_date = datetime.now()
            
            # Create project directory if needed
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save project data
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_project.to_dict(), f, indent=2, ensure_ascii=False)
            
            # Setup version control if new path
            if not self.version_control or save_path != self.current_project_path:
                self.version_control = VersionControl(save_path.parent)
            
            # Create version
            if self.version_control:
                self.version_control.create_version(
                    self.current_project,
                    f"Saved at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
            
            # Update state
            self.current_project_path = save_path
            self.unsaved_changes = False
            self._set_status(ProjectStatus.SAVED)
            
            # Add to recent projects
            self._add_to_recent(str(save_path))
            
            self.project_saved.emit(str(save_path))
            logging.info(f"Saved project: {save_path}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to save project: {e}")
            return False
    
    def auto_save(self) -> bool:
        """Perform auto-save to temporary location."""
        if not self.current_project or not self.current_project_path:
            return False
        
        try:
            # Create auto-save path
            auto_save_path = self.current_project_path.parent / f".autosave_{self.current_project_path.name}"
            
            # Save to auto-save location
            with open(auto_save_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_project.to_dict(), f, indent=2, ensure_ascii=False)
            
            logging.debug(f"Auto-saved to: {auto_save_path}")
            return True
            
        except Exception as e:
            logging.error(f"Auto-save failed: {e}")
            return False
    
    def has_unsaved_changes(self) -> bool:
        """Check if there are unsaved changes."""
        return self.unsaved_changes
    
    def mark_modified(self):
        """Mark the project as modified."""
        if not self.unsaved_changes:
            self.unsaved_changes = True
            self._set_status(ProjectStatus.MODIFIED)
            self.project_modified.emit()
    
    def get_recent_projects(self) -> List[str]:
        """Get list of recent projects."""
        return self.recent_projects.copy()
    
    def get_project_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get available project templates."""
        return self.templates.copy()
    
    def create_template(self, name: str, description: str = "") -> bool:
        """Create a template from current project."""
        if not self.current_project:
            return False
        
        try:
            template_data = self.current_project.to_dict()
            template_data['template_info'] = {
                'name': name,
                'description': description,
                'created_date': datetime.now().isoformat()
            }
            
            self.templates[name] = template_data
            self._save_templates()
            
            logging.info(f"Created template: {name}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to create template: {e}")
            return False
    
    def _set_status(self, status: ProjectStatus):
        """Set project status and emit signal."""
        self.project_status = status
        self.status_changed.emit(status.value)
    
    def _add_to_recent(self, project_path: str):
        """Add project to recent projects list."""
        # Remove if already in list
        if project_path in self.recent_projects:
            self.recent_projects.remove(project_path)
        
        # Add to beginning
        self.recent_projects.insert(0, project_path)
        
        # Keep only last 10
        self.recent_projects = self.recent_projects[:10]
        
        # Save to settings
        self._save_recent_projects()
    
    def _load_recent_projects(self):
        """Load recent projects from settings."""
        settings = QSettings()
        recent = settings.value("recent_projects", [])
        if isinstance(recent, list):
            # Filter out non-existent files
            self.recent_projects = [p for p in recent if os.path.exists(p)]
        else:
            self.recent_projects = []
    
    def _save_recent_projects(self):
        """Save recent projects to settings."""
        settings = QSettings()
        settings.setValue("recent_projects", self.recent_projects)
    
    def _load_templates(self):
        """Load project templates."""
        templates_dir = Path("templates")
        if not templates_dir.exists():
            return
        
        for template_file in templates_dir.glob("*.json"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                    template_name = template_file.stem
                    self.templates[template_name] = template_data
            except Exception as e:
                logging.warning(f"Failed to load template {template_file}: {e}")
    
    def _save_templates(self):
        """Save project templates."""
        templates_dir = Path("templates")
        templates_dir.mkdir(exist_ok=True)
        
        for name, template_data in self.templates.items():
            template_file = templates_dir / f"{name}.json"
            try:
                with open(template_file, 'w', encoding='utf-8') as f:
                    json.dump(template_data, f, indent=2, ensure_ascii=False)
            except Exception as e:
                logging.warning(f"Failed to save template {name}: {e}")
    
    def _on_auto_saved(self, message: str):
        """Handle auto-save completion."""
        logging.debug(f"Auto-save: {message}")
    
    def close_project(self) -> bool:
        """Close the current project."""
        if self.has_unsaved_changes():
            # Would show save dialog in real implementation
            logging.warning("Project has unsaved changes")
        
        self.current_project = None
        self.current_project_path = None
        self.version_control = None
        self.unsaved_changes = False
        self._set_status(ProjectStatus.NEW)
        
        return True


class ProjectWidget(QWidget):
    """Widget for project management UI."""

    project_loaded = Signal(str)
    project_saved = Signal(str)
    project_created = Signal(str)

    def __init__(self, project_manager: ProjectManager):
        super().__init__()
        self.project_manager = project_manager
        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Setup the project management UI."""
        layout = QVBoxLayout(self)

        # Project info section
        info_group = QGroupBox("Current Project")
        info_layout = QVBoxLayout(info_group)

        self.project_name_label = QLabel("No project loaded")
        self.project_name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        info_layout.addWidget(self.project_name_label)

        self.project_description_label = QLabel("")
        info_layout.addWidget(self.project_description_label)

        self.project_status_label = QLabel("")
        info_layout.addWidget(self.project_status_label)

        self.project_modified_label = QLabel("")
        info_layout.addWidget(self.project_modified_label)

        layout.addWidget(info_group)

        # Project actions
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)

        # File operations
        file_layout = QHBoxLayout()

        new_btn = QPushButton("New")
        new_btn.clicked.connect(self._new_project)
        file_layout.addWidget(new_btn)

        open_btn = QPushButton("Open")
        open_btn.clicked.connect(self._open_project)
        file_layout.addWidget(open_btn)

        save_btn = QPushButton("Save")
        save_btn.clicked.connect(self._save_project)
        file_layout.addWidget(save_btn)

        actions_layout.addLayout(file_layout)

        # Version control
        version_layout = QHBoxLayout()

        create_version_btn = QPushButton("Create Version")
        create_version_btn.clicked.connect(self._create_version)
        version_layout.addWidget(create_version_btn)

        view_versions_btn = QPushButton("View Versions")
        view_versions_btn.clicked.connect(self._view_versions)
        version_layout.addWidget(view_versions_btn)

        actions_layout.addLayout(version_layout)

        layout.addWidget(actions_group)

        # Recent projects
        recent_group = QGroupBox("Recent Projects")
        recent_layout = QVBoxLayout(recent_group)

        self.recent_list = QListWidget()
        self.recent_list.itemDoubleClicked.connect(self._open_recent_project)
        recent_layout.addWidget(self.recent_list)

        layout.addWidget(recent_group)

        # Auto-save status
        auto_save_group = QGroupBox("Auto-Save")
        auto_save_layout = QVBoxLayout(auto_save_group)

        self.auto_save_enabled = QCheckBox("Enable Auto-Save")
        self.auto_save_enabled.setChecked(True)
        self.auto_save_enabled.toggled.connect(self._toggle_auto_save)
        auto_save_layout.addWidget(self.auto_save_enabled)

        self.auto_save_status = QLabel("Auto-save: Ready")
        auto_save_layout.addWidget(self.auto_save_status)

        layout.addWidget(auto_save_group)

        # Update UI
        self._update_ui()

    def _connect_signals(self):
        """Connect project manager signals."""
        self.project_manager.project_loaded.connect(self._on_project_loaded)
        self.project_manager.project_saved.connect(self._on_project_saved)
        self.project_manager.project_created.connect(self._on_project_created)
        self.project_manager.status_changed.connect(self._on_status_changed)

        if hasattr(self.project_manager, 'auto_save_manager'):
            self.project_manager.auto_save_manager.auto_saved.connect(
                lambda msg: self.auto_save_status.setText(f"Auto-save: {msg}")
            )

    def _update_ui(self):
        """Update the UI with current project information."""
        if self.project_manager.current_project:
            project = self.project_manager.current_project
            self.project_name_label.setText(project.metadata.name)
            self.project_description_label.setText(project.metadata.description or "No description")

            # Status
            status_text = f"Status: {self.project_manager.status.value.title()}"
            if self.project_manager.has_unsaved_changes():
                status_text += " (Modified)"
            self.project_status_label.setText(status_text)

            # Modified date
            modified_date = project.metadata.modified_date
            if isinstance(modified_date, str):
                try:
                    modified_date = datetime.fromisoformat(modified_date)
                except ValueError:
                    modified_date = datetime.now()
            self.project_modified_label.setText(f"Modified: {modified_date.strftime('%Y-%m-%d %H:%M')}")
        else:
            self.project_name_label.setText("No project loaded")
            self.project_description_label.setText("")
            self.project_status_label.setText("")
            self.project_modified_label.setText("")

        # Update recent projects
        self._update_recent_projects()

    def _update_recent_projects(self):
        """Update the recent projects list."""
        self.recent_list.clear()
        for project_path in self.project_manager.get_recent_projects():
            if os.path.exists(project_path):
                item = QListWidgetItem(os.path.basename(project_path))
                item.setData(Qt.ItemDataRole.UserRole, project_path)
                item.setToolTip(project_path)
                self.recent_list.addItem(item)

    def _new_project(self):
        """Create a new project."""
        name, ok = QInputDialog.getText(self, "New Project", "Project name:")
        if ok and name:
            description, ok = QInputDialog.getText(self, "New Project", "Description (optional):")
            if ok:
                try:
                    self.project_manager.create_project(name, description)
                    self._update_ui()
                    self.project_created.emit(name)
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to create project: {e}")

    def _open_project(self):
        """Open an existing project."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Project", str(self.project_manager.projects_dir),
            "Auto Latent Video Projects (*.alvp);;All Files (*)"
        )
        if file_path:
            try:
                self.project_manager.load_project(file_path)
                self._update_ui()
                self.project_loaded.emit(file_path)
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to load project: {e}")

    def _save_project(self):
        """Save the current project."""
        if self.project_manager.current_project:
            try:
                self.project_manager.save_project()
                self._update_ui()
                if self.project_manager.current_project_path:
                    self.project_saved.emit(str(self.project_manager.current_project_path))
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save project: {e}")
        else:
            QMessageBox.information(self, "Info", "No project to save")

    def _create_version(self):
        """Create a new version of the current project."""
        if not self.project_manager.current_project:
            QMessageBox.information(self, "Info", "No project loaded")
            return

        description, ok = QInputDialog.getText(self, "Create Version", "Version description:")
        if ok:
            try:
                if hasattr(self.project_manager, 'version_control') and self.project_manager.version_control:
                    version_id = self.project_manager.version_control.create_version(description)
                    QMessageBox.information(self, "Success", f"Version created: {version_id}")
                else:
                    QMessageBox.warning(self, "Warning", "Version control not available for this project")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to create version: {e}")

    def _view_versions(self):
        """View project versions."""
        if not self.project_manager.current_project:
            QMessageBox.information(self, "Info", "No project loaded")
            return

        if not hasattr(self.project_manager, 'version_control') or not self.project_manager.version_control:
            QMessageBox.warning(self, "Warning", "Version control not available for this project")
            return

        # Create versions dialog
        dialog = ProjectVersionsDialog(self.project_manager, self)
        dialog.show()

    def _open_recent_project(self, item):
        """Open a recent project."""
        project_path = item.data(Qt.ItemDataRole.UserRole)
        try:
            self.project_manager.load_project(project_path)
            self._update_ui()
            self.project_loaded.emit(project_path)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load project: {e}")

    def _toggle_auto_save(self, enabled):
        """Toggle auto-save functionality."""
        if hasattr(self.project_manager, 'auto_save_manager'):
            if enabled:
                self.project_manager.auto_save_manager.enable(True)
                self.auto_save_status.setText("Auto-save: Enabled")
            else:
                self.project_manager.auto_save_manager.enable(False)
                self.auto_save_status.setText("Auto-save: Disabled")

    def _on_project_loaded(self, project_path):
        """Handle project loaded signal."""
        self._update_ui()

    def _on_project_saved(self, project_path):
        """Handle project saved signal."""
        self._update_ui()

    def _on_project_created(self, project_name):
        """Handle project created signal."""
        self._update_ui()

    def _on_status_changed(self, status):
        """Handle status change signal."""
        self._update_ui()


class ProjectVersionsDialog(QWidget):
    """Dialog for viewing and managing project versions."""

    def __init__(self, project_manager: ProjectManager, parent=None):
        super().__init__(parent)
        self.project_manager = project_manager
        self.setWindowTitle("Project Versions")
        self.setGeometry(200, 200, 700, 500)
        self._setup_ui()
        self._load_versions()

    def _setup_ui(self):
        """Setup the versions dialog UI."""
        layout = QVBoxLayout(self)

        # Info label
        info_label = QLabel("Project Version History")
        info_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(info_label)

        # Versions table
        self.versions_table = QTableWidget()
        self.versions_table.setColumnCount(4)
        self.versions_table.setHorizontalHeaderLabels([
            "Version", "Date", "Description", "Size"
        ])
        self.versions_table.horizontalHeader().setStretchLastSection(True)
        self.versions_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        layout.addWidget(self.versions_table)

        # Buttons
        button_layout = QHBoxLayout()

        restore_btn = QPushButton("Restore Selected")
        restore_btn.clicked.connect(self._restore_version)
        button_layout.addWidget(restore_btn)

        delete_btn = QPushButton("Delete Selected")
        delete_btn.clicked.connect(self._delete_version)
        button_layout.addWidget(delete_btn)

        button_layout.addStretch()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self._load_versions)
        button_layout.addWidget(refresh_btn)

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def _load_versions(self):
        """Load versions into the table."""
        if not hasattr(self.project_manager, 'version_control') or not self.project_manager.version_control:
            self.versions_table.setRowCount(0)
            return

        try:
            versions = self.project_manager.version_control.list_versions()
            self.versions_table.setRowCount(len(versions))

            for row, version_info in enumerate(versions):
                # Version ID
                version_item = QTableWidgetItem(version_info.get('id', 'Unknown'))
                version_item.setData(Qt.ItemDataRole.UserRole, version_info.get('id'))
                self.versions_table.setItem(row, 0, version_item)

                # Date
                timestamp = version_info.get('timestamp', '')
                if timestamp:
                    try:
                        if isinstance(timestamp, str):
                            dt = datetime.fromisoformat(timestamp)
                        else:
                            dt = timestamp
                        date_str = dt.strftime('%Y-%m-%d %H:%M')
                    except (ValueError, AttributeError):
                        date_str = str(timestamp)
                else:
                    date_str = 'Unknown'
                self.versions_table.setItem(row, 1, QTableWidgetItem(date_str))

                # Description
                description = version_info.get('description', 'No description')
                self.versions_table.setItem(row, 2, QTableWidgetItem(description))

                # Size (if available)
                size = version_info.get('size', 0)
                if size > 0:
                    size_mb = size / (1024 * 1024)
                    size_str = f"{size_mb:.2f} MB"
                else:
                    size_str = "Unknown"
                self.versions_table.setItem(row, 3, QTableWidgetItem(size_str))

        except Exception as e:
            logging.error(f"Failed to load versions: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load versions: {e}")

    def _restore_version(self):
        """Restore the selected version."""
        current_row = self.versions_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "Info", "Please select a version to restore")
            return

        version_id = self.versions_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
        version_display = self.versions_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "Confirm Restore",
            f"Are you sure you want to restore version {version_display}?\n"
            "This will replace the current project state.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                if self.project_manager.version_control.restore_version(version_id):
                    QMessageBox.information(self, "Success", f"Version {version_display} restored successfully")
                    self.close()
                else:
                    QMessageBox.critical(self, "Error", "Failed to restore version")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to restore version: {e}")

    def _delete_version(self):
        """Delete the selected version."""
        current_row = self.versions_table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "Info", "Please select a version to delete")
            return

        version_id = self.versions_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
        version_display = self.versions_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete version {version_display}?\n"
            "This action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                if hasattr(self.project_manager.version_control, 'delete_version'):
                    if self.project_manager.version_control.delete_version(version_id):
                        QMessageBox.information(self, "Success", f"Version {version_display} deleted successfully")
                        self._load_versions()  # Refresh the list
                    else:
                        QMessageBox.critical(self, "Error", "Failed to delete version")
                else:
                    QMessageBox.information(self, "Info", "Delete version functionality not available")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to delete version: {e}")
