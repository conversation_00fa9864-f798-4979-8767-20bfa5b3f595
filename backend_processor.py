import json
import logging
import os
import requests
import subprocess
import sys
import tempfile
from typing import Dict, Any, List, <PERSON>ple
import copy
import random
from pathlib import Path
import time  # For retry delays

# Import enhanced error recovery system
from error_recovery import (
    with_retry,
    with_circuit_breaker,
    with_error_recovery,
    ErrorSeverity,
    RetryConfig
)

# Try to import zp-c-gen components, handle gracefully if not available
try:
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'zp-c-gen'))
    from app.utils.script_parser import ScriptParser
    from app.services.zonos_client import ZonosClient
    from app.services.voice_manager import voice_manager
    ZP_C_GEN_AVAILABLE = True
except ImportError as e:
    logging.warning(f"zp-c-gen components not available: {e}. TTS functionality will be limited.")
    # Create mock classes for testing
    class ScriptParser:
        @staticmethod
        def parse_script(script_text):
            return []

    class ZonosClient:
        def __init__(self, api_key):
            self.api_key = api_key

        def generate_speech(self, text, **kwargs):
            raise NotImplementedError("ZonosClient not available")

    class MockVoiceManager:
        @staticmethod
        def get_voice_for_speaker(speaker_name):
            return {'voice_id': None, 'voice_name': None}

    voice_manager = MockVoiceManager()
    ZP_C_GEN_AVAILABLE = False
from pydub import AudioSegment
import math

# Import centralized configuration
from config_manager import get_config

# Get configuration instance
config = get_config()

# Node IDs from configuration
VIDEO_NODE_ID = config.workflow.video_node_id
AUDIO_NODE_ID = config.workflow.audio_node_id
OUTPUT_NODE_ID = config.workflow.output_node_id
LATENTSYNC_NODE_ID = config.workflow.latentsync_node_id
VIDEOCOMBINE_NODE_ID = config.workflow.videocombine_node_id
VIDEOLENGTH_NODE_ID = config.workflow.videolength_node_id

# Setup error recovery fallback handlers
from error_recovery import get_error_recovery_manager

def _setup_error_recovery_fallbacks():
    """Setup fallback handlers for error recovery."""
    recovery_manager = get_error_recovery_manager()

    # Fallback for workflow loading - try alternative workflow files
    def workflow_load_fallback(workflow_path: str) -> Dict[str, Any] | None:
        """Fallback workflow loader that tries alternative files."""
        logging.warning(f"Primary workflow {workflow_path} failed, trying fallbacks...")

        # Try backup workflow files
        fallback_paths = [
            workflow_path.replace('.json', '_backup.json'),
            workflow_path.replace('.json', '_default.json'),
            'workflows/default_workflow.json'
        ]

        for fallback_path in fallback_paths:
            if os.path.exists(fallback_path):
                try:
                    with open(fallback_path, 'r', encoding='utf-8') as f:
                        workflow = json.load(f)
                    logging.info(f"Successfully loaded fallback workflow: {fallback_path}")
                    return workflow
                except Exception as e:
                    logging.warning(f"Fallback workflow {fallback_path} also failed: {e}")
                    continue

        # Return minimal workflow as last resort
        logging.warning("All workflow fallbacks failed, returning minimal workflow")
        return {
            "1": {"class_type": "VHS_LoadVideo", "inputs": {"video": ""}},
            "2": {"class_type": "AudioLoader", "inputs": {"audio": ""}},
            "3": {"class_type": "LatentSync", "inputs": {"seed": 0, "lips_expression": 1.0}},
            "4": {"class_type": "VideoCombine", "inputs": {"crf": 19}}
        }

    # Fallback for TTS generation - use local TTS or skip
    def tts_pipeline_fallback(script_text: str, config: Dict[str, Any], use_local_tts: bool = False) -> List[str]:
        """Fallback TTS pipeline that tries local generation or returns empty list."""
        logging.warning("Primary TTS pipeline failed, trying fallback...")

        # Try to use local TTS if available
        if not use_local_tts:
            logging.info("Attempting fallback to local TTS...")
            try:
                return run_tts_pipeline(script_text, config, use_local_tts=True)
            except Exception as e:
                logging.warning(f"Local TTS fallback also failed: {e}")

        # Return empty list as final fallback
        logging.warning("All TTS fallbacks failed, returning empty audio list")
        return []

    # Register fallback handlers
    recovery_manager.register_fallback_handler("file_load", workflow_load_fallback)
    recovery_manager.register_fallback_handler("tts_pipeline", tts_pipeline_fallback)

# Initialize error recovery fallbacks
_setup_error_recovery_fallbacks()

# --- Workflow Functions ---

@with_error_recovery("file_load", ErrorSeverity.MEDIUM)
def load_workflow(workflow_path: str) -> Dict[str, Any] | None:
    """Loads a ComfyUI workflow from a JSON file."""
    try:
        # Specify encoding
        with open(workflow_path, 'r', encoding='utf-8') as f:
            workflow = json.load(f)
        logging.info("Workflow loaded successfully from %s", workflow_path)
        return workflow
    except FileNotFoundError:
        logging.error("Workflow file not found at %s", workflow_path)
        raise  # Let error recovery handle this
    except json.JSONDecodeError:
        logging.error("Error decoding JSON from workflow file: %s", workflow_path)
        raise  # Let error recovery handle this
    except OSError as e: # Catch OS errors like permission denied
        logging.error("OS error reading workflow file %s: %s", workflow_path, e)
        raise  # Let error recovery handle this

def update_workflow(base_workflow_data: Dict[str, Any], video_path: str, audio_path: str, config: Dict[str, Any]) -> Dict[str, Any] | None:
    """Creates a modified copy of the workflow with updated inputs and parameters."""
    try:
        # Create a deep copy to avoid modifying the original base workflow
        workflow_data = copy.deepcopy(base_workflow_data)

        # --- Convert paths to absolute paths ---
        abs_video_path = os.path.abspath(video_path)
        abs_audio_path = os.path.abspath(audio_path)
        logging.debug(f"Converted video path to absolute: {abs_video_path}")
        logging.debug(f"Converted audio path to absolute: {abs_audio_path}")

        # --- Update File Inputs ---
        if VIDEO_NODE_ID in workflow_data and "inputs" in workflow_data[VIDEO_NODE_ID]:
            workflow_data[VIDEO_NODE_ID]["inputs"]["video"] = abs_video_path # Use absolute path
            logging.info("Updated video input for node %s to path: %s", VIDEO_NODE_ID, abs_video_path)
        else:
            logging.warning("Video node ID %s or its 'inputs' not found in workflow.", VIDEO_NODE_ID)
            # Optionally return None or raise an error if required nodes are missing

        if AUDIO_NODE_ID in workflow_data and "inputs" in workflow_data[AUDIO_NODE_ID]:
            workflow_data[AUDIO_NODE_ID]["inputs"]["audio"] = abs_audio_path # Use absolute path
            logging.info("Updated audio input for node %s to path: %s", AUDIO_NODE_ID, abs_audio_path)
        else:
            logging.warning("Audio node ID %s or its 'inputs' not found in workflow.", AUDIO_NODE_ID)
            # Optionally return None or raise an error

        # --- Update Workflow Parameters from Config ---
        # Node 3: LatentSync Parameters
        if LATENTSYNC_NODE_ID in workflow_data and "inputs" in workflow_data[LATENTSYNC_NODE_ID]:
            inputs = workflow_data[LATENTSYNC_NODE_ID]["inputs"]
            if 'seed' in config:
                inputs["seed"] = config['seed']
            if 'lips_expression' in config:
                inputs["lips_expression"] = config['lips_expression']
            if 'inference_steps' in config:
                inputs["inference_steps"] = config['inference_steps']
            logging.debug(f"Updated params for node {LATENTSYNC_NODE_ID}")
        else:
            logging.warning(f"LatentSync node ID {LATENTSYNC_NODE_ID} or its 'inputs' not found.")

        # Node 5: Video Combine Parameters
        if VIDEOCOMBINE_NODE_ID in workflow_data and "inputs" in workflow_data[VIDEOCOMBINE_NODE_ID]:
            inputs = workflow_data[VIDEOCOMBINE_NODE_ID]["inputs"]
            if 'crf' in config:
                inputs["crf"] = config['crf']
            logging.debug(f"Updated params for node {VIDEOCOMBINE_NODE_ID}")
        else:
            logging.warning(f"Video Combine node ID {VIDEOCOMBINE_NODE_ID} or its 'inputs' not found.")

        # Node 2: Video Length Adjuster Parameters
        if VIDEOLENGTH_NODE_ID in workflow_data and "inputs" in workflow_data[VIDEOLENGTH_NODE_ID]:
            inputs = workflow_data[VIDEOLENGTH_NODE_ID]["inputs"]
            if 'silent_padding_sec' in config:
                inputs["silent_padding_sec"] = config['silent_padding_sec']
            logging.debug(f"Updated params for node {VIDEOLENGTH_NODE_ID}")
        else:
            logging.warning(f"Video Length node ID {VIDEOLENGTH_NODE_ID} or its 'inputs' not found.")

        logging.debug("Workflow update complete.")
        return workflow_data

    except KeyError as e:
        logging.error("Missing expected key during workflow update: %s", e)
        return None
    except Exception as e: # Catch unexpected errors
        logging.error("Unexpected error updating workflow: %s", e)
        return None

def update_framepack_workflow(base_workflow_data: Dict[str, Any], image_path: str, config: Dict[str, Any]) -> Dict[str, Any] | None:
    """Creates a modified copy of the framepack workflow with updated image input."""
    try:
        workflow_data = copy.deepcopy(base_workflow_data)
        abs_image_path = os.path.abspath(image_path)
        for node_id, node in workflow_data.items():
            if node.get('class_type') == 'LoadImage' and 'inputs' in node:
                node['inputs']['image'] = abs_image_path
                logging.info('Updated image input for node %s to path: %s', node_id, abs_image_path)
                break

        # Override FramePack prompt if provided
        prompt = config.get('framepack_prompt')
        if prompt is not None:
            for node_id, node in workflow_data.items():
                if node.get('class_type') == 'StringConstantMultiline' and 'inputs' in node:
                    node['inputs']['string'] = prompt
                    logging.info('Updated FramePack prompt for node %s to: %s', node_id, prompt)

        # Override or randomize FramePack seed
        if config.get('framepack_random'):
            seed_val = random.randint(0, 2**32 - 1)
        else:
            seed_val = config.get('framepack_seed', None)
        if seed_val is not None:
            for node_id, node in workflow_data.items():
                if node.get('class_type') == 'CR Seed' and 'inputs' in node:
                    node['inputs']['seed'] = seed_val
                    logging.info('Updated FramePack seed for node %s to: %s', node_id, seed_val)
        return workflow_data
    except Exception as e:
        logging.error('Error updating framepack workflow: %s', e)
        return None

# --- ComfyUI Interaction Functions ---

@with_error_recovery("comfyui_submit", ErrorSeverity.HIGH)
@with_circuit_breaker("comfyui")
def submit_workflow(comfyui_url: str, node_definitions: Dict[str, Any]) -> str | None:
    """Submits the workflow (node definitions) to the ComfyUI API and returns the prompt ID."""
    submit_url = f"http://{comfyui_url}/prompt"
    # Wrap the node definitions in the required {"prompt": ...} structure for the API
    payload = json.dumps({"prompt": node_definitions}) # Ensure payload matches API spec

    logging.debug("Submitting payload to %s: %s", submit_url, payload[:500] + ("..." if len(payload) > 500 else "")) # Log truncated payload

    try:
        response = requests.post(submit_url, data=payload, headers={'Content-Type': 'application/json'}, timeout=30)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        result_data = response.json()
        prompt_id = result_data.get('prompt_id')
        if prompt_id:
            logging.info("Workflow submitted successfully. Prompt ID: %s", prompt_id)
            return prompt_id
        else:
            logging.error("ComfyUI API did not return a prompt_id. Response: %s", result_data)
            return None
    except requests.exceptions.RequestException as e:
        logging.error("HTTP request failed when submitting workflow: %s", e)
        raise  # Let error recovery handle this
    except json.JSONDecodeError:
        logging.error("Failed to decode JSON response from ComfyUI submit API. Response text: %s", response.text)
        raise  # Let error recovery handle this
    except Exception as e: # Catch unexpected errors
        logging.error("Unexpected error submitting workflow: %s", e)
        raise  # Let error recovery handle this

# --- Helper functions for polling --- (Consider refactoring if they grow complex)

@with_retry("network", RetryConfig(max_attempts=3, base_delay=0.5))
def get_comfyui_status(url: str) -> Dict | None:
    """Fetches status data (history or queue) from a ComfyUI endpoint."""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logging.warning("Error fetching status from %s: %s", url, e)
        raise  # Let retry mechanism handle this
    except json.JSONDecodeError as e:
        logging.error("Error decoding JSON from %s: %s", url, e)
        raise  # Let retry mechanism handle this
    except Exception as e:
        logging.error("Unexpected error fetching status from %s: %s", url, e)
        raise  # Let retry mechanism handle this

def poll_comfyui_status(comfyui_url, prompt_id, poll_interval=5, timeout_secs=600):
    """Polls ComfyUI history until the workflow for prompt_id completes or times out."""
    import time # Explicit import within function scope
    start_time = time.time()
    history_url = f"http://{comfyui_url}/history/{prompt_id}"
    queue_url = f"http://{comfyui_url}/queue"

    logging.info("Polling status for prompt ID: %s...", prompt_id)

    while time.time() - start_time < timeout_secs:
        history = get_comfyui_status(history_url)

        if history is not None and prompt_id in history:
            status_info = history[prompt_id]
            outputs = status_info.get('outputs', {})
            output_files_found = []

            # Iterate through all nodes in the output section
            # Use _ for node_id as it's unused
            for _, node_output_data in outputs.items(): 
                # Check common keys where file lists might appear
                for key in ['gifs', 'videos', 'images']:
                    if key in node_output_data and isinstance(node_output_data[key], list):
                        for item in node_output_data[key]:
                            if isinstance(item, dict) and 'filename' in item:
                                output_files_found.append(item['filename'])
                        # Stop checking keys for this node if files found
                        if output_files_found:
                            break
                # Stop checking nodes if files found
                if output_files_found:
                    break

            # Check if we found any output files
            if output_files_found:
                logging.info("Prompt %s completed successfully. Output files found: %s", prompt_id, output_files_found)
                return (True, output_files_found)
            else:
                # If the prompt ID is in history but no expected output files were found,
                # check the queue status to see if it's truly done.
                queue_data = get_comfyui_status(queue_url)
                if queue_data is not None:
                    is_running = any(item[1] == prompt_id for item in queue_data.get('queue_running', []))
                    is_pending = any(item[1] == prompt_id for item in queue_data.get('queue_pending', []))

                    if not is_running and not is_pending:
                        # Not running, not pending, and prompt is in history but without expected output files
                        # = finished unsuccessfully or unexpectedly
                        logging.warning("Prompt %s is finished but no output files were found in expected format.", prompt_id)
                        logging.debug("Full history entry for %s: %s", prompt_id, status_info) # Log details
                        return (True, []) # Return True for completion, but empty list for files
                    else:
                        # Still running or pending, keep polling
                        logging.debug("Prompt %s found in history, but still running or output not yet found. Output keys: %s", prompt_id, list(outputs.keys()))
                else:
                    logging.warning("Could not get queue status while checking if prompt %s is truly finished.", prompt_id)
                    # Continue polling, assuming it might still be running

        # If prompt ID not yet in history, check the queue
        elif history is not None:
            queue_data = get_comfyui_status(queue_url)
            if queue_data is not None:
                is_running = any(item[1] == prompt_id for item in queue_data.get('queue_running', []))
                is_pending = any(item[1] == prompt_id for item in queue_data.get('queue_pending', []))
                if not is_running and not is_pending:
                    # Not in history, not running, not pending -> Likely failed very early or invalid ID
                    logging.error("Prompt %s not found in history and not in queue. Assuming failure.", prompt_id)
                    return (False, None)
                else:
                    logging.debug("Prompt %s not yet in history. Queue status: running=%s, pending=%s", prompt_id, is_running, is_pending)
            else:
                logging.warning("Could not get queue status while checking if prompt %s exists.", prompt_id)
                # Continue polling

        # If history fetch failed, get_comfyui_status would have returned None and logged a warning
        # We just wait and retry

        time.sleep(poll_interval)  # Wait before next poll

    logging.error("Timeout waiting for prompt %s to complete.", prompt_id)
    return (False, None)

# --- FFmpeg Function ---

def run_ffmpeg_concat(input_files: List[str], output_path: str) -> bool:
    """Uses ffmpeg to concatenate multiple video files found in ComfyUI output."""
    if not input_files:
        logging.warning("No input files provided for FFmpeg concatenation.")
        return False

    # Create a temporary file list for ffmpeg
    list_filename = "ffmpeg_concat_list.txt"
    try:
        # Specify encoding
        with open(list_filename, 'w', encoding='utf-8') as f:
            for file_path in input_files:
                # FFmpeg requires forward slashes or escaped backslashes
                safe_path = file_path.replace('\\', '/')
                f.write(f"file '{safe_path}'\n")
        logging.debug("Created ffmpeg input list: %s with %d files", list_filename, len(input_files))

    except OSError as e:
        logging.error("Failed to create ffmpeg input list file '%s': %s", list_filename, e)
        return False

    # Construct the ffmpeg command
    command = [
        'ffmpeg',
        '-f', 'concat',
        '-safe', '0',      # Allows absolute paths in the list file
        '-i', list_filename,
        '-c', 'copy',       # Copy streams without re-encoding (faster, lossless)
        '-y',              # Overwrite output file if it exists
        output_path
    ]

    logging.info("Running FFmpeg command: %s", " ".join(command))

    try:
        # Ensure output directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir: # Only create if path includes a directory
            os.makedirs(output_dir, exist_ok=True)
            logging.debug("Ensured output directory exists: %s", output_dir)

        startupinfo = None
        if sys.platform == "win32":
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

        # Changed from result to proc_result to avoid Pylint warning
        proc_result = subprocess.run(command, capture_output=True, text=True, check=True, startupinfo=startupinfo)
        logging.info("FFmpeg completed successfully.")
        logging.debug("FFmpeg stdout: %s", proc_result.stdout)
        logging.debug("FFmpeg stderr: %s", proc_result.stderr)
        return True

    except FileNotFoundError:
        logging.error("ffmpeg command not found. Make sure FFmpeg is installed and in your system's PATH.")
        return False
    except subprocess.CalledProcessError as e:
        logging.error("FFmpeg command failed with exit code %d.", e.returncode)
        logging.error("FFmpeg stderr: %s", e.stderr)
        logging.error("FFmpeg stdout: %s", e.stdout)
        return False
    except Exception as e: # Catch unexpected errors
        logging.error("An unexpected error occurred running FFmpeg: %s", e)
        return False
    finally:
        # Clean up the temporary list file
        if os.path.exists(list_filename):
            try:
                os.remove(list_filename)
                logging.debug("Removed temporary ffmpeg list file: %s", list_filename)
            except OSError as e:
                logging.warning("Failed to remove temporary ffmpeg list file '%s': %s", list_filename, e)

# --- Script Generation via LLM ---
def generate_script(provider: str, api_url: str, api_key: str, model: str,
                   num_speakers: int, speaker_names: List[str], subject: str) -> str:
    """
    Generate a podcast script using the specified LLM provider.
    """
    logging.info("Generating script via %s", provider)
    payload = {
        "model": model,
        "num_speakers": num_speakers,
        "speaker_names": speaker_names,
        "subject": subject
    }
    try:
        if provider == "Ollama":
            response = requests.post(f"{api_url}/generate_script", json=payload)
        else:  # OpenRouter
            headers = {"Authorization": f"Bearer {api_key}"}
            response = requests.post(f"{api_url}/generate_script", headers=headers, json=payload)
        response.raise_for_status()
        return response.json().get("script", "")
    except Exception as e:
        logging.error("Script generation failed: %s", e)
        return ""

# --- TTS Pipeline ---
@with_error_recovery("tts_pipeline", ErrorSeverity.MEDIUM)
@with_circuit_breaker("tts")
def run_tts_pipeline(script_text: str, config: Dict[str, Any], use_local_tts: bool = False) -> List[str]:
    """
    Parse the podcast script into segments and generate speech WAVs via ZonosClient.
    """
    logging.info("TTS pipeline started")
    logging.debug(f"RAW SCRIPT TEXT (first 200 chars): {script_text[:200]}")

    # Check if zp-c-gen is available
    if not ZP_C_GEN_AVAILABLE:
        logging.warning("zp-c-gen not available. TTS pipeline cannot proceed.")
        return []

    output_dir = config.get('tts_output_dir') or tempfile.mkdtemp(prefix="tts_output_")
    os.makedirs(output_dir, exist_ok=True)
    speaker_audio_files_map = config.get('speaker_audio_files_map', {})

    try:
        segments = ScriptParser.parse_script(script_text)
    except Exception as e:
        logging.error(f"Script parsing failed: {e}")
        return []

    if not config.get('zonos_api_key'):
        logging.warning("No Zonos API key provided. TTS pipeline cannot proceed.")
        return []

    try:
        client = ZonosClient(api_key=config.get('zonos_api_key'))
    except Exception as e:
        logging.error(f"Failed to initialize ZonosClient: {e}")
        return []

    wav_paths: List[str] = []
    for idx, segment in enumerate(segments):
        logging.debug(f"Backend Processor TTS - Segment {idx} data: {segment}")
        speaker = segment.get('speaker', f"seg{idx}")
        text = segment.get('text', '')
        speaking_rate = segment.get('speaking_rate', None)
        pause = segment.get('pause', 0.0)
        emotion = segment.get('emotion', None)
        pitch_std = segment.get('pitch_std', None)  # Extract pitch_std
        # Always provide a full emotion dict for the TTS API
        if not emotion or not isinstance(emotion, dict) or not emotion:
            # Use ScriptParser's default for neutral
            emotion = ScriptParser._create_emotion_weights('neutral', 0.6)
        # Wrap emotion dict in EmotionWeights for API compatibility
        try:
            from app.services.zonos_client import EmotionWeights
            emotion = EmotionWeights(**emotion)
        except ImportError:
            # If EmotionWeights not available, use dict directly
            pass

        if use_local_tts:
            speaker_audio = speaker_audio_files_map.get(speaker)
            if not speaker_audio or not os.path.exists(speaker_audio):
                logging.warning(f"Local TTS: Speaker audio file not found or invalid for speaker '{speaker}': {speaker_audio}. Ensure a valid WAV file is selected in the GUI.")
                # If no valid local speaker audio, the ZonosClient's _generate_speech_local will raise an error, which is caught below.
                # We proceed with speaker_audio as potentially None or invalid to let the client handle it.
        else:
            # Existing logic for API-based TTS
            voice_id = voice_manager.get_speaker_voice(speaker)
            speaker_audio = voice_manager.get_base64_voice_sample(voice_id) if voice_id and voice_id.startswith('custom_') else None
        
        output_format = config.get('output_format', 'wav').lower()
        # Ensure the extension is one of the known/supported types, default to .wav
        if output_format not in ['mp3', 'wav']:
            logging.warning(f"Unsupported output format '{output_format}' requested. Defaulting to 'wav'.")
            output_format = 'wav'
        file_extension = f".{output_format}"
        
        # Construct the output path with the correct extension
        actual_output_path = os.path.join(output_dir, f"{idx}_{speaker}{file_extension}")
        num_retries = 3  # number of retries
        success = False
        for attempt in range(num_retries + 1):
            try:
                # For API TTS, include speaker voice_id if available
                if not use_local_tts and voice_id:
                    tts_text = {'text': text, 'voice_id': voice_id}
                else:
                    tts_text = text
                logging.debug(f"Backend Processor TTS - Before call: speaker='{speaker}', text='{text[:30]}...', speaking_rate={speaking_rate}, emotion={emotion}, pitch_std_to_pass={pitch_std} (type: {type(pitch_std)}), use_local_tts={use_local_tts}")
                audio_content_or_path = client.generate_speech(
                    use_local_tts=use_local_tts,
                    text=tts_text,
                    speaking_rate=speaking_rate,
                    emotion=emotion,
                    speaker_audio=speaker_audio,
                    output_path=actual_output_path, # Pass the path with correct extension
                    pitchStd=pitch_std,  # Pass pitch_std
                    mime_type=output_format # Pass the determined output format
                )
                wav_paths.append(actual_output_path) # Add the path with correct extension
                success = True
                break
            except Exception as e:
                if attempt < num_retries:
                    wait = 5 * (attempt + 1)
                    logging.warning("Attempt %d for segment %s failed: %s. Retrying in %d seconds...", attempt+1, idx, e, wait)
                    time.sleep(wait)
                else:
                    logging.error("Failed to generate speech for segment %s after %d attempts: %s", idx, num_retries + 1, e)
        if not success:
            continue
        if pause and pause > 0:
            silence_path = os.path.join(output_dir, f"{idx}_{speaker}_pause.wav")
            silence = AudioSegment.silent(duration=int(pause * 1000))
            silence.export(silence_path, format="wav")
            wav_paths.append(silence_path)
    return wav_paths

# --- Auto Pair Audio Images ---
IMAGE_EXTENSIONS = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp', '.tiff')
def auto_pair_audio_images(wav_paths: List[str], image_folder: Path) -> List[Tuple[str, str]]:
    """
    Shuffle images in image_folder and pair with wav_paths.
    """
    logging.info("Auto-pair audio/images started")
    images = sorted([str(p) for p in image_folder.iterdir() if p.suffix.lower() in IMAGE_EXTENSIONS])
    if not images:
        logging.warning("No images found in %s", image_folder)
        return []
    random.shuffle(images)
    if len(images) < len(wav_paths):
        repeat = math.ceil(len(wav_paths) / len(images))
        images = images * repeat
    paired = list(zip(images[:len(wav_paths)], wav_paths))
    logging.info("Paired %d images with %d audio segments", len(paired), len(wav_paths))
    return paired

# --- Example Usage (for testing) ---
if __name__ == '__main__':
    # This block allows testing the functions independently
    logging.basicConfig(level=logging.DEBUG,
                        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')

    # Configuration - Replace with your actual values
    API_URL = "127.0.0.1:8188" # Example ComfyUI URL
    WORKFLOW_FILE = "path/to/your/workflow_api.json" # Path to your workflow file
    VIDEO_FILE = "path/to/your/input.mp4" # Path to your video file
    AUDIO_FILE = "path/to/your/input.wav" # Path to your audio file
    OUTPUT_VIDEO = "output/final_video.mp4"
    # FIXME: This needs to be configured correctly!
    COMFYUI_OUTPUT_DIR = "/path/to/ComfyUI/output" # Base output dir for ComfyUI

    # 1. Load the base workflow (node definitions)
    node_defs = load_workflow(WORKFLOW_FILE)
    if not node_defs:
        sys.exit(1)

    # 2. Update the workflow with specific file paths
    config = {
        'seed': 42,
        'lips_expression': 'smile',
        'inference_steps': 100,
        'crf': 18,
        'silent_padding_sec': 2
    }
    updated_node_defs = update_workflow(node_defs, VIDEO_FILE, AUDIO_FILE, config)
    if not updated_node_defs:
        sys.exit(1)

    # 3. Submit the workflow (updated node definitions)
    prompt_id_val = submit_workflow(API_URL, updated_node_defs)
    if not prompt_id_val:
        sys.exit(1)

    # 4. Poll for completion
    success, output_filenames = poll_comfyui_status(API_URL, prompt_id_val)

    # 5. Concatenate if successful
    if success and output_filenames:
        # Construct full paths to the output files from ComfyUI
        full_output_paths = [os.path.join(COMFYUI_OUTPUT_DIR, fname) for fname in output_filenames]
        missing_files = [p for p in full_output_paths if not os.path.exists(p)]
        if missing_files:
            logging.error("Could not find the following expected output files from ComfyUI: %s", missing_files)
            sys.exit(1)

        concat_success = run_ffmpeg_concat(full_output_paths, OUTPUT_VIDEO)
        if concat_success:
            logging.info("Successfully processed and saved final video to %s", OUTPUT_VIDEO)
        else:
            logging.error("FFmpeg concatenation failed.")
            sys.exit(1)
    elif success:
        logging.warning("ComfyUI processing finished, but no output files were detected.")
    else:
        logging.error("ComfyUI processing failed or timed out.")
        sys.exit(1)
