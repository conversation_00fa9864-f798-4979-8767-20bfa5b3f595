<?xml version="1.0"?>
<!--
    Tree-based 20x20 left eye detector.
    The detector is trained by 6665 positive samples from FERET, VALID and BioID face databases. 
    Created by <PERSON><PERSON> (http://yushiqi.cn/research/eyedetection).

////////////////////////////////////////////////////////////////////////////////////////

  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<cascade type_id="opencv-cascade-classifier"><stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>20</height>
  <width>20</width>
  <stageParams>
    <maxWeakCount>33</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount></featureParams>
  <stageNum>20</stageNum>
  <stages>
    <_>
      <maxWeakCount>5</maxWeakCount>
      <stageThreshold>-2.3924100399017334e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 0 2.7325989678502083e-02 -1 -2 1 -7.0568458177149296e-03</internalNodes>
          <leafValues>
            -9.0600621700286865e-01 9.3385708332061768e-01
            -4.5859959721565247e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 2 -1.2538699805736542e-01 -1 -2 3
            -1.1487299948930740e-01</internalNodes>
          <leafValues>
            7.2463721036911011e-01 5.3034168481826782e-01
            -8.3221220970153809e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 4 -5.8309938758611679e-02 -1 -2 5
            -1.7684370279312134e-02</internalNodes>
          <leafValues>
            6.5408891439437866e-01 2.9482871294021606e-01
            -7.4809581041336060e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 6 3.5937170032411814e-03 -1 -2 7 -1.3436110457405448e-03</internalNodes>
          <leafValues>
            -5.0303918123245239e-01 6.5995341539382935e-01
            -5.5740857124328613e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 8 -2.1795940119773149e-03 -1 -2 9 1.1514870449900627e-02</internalNodes>
          <leafValues>
            -4.2016351222991943e-01 5.9694331884384155e-01
            -8.0508047342300415e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>7</maxWeakCount>
      <stageThreshold>-2.6498730182647705e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 10 -2.2485560178756714e-01 -1 -2 11
            -9.6008004620671272e-03</internalNodes>
          <leafValues>
            -8.1363201141357422e-01 9.0863138437271118e-01
            -3.2208970189094543e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 12 7.4219167232513428e-02 -1 -2 13
            -5.3165741264820099e-03</internalNodes>
          <leafValues>
            -7.5329452753067017e-01 8.6339497566223145e-01
            -3.3463571220636368e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 14 -2.1913449745625257e-03 -1 -2 15
            1.1800959706306458e-02</internalNodes>
          <leafValues>
            -5.5720347166061401e-01 -3.2359680533409119e-01
            6.4163821935653687e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 16 -7.6179709285497665e-03 -1 -2 17
            -9.0587511658668518e-03</internalNodes>
          <leafValues>
            -5.3167867660522461e-01 -7.3611450195312500e-01
            5.5660772323608398e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 18 -4.9959779717028141e-03 -1 -2 19
            8.0803930759429932e-03</internalNodes>
          <leafValues>
            -4.1476911306381226e-01 5.9278357028961182e-01
            -6.7384922504425049e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 20 1.9909010734409094e-03 -1 -2 21
            1.6845749923959374e-03</internalNodes>
          <leafValues>
            -4.2145928740501404e-01 5.4679220914840698e-01
            -7.5099450349807739e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 22 -5.0781872123479843e-03 -1 -2 23
            2.6645609177649021e-03</internalNodes>
          <leafValues>
            -3.9899548888206482e-01 5.8940601348876953e-01
            -4.6778041124343872e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-2.3828399181365967e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 24 -2.5301438570022583e-01 -1 -2 25
            2.9663778841495514e-03</internalNodes>
          <leafValues>
            -7.5402587652206421e-01 -3.5279649496078491e-01
            8.7992298603057861e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 26 -4.7127649188041687e-02 -1 -2 27
            1.9500750349834561e-03</internalNodes>
          <leafValues>
            -5.2234899997711182e-01 -3.0379909276962280e-01
            7.5204378366470337e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 28 -7.1481026709079742e-02 -1 -2 29
            2.2189730405807495e-01</internalNodes>
          <leafValues>
            6.5841901302337646e-01 -6.0907202959060669e-01
            5.6842160224914551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 30 3.3842820674180984e-02 -1 -2 31
            -5.1714561413973570e-04</internalNodes>
          <leafValues>
            -6.4311647415161133e-01 5.4620361328125000e-01
            -3.9984148740768433e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 32 -3.4458211157470942e-03 -1 -2 33
            2.4395729415118694e-03</internalNodes>
          <leafValues>
            -4.5636838674545288e-01 4.7798189520835876e-01
            -9.1247087717056274e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 34 2.1385070867836475e-03 -1 -2 35
            1.8324409611523151e-03</internalNodes>
          <leafValues>
            -8.3617758750915527e-01 3.3462798595428467e-01
            -7.5008547306060791e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 36 1.1167610064148903e-03 -1 -2 37
            9.9106997367925942e-05</internalNodes>
          <leafValues>
            -6.9083797931671143e-01 -3.4561330080032349e-01
            4.1183179616928101e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 38 1.5447770245373249e-02 -1 -2 39
            -3.2244939357042313e-02</internalNodes>
          <leafValues>
            3.6980190873146057e-01 6.1112838983535767e-01
            -5.5685341358184814e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-2.1312201023101807e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 40 -1.2251129746437073e-01 -1 -2 41
            -1.4230609871447086e-02</internalNodes>
          <leafValues>
            -6.7026627063751221e-01 8.7802392244338989e-01
            -1.8784180283546448e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 42 -5.9833219274878502e-03 -1 -2 43
            7.7085137367248535e-02</internalNodes>
          <leafValues>
            -5.8122849464416504e-01 -5.0395351648330688e-01
            6.7387360334396362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 44 -1.1086189746856689e-01 -1 -2 45
            9.4604760408401489e-02</internalNodes>
          <leafValues>
            6.3432037830352783e-01 -4.9726390838623047e-01
            3.8787439465522766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 46 1.7696130089461803e-04 -1 -2 47
            2.0120320841670036e-03</internalNodes>
          <leafValues>
            -6.3938802480697632e-01 -3.5313910245895386e-01
            5.1538437604904175e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 48 -1.6102839726954699e-03 -1 -2 49
            1.6666069859638810e-03</internalNodes>
          <leafValues>
            -5.1915901899337769e-01 4.0478190779685974e-01
            -6.9496357440948486e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 50 -7.1480998303741217e-04 -1 -2 51
            -4.7647571191191673e-03</internalNodes>
          <leafValues>
            -4.8945188522338867e-01 -5.0037759542465210e-01
            4.0796059370040894e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 52 7.8659597784280777e-03 -1 -2 53
            -1.2938310392200947e-03</internalNodes>
          <leafValues>
            -3.3636429905891418e-01 -6.7621380090713501e-01
            4.7010248899459839e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 54 -3.6533139063976705e-04 -1 -2 55
            2.0565679296851158e-03</internalNodes>
          <leafValues>
            -4.7071608901023865e-01 4.1323411464691162e-01
            -5.5526417493820190e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 56 7.8385717642959207e-05 -1 -2 57
            1.7511800397187471e-03</internalNodes>
          <leafValues>
            -5.1521158218383789e-01 3.3417248725891113e-01
            -7.9558157920837402e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-2.0176210403442383e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 58 -6.4695239067077637e-02 -1 -2 59
            9.5212170854210854e-03</internalNodes>
          <leafValues>
            -6.1326402425765991e-01 -5.4831558465957642e-01
            7.8652447462081909e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 60 -9.8109766840934753e-02 -1 -2 61
            -8.5938459634780884e-01</internalNodes>
          <leafValues>
            6.9113308191299438e-01 4.5364680886268616e-01
            -5.0026148557662964e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 62 -8.9836172759532928e-02 -1 -2 63
            2.6945930439978838e-03</internalNodes>
          <leafValues>
            -5.2928781509399414e-01 -3.8199779391288757e-01
            5.7821297645568848e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 64 2.5973599404096603e-03 -1 -2 65
            -3.0058110132813454e-03</internalNodes>
          <leafValues>
            -9.1928368806838989e-01 -8.0213797092437744e-01
            2.9259279370307922e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 66 -4.5496290549635887e-03 -1 -2 67
            4.7376728616654873e-03</internalNodes>
          <leafValues>
            -4.3678951263427734e-01 4.1010880470275879e-01
            -7.2692811489105225e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 68 4.6190437860786915e-03 -1 -2 69
            4.5377281494438648e-03</internalNodes>
          <leafValues>
            -8.4895151853561401e-01 3.0124679207801819e-01
            -7.0301771163940430e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 70 -2.4952790699899197e-03 -1 -2 71
            -5.1753767766058445e-03</internalNodes>
          <leafValues>
            -4.6784749627113342e-01 -7.4530351161956787e-01
            4.0011820197105408e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 72 -5.2049742080271244e-03 -1 -2 73
            -8.7892003357410431e-02</internalNodes>
          <leafValues>
            4.8669269680976868e-01 8.3493947982788086e-01
            -3.3827719092369080e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 74 6.9997250102460384e-03 -1 -2 75
            -9.0990252792835236e-03</internalNodes>
          <leafValues>
            -2.9039889574050903e-01 6.2315821647644043e-01
            -3.5424730181694031e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>11</maxWeakCount>
      <stageThreshold>-2.2212049961090088e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 76 -5.5702101439237595e-02 -1 -2 77
            3.4033291041851044e-02</internalNodes>
          <leafValues>
            -6.9841581583023071e-01 -3.9509189128875732e-01
            8.0313128232955933e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 78 -4.6199060976505280e-02 -1 -2 79
            -4.8061669804155827e-03</internalNodes>
          <leafValues>
            -4.8860380053520203e-01 8.0775612592697144e-01
            -7.4490822851657867e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 80 1.8170489929616451e-03 -1 -2 81
            -3.6162370815873146e-03</internalNodes>
          <leafValues>
            -3.8043528795242310e-01 6.0451722145080566e-01
            -2.2582240402698517e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 82 -1.5706950798630714e-02 -1 -2 83
            4.3929950334131718e-03</internalNodes>
          <leafValues>
            -3.7577998638153076e-01 5.4214221239089966e-01
            -3.7388241291046143e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 84 -1.0047219984699041e-04 -1 -2 85
            -8.6475118994712830e-02</internalNodes>
          <leafValues>
            -4.7433409094810486e-01 5.0186318159103394e-01
            -2.1136230230331421e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 86 -7.7960766851902008e-02 -1 -2 87
            9.8561286926269531e-02</internalNodes>
          <leafValues>
            5.7337349653244019e-01 -3.2515558600425720e-01
            5.3035980463027954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 88 -5.4359167814254761e-01 -1 -2 89
            -4.4177699834108353e-02</internalNodes>
          <leafValues>
            5.9464299678802490e-01 2.9671078920364380e-01
            -3.8474830985069275e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 90 -8.8016409426927567e-04 -1 -2 91
            2.6359390467405319e-03</internalNodes>
          <leafValues>
            -3.2000589370727539e-01 -1.7586140334606171e-01
            4.8360350728034973e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 92 -1.4203689992427826e-02 -1 -2 93
            -7.3902818257920444e-05</internalNodes>
          <leafValues>
            -7.7882087230682373e-01 3.0619418621063232e-01
            -3.3196049928665161e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 94 4.6157240867614746e-03 -1 -2 95
            1.1152310296893120e-02</internalNodes>
          <leafValues>
            4.9689778685569763e-01 -5.3435891866683960e-01
            9.7229443490505219e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 96 -6.0547702014446259e-03 -1 -2 97
            -2.1118740551173687e-03</internalNodes>
          <leafValues>
            -8.3811217546463013e-01 6.3617032766342163e-01
            -4.8299189656972885e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>13</maxWeakCount>
      <stageThreshold>-2.1328830718994141e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 98 -1.2956829741597176e-02 -1 -2 99
            -2.7141019701957703e-02</internalNodes>
          <leafValues>
            -6.4874732494354248e-01 7.6293057203292847e-01
            -3.3947870135307312e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 100 4.5119998976588249e-03 -1 -2 101
            1.2516690418124199e-02</internalNodes>
          <leafValues>
            -5.0059837102890015e-01 -3.6873328685760498e-01
            5.9888631105422974e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 102 -6.0557941906154156e-03 -1 -2 103
            -4.6923749148845673e-02</internalNodes>
          <leafValues>
            -3.8940930366516113e-01 6.3268911838531494e-01
            -2.6270028948783875e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 104 -2.4018269032239914e-03 -1 -2 105
            -1.5936089679598808e-02</internalNodes>
          <leafValues>
            -5.0517928600311279e-01 6.5526002645492554e-01
            -1.7308109998703003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 106 1.4000290073454380e-02 -1 -2 107
            1.3202779926359653e-02</internalNodes>
          <leafValues>
            -4.1653230786323547e-01 -4.9121969938278198e-01
            3.7397938966751099e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 108 -2.7658580802381039e-04 -1 -2 109
            -4.8634149134159088e-03</internalNodes>
          <leafValues>
            -4.5382869243621826e-01 -5.9796881675720215e-01
            3.1217721104621887e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 110 2.7654920704662800e-03 -1 -2 111
            2.5534769892692566e-01</internalNodes>
          <leafValues>
            -7.6476567983627319e-01 -3.4267220646142960e-02
            7.0786577463150024e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 112 4.6812961809337139e-03 -1 -2 113
            6.5162130631506443e-03</internalNodes>
          <leafValues>
            -7.8790861368179321e-01 1.8877579271793365e-01
            -7.9132258892059326e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 114 5.7325329631567001e-02 -1 -2 115
            -1.2718330137431622e-02</internalNodes>
          <leafValues>
            6.2349188327789307e-01 3.0860608816146851e-01
            -3.2784330844879150e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 116 -6.7374261561781168e-04 -1 -2 117
            5.6564649567008018e-03</internalNodes>
          <leafValues>
            -4.5451548695564270e-01 2.7431339025497437e-01
            -7.8447937965393066e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 118 3.1134090386331081e-03 -1 -2 119
            2.4249779526144266e-03</internalNodes>
          <leafValues>
            3.9738771319389343e-01 -3.5198271274566650e-01
            3.0490091443061829e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 120 -5.5641461163759232e-02 -1 -2 121
            4.3548129498958588e-02</internalNodes>
          <leafValues>
            4.5575490593910217e-01 -3.3370929956436157e-01
            2.9501429200172424e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 122 8.0783379962667823e-04 -1 -2 123
            1.8713270546868443e-03</internalNodes>
          <leafValues>
            2.2460040450096130e-01 -6.6048407554626465e-01
            1.5031670033931732e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>13</maxWeakCount>
      <stageThreshold>-1.9884539842605591e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 124 -4.3516629934310913e-01 -1 -2 125
            6.2595037743449211e-03</internalNodes>
          <leafValues>
            -4.9959290027618408e-01 -2.3639589548110962e-01
            7.9975378513336182e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 126 -6.6518150269985199e-03 -1 -2 127
            -5.7092090137302876e-03</internalNodes>
          <leafValues>
            -5.4752808809280396e-01 6.4273327589035034e-01
            -2.1511809527873993e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 128 1.9450180232524872e-02 -1 -2 129
            -5.4476498626172543e-03</internalNodes>
          <leafValues>
            -5.3605002164840698e-01 5.5794501304626465e-01
            -2.1474960446357727e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 130 -1.6347589553333819e-04 -1 -2 131
            7.1614650078117847e-03</internalNodes>
          <leafValues>
            -5.5962842702865601e-01 -1.6604369878768921e-01
            4.6805259585380554e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 132 -1.3145170174539089e-02 -1 -2 133
            -1.1436809785664082e-02</internalNodes>
          <leafValues>
            -4.1279909014701843e-01 3.7901800870895386e-01
            -4.1791579127311707e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 134 -7.2912001051008701e-03 -1 -2 135
            -5.2170921117067337e-04</internalNodes>
          <leafValues>
            -7.6089668273925781e-01 3.2527619600296021e-01
            -3.0110970139503479e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 136 3.3754010219126940e-03 -1 -2 137
            2.5100160855799913e-03</internalNodes>
          <leafValues>
            -7.8373962640762329e-01 1.8525449931621552e-01
            -5.8084958791732788e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 138 -1.2884209863841534e-03 -1 -2 139
            -1.8726480193436146e-03</internalNodes>
          <leafValues>
            2.7339500188827515e-01 1.6819879412651062e-01
            -5.1986902952194214e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 140 2.4010189808905125e-03 -1 -2 141
            4.8938081599771976e-03</internalNodes>
          <leafValues>
            -8.2964670658111572e-01 1.6796599328517914e-01
            -6.5530872344970703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 142 3.1223020050674677e-03 -1 -2 143
            5.0366491079330444e-02</internalNodes>
          <leafValues>
            -4.3521308898925781e-01 -5.8327801525592804e-03
            7.0878309011459351e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 144 3.6151800304651260e-02 -1 -2 145
            -1.3426589965820312e-01</internalNodes>
          <leafValues>
            4.4979161024093628e-01 3.9472430944442749e-01
            -3.7588629126548767e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 146 -2.7791369706392288e-02 -1 -2 147
            -1.2712170369923115e-02</internalNodes>
          <leafValues>
            -2.9488721489906311e-01 -7.2011739015579224e-01
            3.6595028638839722e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 148 -3.8276749546639621e-04 -1 -2 149
            -6.1330529861152172e-03</internalNodes>
          <leafValues>
            -4.0581339597702026e-01 -5.2725958824157715e-01
            3.6040499806404114e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>16</maxWeakCount>
      <stageThreshold>-2.0902318954467773e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 150 -4.7748669981956482e-02 -1 -2 151
            4.6201851218938828e-03</internalNodes>
          <leafValues>
            -5.9902387857437134e-01 -2.4887490272521973e-01
            6.9201582670211792e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 152 -8.5353456437587738e-02 -1 -2 153
            -7.0110969245433807e-03</internalNodes>
          <leafValues>
            -5.1715832948684692e-01 5.6950652599334717e-01
            -2.4749420583248138e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 154 -7.6567470096051693e-03 -1 -2 155
            -3.5919491201639175e-02</internalNodes>
          <leafValues>
            -3.7316519021987915e-01 4.9438580870628357e-01
            -3.9586681127548218e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 156 -7.4326626956462860e-02 -1 -2 157
            9.0118587017059326e-02</internalNodes>
          <leafValues>
            5.6755977869033813e-01 -3.8921171426773071e-01
            3.1079098582267761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 158 1.6736460849642754e-02 -1 -2 159
            1.8592580454424024e-03</internalNodes>
          <leafValues>
            -3.6674138903617859e-01 3.4875720739364624e-01
            -5.7483112812042236e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 160 7.5264140032231808e-03 -1 -2 161
            -3.5309391096234322e-03</internalNodes>
          <leafValues>
            6.7878991365432739e-01 4.8617920279502869e-01
            -2.5660640001296997e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 162 -4.9510748795000836e-05 -1 -2 163
            -6.8923248909413815e-03</internalNodes>
          <leafValues>
            -4.5661240816116333e-01 -5.7134729623794556e-01
            3.2921048998832703e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 164 6.1156069859862328e-03 -1 -2 165
            -5.5014882236719131e-03</internalNodes>
          <leafValues>
            -7.1315360069274902e-01 -5.9139078855514526e-01
            1.9805949926376343e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 166 -4.2378060519695282e-02 -1 -2 167
            2.2011259570717812e-03</internalNodes>
          <leafValues>
            -3.8239300251007080e-01 3.3457010984420776e-01
            -4.3032339215278625e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 168 2.1217379253357649e-03 -1 -2 169
            6.4385468140244484e-03</internalNodes>
          <leafValues>
            -6.8310022354125977e-01 2.0478610694408417e-01
            -6.1793941259384155e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 170 3.1177410855889320e-03 -1 -2 171
            4.2230269173160195e-04</internalNodes>
          <leafValues>
            5.1137161254882812e-01 -3.6440208554267883e-01
            2.1073049306869507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 172 -6.5657291561365128e-03 -1 -2 173
            2.5686610024422407e-03</internalNodes>
          <leafValues>
            -6.4581501483917236e-01 2.7643561363220215e-01
            -3.4198498725891113e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 174 -6.2437567976303399e-05 -1 -2 175
            -3.6269261036068201e-03</internalNodes>
          <leafValues>
            -3.1758078932762146e-01 -8.1051957607269287e-01
            2.7218630909919739e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 176 -3.4638389479368925e-03 -1 -2 177
            -7.4930191040039062e-02</internalNodes>
          <leafValues>
            -3.9515769481658936e-01 -5.4353868961334229e-01
            2.6106119155883789e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 178 -9.7247250378131866e-03 -1 -2 179
            4.5450199395418167e-03</internalNodes>
          <leafValues>
            4.1124871373176575e-01 -3.1576550006866455e-01
            3.9046970009803772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 180 -2.7354240883141756e-03 -1 -2 181
            -1.6969470307230949e-02</internalNodes>
          <leafValues>
            -7.4906748533248901e-01 -6.2437218427658081e-01
            1.8387380242347717e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>15</maxWeakCount>
      <stageThreshold>-1.9407310485839844e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 182 -2.4978699162602425e-02 -1 -2 183
            -5.8007869869470596e-02</internalNodes>
          <leafValues>
            -6.0697889328002930e-01 7.1478021144866943e-01
            -2.9943239688873291e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 184 -5.1753749139606953e-03 -1 -2 185
            -8.9618662605062127e-04</internalNodes>
          <leafValues>
            -3.5297989845275879e-01 5.4417461156845093e-01
            -3.9789950847625732e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 186 -2.8718139219563454e-05 -1 -2 187
            4.7620530240237713e-03</internalNodes>
          <leafValues>
            -4.8898181319236755e-01 -3.1144559383392334e-01
            4.6786791086196899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 188 1.9751280546188354e-02 -1 -2 189
            -1.2683609966188669e-03</internalNodes>
          <leafValues>
            -4.3020489811897278e-01 -5.4090851545333862e-01
            3.9797520637512207e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 190 -4.5749718992738053e-05 -1 -2 191
            2.4090509396046400e-03</internalNodes>
          <leafValues>
            -4.4518938660621643e-01 2.8822308778762817e-01
            -5.4514312744140625e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 192 -4.5728669501841068e-03 -1 -2 193
            8.9018214493989944e-03</internalNodes>
          <leafValues>
            5.5039870738983154e-01 -4.1598889231681824e-01
            1.7468899488449097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 194 -1.2056449800729752e-01 -1 -2 195
            4.6919930726289749e-02</internalNodes>
          <leafValues>
            6.8890577554702759e-01 -4.2266309261322021e-01
            1.7010940611362457e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 196 -4.2390259914100170e-03 -1 -2 197
            3.2174249645322561e-03</internalNodes>
          <leafValues>
            -6.3045340776443481e-01 -3.6097949743270874e-01
            2.4933730065822601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 198 -8.5738790221512318e-04 -1 -2 199
            -1.8432449549436569e-02</internalNodes>
          <leafValues>
            3.0993479490280151e-01 9.7758449614048004e-02
            -5.0742352008819580e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 200 5.8692828752100468e-03 -1 -2 201
            -6.8751699291169643e-03</internalNodes>
          <leafValues>
            -7.4556058645248413e-01 -6.7458391189575195e-01
            1.5918810665607452e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 202 -6.8542227381840348e-05 -1 -2 203
            -1.0658579878509045e-02</internalNodes>
          <leafValues>
            -4.1279420256614685e-01 3.7002709507942200e-01
            -2.1731729805469513e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 204 -1.8811509944498539e-03 -1 -2 205
            -2.2309130057692528e-02</internalNodes>
          <leafValues>
            5.7902830839157104e-01 1.9725680351257324e-01
            -3.2475191354751587e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 206 6.5826578065752983e-04 -1 -2 207
            -5.0781588070094585e-03</internalNodes>
          <leafValues>
            -6.0630238056182861e-01 -7.7123302221298218e-01
            1.8186129629611969e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 208 5.6215081363916397e-02 -1 -2 209
            -3.7720590829849243e-02</internalNodes>
          <leafValues>
            5.0561398267745972e-01 3.6052110791206360e-01
            -3.2743760943412781e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 210 3.9480631239712238e-03 -1 -2 211
            -2.4269670248031616e-03</internalNodes>
          <leafValues>
            -7.5788182020187378e-01 5.2076101303100586e-01
            -6.1021361500024796e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>19</maxWeakCount>
      <stageThreshold>-2.1061589717864990e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 212 -1.6906699165701866e-02 -1 -2 213
            2.5327840819954872e-02</internalNodes>
          <leafValues>
            -4.7501268982887268e-01 -4.4016760587692261e-01
            6.0885351896286011e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 214 -1.5663320198655128e-02 -1 -2 215
            -1.6101899743080139e-01</internalNodes>
          <leafValues>
            5.7100051641464233e-01 4.0989148616790771e-01
            -3.8142371177673340e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 216 1.6885380318854004e-04 -1 -2 217
            -3.0552360694855452e-03</internalNodes>
          <leafValues>
            -4.7958490252494812e-01 4.2852300405502319e-01
            -2.8252631425857544e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 218 4.8042940907180309e-03 -1 -2 219
            -5.0092511810362339e-03</internalNodes>
          <leafValues>
            -6.8659138679504395e-01 -5.9033542871475220e-01
            1.9732500612735748e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 220 -3.7119518965482712e-02 -1 -2 221
            3.7857799325138330e-03</internalNodes>
          <leafValues>
            -4.3130961060523987e-01 3.3596190810203552e-01
            -3.7401720881462097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 222 -1.0869850404560566e-02 -1 -2 223
            4.0577541221864522e-04</internalNodes>
          <leafValues>
            5.4841208457946777e-01 -5.0022697448730469e-01
            5.1423858851194382e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 224 5.0201490521430969e-03 -1 -2 225
            2.5601210072636604e-03</internalNodes>
          <leafValues>
            -5.9016227722167969e-01 1.9469800591468811e-01
            -6.4648360013961792e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 226 -1.2395749799907207e-03 -1 -2 227
            -5.1075750961899757e-03</internalNodes>
          <leafValues>
            -2.7762159705162048e-01 -6.1149162054061890e-01
            3.5250389575958252e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 228 -6.4853738876990974e-05 -1 -2 229
            2.3282810579985380e-03</internalNodes>
          <leafValues>
            -3.4008860588073730e-01 2.7134749293327332e-01
            -6.6915398836135864e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 230 -1.5571110416203737e-03 -1 -2 231
            2.3992219939827919e-03</internalNodes>
          <leafValues>
            -4.1144248843193054e-01 2.5939700007438660e-01
            -4.0380299091339111e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 232 7.7784422319382429e-04 -1 -2 233
            3.2334199640899897e-03</internalNodes>
          <leafValues>
            2.9523921012878418e-01 -5.8436852693557739e-01
            -1.7936639487743378e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 234 -5.6113858590833843e-05 -1 -2 235
            1.9111000001430511e-03</internalNodes>
          <leafValues>
            -3.5021650791168213e-01 2.6312610507011414e-01
            -6.1549347639083862e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 236 -3.4321150742471218e-03 -1 -2 237
            -1.4541969634592533e-02</internalNodes>
          <leafValues>
            3.7493300437927246e-01 4.3788930773735046e-01
            -3.0131611227989197e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 238 -2.5027070194482803e-02 -1 -2 239
            -3.1183639075607061e-03</internalNodes>
          <leafValues>
            -5.2829748392105103e-01 -8.1336849927902222e-01
            1.7928420007228851e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 240 2.9415208846330643e-03 -1 -2 241
            -2.4807679001241922e-03</internalNodes>
          <leafValues>
            -4.7243058681488037e-01 -6.0058331489562988e-01
            2.1497109532356262e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 242 -4.2498838156461716e-03 -1 -2 243
            7.6959328725934029e-03</internalNodes>
          <leafValues>
            -3.3230608701705933e-01 2.1247069537639618e-01
            -8.1967252492904663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 244 -6.1426039785146713e-02 -1 -2 245
            5.3176790475845337e-02</internalNodes>
          <leafValues>
            5.2200448513031006e-01 -2.9851761460304260e-01
            2.8654190897941589e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 246 2.5695779186207801e-05 -1 -2 247
            2.4311970919370651e-03</internalNodes>
          <leafValues>
            -3.4719291329383850e-01 -1.2133490294218063e-01
            3.8965350389480591e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 248 5.6956289336085320e-03 -1 -2 249
            -6.6630227956920862e-04</internalNodes>
          <leafValues>
            -6.6364032030105591e-01 2.7921909093856812e-01
            -2.1624849736690521e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>20</maxWeakCount>
      <stageThreshold>-2.0051579475402832e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 250 -2.8509549796581268e-02 -1 -2 251
            -1.6429109498858452e-02</internalNodes>
          <leafValues>
            -5.5133241415023804e-01 6.0328769683837891e-01
            -3.0009600520133972e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 252 -5.8078952133655548e-03 -1 -2 253
            -1.4670349657535553e-02</internalNodes>
          <leafValues>
            -4.8640519380569458e-01 4.4786658883094788e-01
            -3.5448360443115234e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 254 -1.0694459779188037e-03 -1 -2 255
            -5.0697539001703262e-02</internalNodes>
          <leafValues>
            -3.8593119382858276e-01 4.3865600228309631e-01
            -3.1134051084518433e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 256 -7.2318017482757568e-02 -1 -2 257
            -1.6740759834647179e-02</internalNodes>
          <leafValues>
            5.5695492029190063e-01 3.4036931395530701e-01
            -3.7713068723678589e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 258 1.2923260219395161e-02 -1 -2 259
            -2.0832989830523729e-03</internalNodes>
          <leafValues>
            2.6987180113792419e-01 7.2217263281345367e-02
            -5.0617259740829468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 260 2.9217539122328162e-04 -1 -2 261
            4.6477448195219040e-03</internalNodes>
          <leafValues>
            -4.7199469804763794e-01 -2.0233640074729919e-01
            3.6684620380401611e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 262 1.6355320112779737e-03 -1 -2 263
            6.0143060982227325e-03</internalNodes>
          <leafValues>
            -3.3369150757789612e-01 2.6335370540618896e-01
            -7.5315129756927490e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 264 -1.9768040627241135e-02 -1 -2 265
            5.0995801575481892e-03</internalNodes>
          <leafValues>
            -7.3396641016006470e-01 -1.0626330226659775e-01
            3.7877479195594788e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 266 2.1737320348620415e-03 -1 -2 267
            2.3621059954166412e-02</internalNodes>
          <leafValues>
            -4.5873621106147766e-01 -3.7341989576816559e-02
            5.0312960147857666e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 268 4.7070439904928207e-02 -1 -2 269
            4.8429161310195923e-02</internalNodes>
          <leafValues>
            3.9159670472145081e-01 -2.7507638931274414e-01
            3.6923450231552124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 270 7.1763257437851280e-05 -1 -2 271
            -4.0031517855823040e-03</internalNodes>
          <leafValues>
            -2.6133701205253601e-01 -4.6118479967117310e-01
            3.4101578593254089e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 272 2.5536299217492342e-03 -1 -2 273
            -2.5720898993313313e-03</internalNodes>
          <leafValues>
            4.4237849116325378e-01 4.3066531419754028e-01
            -2.8360688686370850e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 274 8.7512210011482239e-03 -1 -2 275
            5.7346918620169163e-03</internalNodes>
          <leafValues>
            -7.7647632360458374e-01 1.4551159739494324e-01
            -7.5074160099029541e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 276 -6.6438838839530945e-03 -1 -2 277
            -3.4590701106935740e-03</internalNodes>
          <leafValues>
            4.0350550413131714e-01 2.8769719600677490e-01
            -2.8021600842475891e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 278 9.9742468446493149e-03 -1 -2 279
            1.3233659788966179e-02</internalNodes>
          <leafValues>
            -6.0677021741867065e-01 1.5478080511093140e-01
            -7.0759147405624390e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 280 -5.0271311774849892e-03 -1 -2 281
            -1.2092100223526359e-04</internalNodes>
          <leafValues>
            -7.3897778987884521e-01 2.3473000526428223e-01
            -2.4400579929351807e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 282 -1.2881499715149403e-03 -1 -2 283
            6.2854858115315437e-03</internalNodes>
          <leafValues>
            -2.8901669383049011e-01 2.8100869059562683e-01
            -5.6933850049972534e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 284 5.6929360143840313e-03 -1 -2 285
            -5.3880861960351467e-03</internalNodes>
          <leafValues>
            -7.8456932306289673e-01 2.6201328635215759e-01
            -2.2232030332088470e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 286 4.8205819912254810e-03 -1 -2 287
            3.4279188513755798e-01</internalNodes>
          <leafValues>
            5.6795972585678101e-01 -1.8314230442047119e-01
            5.4108071327209473e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 288 5.1370919682085514e-03 -1 -2 289
            -9.1285221278667450e-03</internalNodes>
          <leafValues>
            -3.9116761088371277e-01 5.3076338768005371e-01
            -3.0019309371709824e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>21</maxWeakCount>
      <stageThreshold>-2.1121981143951416e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 290 -5.1386129111051559e-02 -1 -2 291
            5.1850839518010616e-03</internalNodes>
          <leafValues>
            -5.3148782253265381e-01 -2.4744540452957153e-01
            6.1181622743606567e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 292 -1.5259400010108948e-02 -1 -2 293
            2.5995150208473206e-02</internalNodes>
          <leafValues>
            -4.3303629755973816e-01 4.3979901820421219e-02
            7.3829138278961182e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 294 -3.2312370836734772e-02 -1 -2 295
            1.3700700365006924e-02</internalNodes>
          <leafValues>
            -3.9609751105308533e-01 -2.7643880248069763e-01
            4.2535358667373657e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 296 -2.2647869773209095e-03 -1 -2 297
            -6.8290620110929012e-03</internalNodes>
          <leafValues>
            -3.2005569338798523e-01 -5.1682972908020020e-01
            3.6975708603858948e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 298 -2.2481549531221390e-03 -1 -2 299
            4.5944549143314362e-02</internalNodes>
          <leafValues>
            -3.6244350671768188e-01 -1.3187309959903359e-03
            6.3217681646347046e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 300 1.8755620112642646e-03 -1 -2 301
            -1.9700559787452221e-03</internalNodes>
          <leafValues>
            -7.1403390169143677e-01 -5.8730661869049072e-01
            1.7592810094356537e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 302 -6.5721389837563038e-03 -1 -2 303
            -1.1746180243790150e-02</internalNodes>
          <leafValues>
            -3.6347511410713196e-01 3.1440791487693787e-01
            -4.0111118555068970e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 304 -1.6494120063725859e-04 -1 -2 305
            -7.2169408667832613e-05</internalNodes>
          <leafValues>
            -3.7792590260505676e-01 5.2791112661361694e-01
            -1.0790319740772247e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 306 1.9697639800142497e-04 -1 -2 307
            -1.1423509567975998e-02</internalNodes>
          <leafValues>
            -4.7097641229629517e-01 -8.5209292173385620e-01
            1.7662869393825531e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 308 -4.5562228187918663e-03 -1 -2 309
            -4.4720191508531570e-03</internalNodes>
          <leafValues>
            -8.0601161718368530e-01 -6.1500209569931030e-01
            1.2908309698104858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 310 -1.7765410011634231e-03 -1 -2 311
            -7.8799277544021606e-03</internalNodes>
          <leafValues>
            3.1382599472999573e-01 3.0394628643989563e-01
            -3.7204921245574951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 312 -1.4284689677879214e-03 -1 -2 313
            -1.8939910223707557e-03</internalNodes>
          <leafValues>
            5.0413030385971069e-01 3.4823760390281677e-01
            -2.3673820495605469e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 314 -3.1496640294790268e-03 -1 -2 315
            -1.0716119781136513e-02</internalNodes>
          <leafValues>
            -6.6812378168106079e-01 -4.8515519499778748e-01
            1.9036419689655304e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 316 -6.8033537827432156e-03 -1 -2 317
            1.4902319759130478e-02</internalNodes>
          <leafValues>
            -5.6979268789291382e-01 1.3098250329494476e-01
            -7.1448272466659546e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 318 -3.4170228987932205e-02 -1 -2 319
            -1.4779250323772430e-01</internalNodes>
          <leafValues>
            5.0575131177902222e-01 2.8233268857002258e-01
            -2.7205321192741394e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 320 -5.5842810979811475e-05 -1 -2 321
            3.9885081350803375e-02</internalNodes>
          <leafValues>
            -2.6936730742454529e-01 5.6696129031479359e-03
            6.3975161314010620e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 322 1.2483130209147930e-02 -1 -2 323
            -3.2864511013031006e-04</internalNodes>
          <leafValues>
            -7.4533742666244507e-01 3.6449620127677917e-01
            -9.6498817205429077e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 324 -1.4710469986312091e-04 -1 -2 325
            -2.7814340591430664e-01</internalNodes>
          <leafValues>
            1.4060440659523010e-01 5.7002830505371094e-01
            -4.8755478858947754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 326 -1.3452640268951654e-03 -1 -2 327
            9.1500842245295644e-04</internalNodes>
          <leafValues>
            3.9255830645561218e-01 -3.0215170979499817e-01
            3.6698031425476074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 328 -3.4133149310946465e-03 -1 -2 329
            5.1169008947908878e-03</internalNodes>
          <leafValues>
            -6.4085817337036133e-01 -2.3052580654621124e-01
            2.4285919964313507e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 330 8.8846698403358459e-02 -1 -2 331
            6.1080828309059143e-03</internalNodes>
          <leafValues>
            4.5381888747215271e-01 -3.5880088806152344e-01
            1.3209380209445953e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>23</maxWeakCount>
      <stageThreshold>-1.8701590299606323e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 332 -1.5930000692605972e-02 -1 -2 333
            2.7407450601458549e-02</internalNodes>
          <leafValues>
            -3.5245341062545776e-01 -6.0236789286136627e-02
            7.2715848684310913e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 334 -8.5037678480148315e-02 -1 -2 335
            -1.1508919997140765e-03</internalNodes>
          <leafValues>
            -4.3576711416244507e-01 4.6471679210662842e-01
            -3.5896891355514526e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 336 -6.4599298639222980e-04 -1 -2 337
            5.5495807901024818e-03</internalNodes>
          <leafValues>
            -3.1371060013771057e-01 4.1225919127464294e-01
            -4.9400448799133301e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 338 -1.1472150217741728e-03 -1 -2 339
            -6.4546810463070869e-03</internalNodes>
          <leafValues>
            -3.9192581176757812e-01 -6.9197827577590942e-01
            2.6103940606117249e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 340 -1.1414250358939171e-02 -1 -2 341
            1.1582579463720322e-03</internalNodes>
          <leafValues>
            3.2361420989036560e-01 -3.8304999470710754e-01
            2.8015980124473572e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 342 -6.1077292775735259e-04 -1 -2 343
            1.1812780285254121e-03</internalNodes>
          <leafValues>
            -3.7471079826354980e-01 -1.7685219645500183e-01
            3.5498109459877014e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 344 7.9117231070995331e-03 -1 -2 345
            -9.0904926764778793e-05</internalNodes>
          <leafValues>
            -6.9681918621063232e-01 2.0756739377975464e-01
            -4.4282090663909912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 346 2.8638960793614388e-03 -1 -2 347
            1.2769990134984255e-03</internalNodes>
          <leafValues>
            -4.1364789009094238e-01 -2.1157020330429077e-01
            3.1919568777084351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 348 -7.5440858490765095e-03 -1 -2 349
            5.4467269219458103e-03</internalNodes>
          <leafValues>
            -7.5495690107345581e-01 1.3229879736900330e-01
            -6.7695891857147217e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 350 1.3641830300912261e-03 -1 -2 351
            1.3810779899358749e-02</internalNodes>
          <leafValues>
            -4.2168149352073669e-01 1.5719360113143921e-01
            -6.7965167760848999e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 352 5.0265640020370483e-02 -1 -2 353
            4.7765119234099984e-05</internalNodes>
          <leafValues>
            7.4369138479232788e-01 -3.8102349638938904e-01
            1.0605350136756897e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 354 1.4666689932346344e-01 -1 -2 355
            -3.0426830053329468e-01</internalNodes>
          <leafValues>
            5.3409832715988159e-01 3.7783610820770264e-01
            -2.1534620225429535e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 356 -3.2244708854705095e-03 -1 -2 357
            -1.7187190242111683e-03</internalNodes>
          <leafValues>
            2.8274241089820862e-01 1.0677109658718109e-01
            -4.4204118847846985e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 358 -8.4115704521536827e-03 -1 -2 359
            -2.3220919072628021e-02</internalNodes>
          <leafValues>
            -8.3557051420211792e-01 -5.1933908462524414e-01
            1.3181640207767487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 360 -6.3912221230566502e-03 -1 -2 361
            -3.0661540222354233e-04</internalNodes>
          <leafValues>
            -6.8552321195602417e-01 2.2192850708961487e-01
            -2.3945030570030212e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 362 1.8742750398814678e-03 -1 -2 363
            -2.8299540281295776e-02</internalNodes>
          <leafValues>
            -4.7218438982963562e-01 -6.8186718225479126e-01
            1.5923790633678436e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 364 7.9352483153343201e-03 -1 -2 365
            -8.7599940598011017e-03</internalNodes>
          <leafValues>
            -7.3135781288146973e-01 -6.0014718770980835e-01
            1.0350330173969269e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 366 -5.5426149629056454e-03 -1 -2 367
            -1.8066290067508817e-03</internalNodes>
          <leafValues>
            -5.9360408782958984e-01 2.5533521175384521e-01
            -1.7036439478397369e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 368 -8.3993803709745407e-03 -1 -2 369
            -1.9515500171110034e-03</internalNodes>
          <leafValues>
            -2.3953610658645630e-01 3.7252411246299744e-01
            -1.2982900440692902e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 370 -2.2850139066576958e-03 -1 -2 371
            -6.1910818330943584e-03</internalNodes>
          <leafValues>
            5.0227212905883789e-01 4.4551658630371094e-01
            -1.6307780146598816e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 372 1.1659320443868637e-03 -1 -2 373
            -2.1016779355704784e-03</internalNodes>
          <leafValues>
            3.4809079766273499e-01 3.1531378626823425e-01
            -3.4710261225700378e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 374 -9.1615924611687660e-03 -1 -2 375
            -2.0036540925502777e-02</internalNodes>
          <leafValues>
            -6.8623197078704834e-01 -6.8991881608963013e-01
            1.2962220609188080e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 376 2.7148448862135410e-03 -1 -2 377
            2.2834159899502993e-03</internalNodes>
          <leafValues>
            4.7745740413665771e-01 -1.3344570063054562e-02
            -6.1795878410339355e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>26</maxWeakCount>
      <stageThreshold>-1.9807859659194946e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 378 -3.2838471233844757e-02 -1 -2 379
            -7.5696408748626709e-03</internalNodes>
          <leafValues>
            -5.1984071731567383e-01 6.3690251111984253e-01
            -1.1562170088291168e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 380 5.4125871509313583e-02 -1 -2 381
            2.7004599571228027e-01</internalNodes>
          <leafValues>
            5.0340247154235840e-01 -3.4640678763389587e-01
            3.7651509046554565e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 382 7.0261410437524319e-03 -1 -2 383
            3.1245660502463579e-03</internalNodes>
          <leafValues>
            -4.1046440601348877e-01 -4.1382190585136414e-01
            3.7550741434097290e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 384 -1.8708549905568361e-03 -1 -2 385
            -1.4969009906053543e-02</internalNodes>
          <leafValues>
            -3.7827330827713013e-01 3.9941680431365967e-01
            -2.2254510223865509e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 386 3.4136420581489801e-03 -1 -2 387
            2.3454260081052780e-03</internalNodes>
          <leafValues>
            -5.4667568206787109e-01 1.6618840396404266e-01
            -6.3203942775726318e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 388 -1.1689099483191967e-03 -1 -2 389
            -7.8206984326243401e-03</internalNodes>
          <leafValues>
            -4.4972181320190430e-01 -5.7166117429733276e-01
            1.8599990010261536e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 390 -2.6324259117245674e-02 -1 -2 391
            -9.1647548833861947e-04</internalNodes>
          <leafValues>
            -7.8041112422943115e-01 2.3100090026855469e-01
            -2.1224120259284973e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 392 -2.3702960461378098e-03 -1 -2 393
            -9.2874821275472641e-03</internalNodes>
          <leafValues>
            2.7304211258888245e-01 2.3200799524784088e-01
            -3.4602558612823486e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 394 2.9221060685813427e-03 -1 -2 395
            -1.4097889652475715e-03</internalNodes>
          <leafValues>
            -6.9972628355026245e-01 4.8019358515739441e-01
            -4.2650200426578522e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 396 9.3326548812910914e-04 -1 -2 397
            -5.6837309151887894e-02</internalNodes>
          <leafValues>
            3.7708479166030884e-01 4.6375161409378052e-01
            -2.0441579818725586e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 398 -9.1405760031193495e-05 -1 -2 399
            -1.1147770099341869e-02</internalNodes>
          <leafValues>
            -2.9447770118713379e-01 3.6579200625419617e-01
            -1.6106230020523071e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 400 8.0759642878547311e-04 -1 -2 401
            1.7215589759871364e-03</internalNodes>
          <leafValues>
            -3.8769969344139099e-01 1.7790059745311737e-01
            -5.9673792123794556e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 402 1.4305640012025833e-02 -1 -2 403
            -3.8885008543729782e-02</internalNodes>
          <leafValues>
            -2.8887918591499329e-01 3.6497229337692261e-01
            -1.3762719929218292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 404 -3.4479280002415180e-03 -1 -2 405
            3.0168178677558899e-01</internalNodes>
          <leafValues>
            1.8110840022563934e-01 -3.5425490140914917e-01
            4.2958360910415649e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 406 2.8582389932125807e-03 -1 -2 407
            1.4091320335865021e-03</internalNodes>
          <leafValues>
            5.2957808971405029e-01 -2.1234430372714996e-01
            3.1428509950637817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 408 -1.6597079811617732e-03 -1 -2 409
            8.7804382201284170e-04</internalNodes>
          <leafValues>
            -6.3348418474197388e-01 -5.5315300822257996e-02
            3.9389958977699280e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 410 2.0211800001561642e-03 -1 -2 411
            -6.8409871309995651e-03</internalNodes>
          <leafValues>
            -4.7127309441566467e-01 -6.4065527915954590e-01
            1.4861440658569336e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 412 4.7200761735439301e-02 -1 -2 413
            4.9684080295264721e-03</internalNodes>
          <leafValues>
            4.1216409206390381e-01 -3.2404300570487976e-01
            1.5755960345268250e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 414 3.7529911845922470e-02 -1 -2 415
            -1.1665089987218380e-02</internalNodes>
          <leafValues>
            4.1328459978103638e-01 2.5467500090599060e-01
            -3.1303560733795166e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 416 -6.8298257247079164e-05 -1 -2 417
            1.5325429849326611e-02</internalNodes>
          <leafValues>
            -2.7212071418762207e-01 2.2946609556674957e-01
            -6.7345708608627319e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 418 8.5185896605253220e-03 -1 -2 419
            -2.6828479021787643e-03</internalNodes>
          <leafValues>
            -7.1114671230316162e-01 1.5511700510978699e-01
            -3.5444891452789307e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 420 1.3791749952360988e-03 -1 -2 421
            -3.3968368370551616e-05</internalNodes>
          <leafValues>
            3.6916270852088928e-01 5.9150930494070053e-02
            -4.6007719635963440e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 422 5.8259358629584312e-03 -1 -2 423
            -8.1688696518540382e-03</internalNodes>
          <leafValues>
            -5.4986697435379028e-01 -5.0567412376403809e-01
            1.5189670026302338e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 424 -2.3251199163496494e-03 -1 -2 425
            -4.8669208772480488e-03</internalNodes>
          <leafValues>
            3.4904810786247253e-01 5.3138560056686401e-01
            -2.1413469314575195e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 426 4.3380381539463997e-03 -1 -2 427
            3.4176679328083992e-03</internalNodes>
          <leafValues>
            -7.8248262405395508e-01 1.2460789829492569e-01
            -5.5297750234603882e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 428 5.5309730768203735e-01 -1 -2 429
            2.3636389523744583e-03</internalNodes>
          <leafValues>
            4.6573078632354736e-01 -3.3309051394462585e-01
            9.4380050897598267e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>26</maxWeakCount>
      <stageThreshold>-1.9697020053863525e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 430 -2.2934280335903168e-02 -1 -2 431
            -4.2665850371122360e-02</internalNodes>
          <leafValues>
            -4.4716298580169678e-01 5.4085898399353027e-01
            -3.3589279651641846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 432 -9.8418388515710831e-03 -1 -2 433
            -1.1932349763810635e-02</internalNodes>
          <leafValues>
            3.9958000183105469e-01 3.4219118952751160e-01
            -4.2416951060295105e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 434 -2.4437010288238525e-02 -1 -2 435
            -4.9987169913947582e-03</internalNodes>
          <leafValues>
            -3.7337359786033630e-01 4.0358328819274902e-01
            -3.5199370980262756e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 436 1.8582950579002500e-03 -1 -2 437
            2.7540219016373158e-03</internalNodes>
          <leafValues>
            -4.4158118963241577e-01 -2.8722938895225525e-01
            3.3857241272926331e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 438 -3.4452530089765787e-03 -1 -2 439
            -5.9277489781379700e-03</internalNodes>
          <leafValues>
            -3.1821981072425842e-01 -6.5073519945144653e-01
            2.7109220623970032e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 440 -1.2391789641696960e-04 -1 -2 441
            -7.3327139019966125e-02</internalNodes>
          <leafValues>
            -3.3467200398445129e-01 -5.9646248817443848e-01
            2.2861810028553009e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 442 -8.3964750170707703e-02 -1 -2 443
            -8.1644707825034857e-04</internalNodes>
          <leafValues>
            -2.2525189816951752e-01 3.8213649392127991e-01
            -3.3410450816154480e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 444 -1.5207779593765736e-02 -1 -2 445
            4.6894788742065430e-02</internalNodes>
          <leafValues>
            3.0742698907852173e-01 -3.8833889365196228e-01
            2.3177519440650940e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 446 -1.0398440062999725e-01 -1 -2 447
            3.9815339259803295e-03</internalNodes>
          <leafValues>
            7.1321141719818115e-01 -2.3310199379920959e-01
            2.9247841238975525e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 448 2.5737080723047256e-03 -1 -2 449
            9.1035291552543640e-04</internalNodes>
          <leafValues>
            -5.5017340183258057e-01 -1.8228930234909058e-01
            2.8370320796966553e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 450 6.4211348071694374e-03 -1 -2 451
            -5.8243819512426853e-03</internalNodes>
          <leafValues>
            -4.8581978678703308e-01 2.4608190357685089e-01
            -2.1565020084381104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 452 -4.0043629705905914e-02 -1 -2 453
            8.4683427121490240e-04</internalNodes>
          <leafValues>
            -6.3880550861358643e-01 -6.0435589402914047e-02
            4.3711128830909729e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 454 1.2964580208063126e-02 -1 -2 455
            -2.2524749510921538e-04</internalNodes>
          <leafValues>
            5.9495061635971069e-01 8.6831472814083099e-02
            -3.6362320184707642e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 456 -1.7258729785680771e-03 -1 -2 457
            -7.2289421223104000e-03</internalNodes>
          <leafValues>
            -6.4707720279693604e-01 -6.8775367736816406e-01
            1.3838720321655273e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 458 2.5079259648919106e-03 -1 -2 459
            -1.9473560387268662e-03</internalNodes>
          <leafValues>
            3.0659309029579163e-01 2.2967760264873505e-01
            -3.4737649559974670e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 460 7.4747111648321152e-03 -1 -2 461
            1.0328400094294921e-04</internalNodes>
          <leafValues>
            -6.5191787481307983e-01 -2.0725889503955841e-01
            2.2402130067348480e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 462 -7.8996885567903519e-03 -1 -2 463
            4.2833909392356873e-03</internalNodes>
          <leafValues>
            -7.2479170560836792e-01 1.3954970240592957e-01
            -4.3086060881614685e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 464 6.3452741596847773e-04 -1 -2 465
            -5.4966621100902557e-03</internalNodes>
          <leafValues>
            2.9792639613151550e-01 -5.6205391883850098e-01
            -2.9608119279146194e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 466 3.1408690847456455e-03 -1 -2 467
            -5.0443639047443867e-03</internalNodes>
          <leafValues>
            -6.1322140693664551e-01 -5.3060102462768555e-01
            1.2507459521293640e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 468 4.5964870601892471e-02 -1 -2 469
            -5.3749699145555496e-03</internalNodes>
          <leafValues>
            3.8188719749450684e-01 1.4089010655879974e-01
            -3.5535690188407898e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 470 2.9262059833854437e-03 -1 -2 471
            5.2230368601158261e-04</internalNodes>
          <leafValues>
            -6.0886657238006592e-01 -7.1441568434238434e-02
            3.6275258660316467e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 472 -4.4181118719279766e-03 -1 -2 473
            4.3349149636924267e-03</internalNodes>
          <leafValues>
            -7.6458007097244263e-01 1.1246410012245178e-01
            -5.4553848505020142e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 474 2.6483018882572651e-03 -1 -2 475
            -1.0814110282808542e-03</internalNodes>
          <leafValues>
            2.3542310297489166e-01 1.4422300457954407e-01
            -3.4401959180831909e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 476 -5.4296739108394831e-05 -1 -2 477
            5.5393581278622150e-03</internalNodes>
          <leafValues>
            -2.8607460856437683e-01 1.9345289468765259e-01
            -5.0549429655075073e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 478 3.3703099936246872e-02 -1 -2 479
            -1.2178930046502501e-04</internalNodes>
          <leafValues>
            3.8302558660507202e-01 6.6414177417755127e-02
            -4.8530051112174988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 480 -1.7803770024329424e-03 -1 -2 481
            -5.6019638577708974e-05</internalNodes>
          <leafValues>
            4.4113549590110779e-01 1.2396749854087830e-01
            -2.6292270421981812e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>30</maxWeakCount>
      <stageThreshold>-2.0330519676208496e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 482 3.1982790678739548e-03 -1 -2 483
            -1.5240450156852603e-03</internalNodes>
          <leafValues>
            5.4208421707153320e-01 8.2784838974475861e-02
            -5.0164830684661865e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 484 -1.2284429743885994e-02 -1 -2 485
            -8.3555448800325394e-03</internalNodes>
          <leafValues>
            4.4174939393997192e-01 3.5863399505615234e-01
            -3.6254858970642090e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 486 4.1357800364494324e-02 -1 -2 487
            2.2308749612420797e-03</internalNodes>
          <leafValues>
            4.7858810424804688e-01 -6.0390347242355347e-01
            -8.7199418339878321e-04</leafValues></_>
        <_>
          <internalNodes>
            1 0 488 -5.4160541296005249e-01 -1 -2 489
            7.9009458422660828e-03</internalNodes>
          <leafValues>
            -3.2536658644676208e-01 -3.6415100097656250e-01
            4.0501600503921509e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 490 -2.7236728928983212e-03 -1 -2 491
            2.1041880827397108e-03</internalNodes>
          <leafValues>
            -2.7644181251525879e-01 3.4068119525909424e-01
            -4.1922488808631897e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 492 1.2688159476965666e-03 -1 -2 493
            -4.2881062254309654e-03</internalNodes>
          <leafValues>
            -5.4520767927169800e-01 3.0060088634490967e-01
            -1.5233190357685089e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 494 -4.8890449106693268e-03 -1 -2 495
            5.0922110676765442e-03</internalNodes>
          <leafValues>
            -3.7665820121765137e-01 2.1803319454193115e-01
            -5.7126522064208984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 496 -7.0944731123745441e-03 -1 -2 497
            2.5431890040636063e-02</internalNodes>
          <leafValues>
            5.1921921968460083e-01 -2.1260249614715576e-01
            3.0566200613975525e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 498 -6.7461907747201622e-05 -1 -2 499
            -8.5350889712572098e-03</internalNodes>
          <leafValues>
            -3.3406749367713928e-01 3.5043460130691528e-01
            -9.0384833514690399e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 500 -4.1117807850241661e-03 -1 -2 501
            6.3964081928133965e-03</internalNodes>
          <leafValues>
            -6.9683700799942017e-01 1.1542639881372452e-01
            -6.6645371913909912e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 502 9.8322751000523567e-04 -1 -2 503
            -5.5737968068569899e-04</internalNodes>
          <leafValues>
            3.5695379972457886e-01 2.3081110417842865e-01
            -2.8862631320953369e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 504 2.8798289131373167e-03 -1 -2 505
            -7.7164517715573311e-03</internalNodes>
          <leafValues>
            -5.9923267364501953e-01 3.6074900627136230e-01
            -8.1827618181705475e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 506 3.7285129074007273e-03 -1 -2 507
            -1.3161109760403633e-02</internalNodes>
          <leafValues>
            -3.7732011079788208e-01 6.7023038864135742e-01
            1.5114549547433853e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 508 -3.8966130465269089e-02 -1 -2 509
            -5.7413699105381966e-03</internalNodes>
          <leafValues>
            -3.1252211332321167e-01 3.3947479724884033e-01
            -1.6011409461498260e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 510 1.2538330256938934e-01 -1 -2 511
            -9.7243122756481171e-02</internalNodes>
          <leafValues>
            7.3721152544021606e-01 5.0288981199264526e-01
            -1.3284370303153992e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 512 -2.0128490868955851e-03 -1 -2 513
            3.5349070094525814e-03</internalNodes>
          <leafValues>
            4.1367891430854797e-01 -1.5923270583152771e-01
            4.4056579470634460e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 514 4.4846540689468384e-01 -1 -2 515
            -1.0387780144810677e-02</internalNodes>
          <leafValues>
            5.9423661231994629e-01 3.0399119853973389e-01
            -1.8287350237369537e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 516 -1.4210389927029610e-03 -1 -2 517
            3.6446070298552513e-03</internalNodes>
          <leafValues>
            -4.5361068844795227e-01 1.5766820311546326e-01
            -6.2608838081359863e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 518 3.2253630924969912e-03 -1 -2 519
            9.8893349058926105e-04</internalNodes>
          <leafValues>
            -4.1410240530967712e-01 -1.0757800191640854e-01
            3.1156888604164124e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 520 -2.7107829228043556e-03 -1 -2 521
            -6.9264871999621391e-03</internalNodes>
          <leafValues>
            -7.5352817773818970e-01 2.7464428544044495e-01
            -1.1728949844837189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 522 -3.7942770868539810e-02 -1 -2 523
            1.3486459851264954e-02</internalNodes>
          <leafValues>
            2.6936548948287964e-01 -3.1532868742942810e-01
            2.5785440206527710e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 524 2.7866458985954523e-03 -1 -2 525
            3.2895719632506371e-03</internalNodes>
          <leafValues>
            -6.8431657552719116e-01 1.2949100136756897e-01
            -4.4475141167640686e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 526 1.7910100286826491e-03 -1 -2 527
            3.3694170415401459e-03</internalNodes>
          <leafValues>
            -5.6237429380416870e-01 -6.1936769634485245e-02
            3.6794289946556091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 528 6.5897632157430053e-04 -1 -2 529
            -3.2603838917566463e-05</internalNodes>
          <leafValues>
            -2.7705720067024231e-01 2.7426779270172119e-01
            -2.2369539737701416e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 530 -6.0175720602273941e-02 -1 -2 531
            -2.1217610687017441e-02</internalNodes>
          <leafValues>
            -7.4174910783767700e-01 -4.5034751296043396e-01
            1.1426000297069550e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 532 -2.2632910404354334e-03 -1 -2 533
            6.0313078574836254e-03</internalNodes>
          <leafValues>
            -3.0538588762283325e-01 2.0562660694122314e-01
            -4.0689799189567566e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 534 5.7578482665121555e-04 -1 -2 535
            -9.3677162658423185e-04</internalNodes>
          <leafValues>
            3.5098749399185181e-01 2.1616159379482269e-01
            -2.4415770173072815e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 536 -3.7626568228006363e-02 -1 -2 537
            4.4729812070727348e-03</internalNodes>
          <leafValues>
            -5.9113681316375732e-01 1.5792270004749298e-01
            -3.2226279377937317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 538 -7.1853301487863064e-03 -1 -2 539
            4.0520228445529938e-02</internalNodes>
          <leafValues>
            -5.9519052505493164e-01 -6.6688463091850281e-02
            3.4030249714851379e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 540 -6.1968388035893440e-03 -1 -2 541
            1.0311529971659184e-02</internalNodes>
          <leafValues>
            -6.7287462949752808e-01 1.0683239996433258e-01
            -5.4825967550277710e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>33</maxWeakCount>
      <stageThreshold>-1.9516259431838989e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 542 -1.9320519641041756e-02 -1 -2 543
            -1.5126460231840611e-02</internalNodes>
          <leafValues>
            -3.8712570071220398e-01 6.4468181133270264e-01
            -1.2727110087871552e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 544 -6.0182690620422363e-02 -1 -2 545
            -1.3576049823313951e-03</internalNodes>
          <leafValues>
            -3.0819109082221985e-01 4.8021888732910156e-01
            -3.3428680896759033e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 546 -5.6930771097540855e-03 -1 -2 547
            -8.0942036584019661e-03</internalNodes>
          <leafValues>
            -3.3166080713272095e-01 4.7517481446266174e-01
            -7.4761562049388885e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 548 6.8413332337513566e-04 -1 -2 549
            -1.1520589888095856e-01</internalNodes>
          <leafValues>
            -3.5741969943046570e-01 2.6105090975761414e-01
            -3.1773808598518372e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 550 -9.1124046593904495e-03 -1 -2 551
            5.4891068430151790e-05</internalNodes>
          <leafValues>
            -5.8540707826614380e-01 -2.2981899976730347e-01
            2.3482909798622131e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 552 -9.5622539520263672e-03 -1 -2 553
            -8.2032606005668640e-03</internalNodes>
          <leafValues>
            3.9155280590057373e-01 4.3179950118064880e-01
            -2.3173290491104126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 554 -4.0035760030150414e-03 -1 -2 555
            2.5406230706721544e-03</internalNodes>
          <leafValues>
            -5.8700478076934814e-01 1.7990030348300934e-01
            -4.1681569814682007e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 556 1.9435470458120108e-03 -1 -2 557
            8.4362342022359371e-04</internalNodes>
          <leafValues>
            3.0340009927749634e-01 -3.0661040544509888e-01
            2.3646999895572662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 558 -5.3103519603610039e-03 -1 -2 559
            -3.5526719875633717e-03</internalNodes>
          <leafValues>
            -5.6304818391799927e-01 -5.5695772171020508e-01
            1.5022790431976318e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 560 7.1414401754736900e-03 -1 -2 561
            -1.1435860069468617e-03</internalNodes>
          <leafValues>
            -6.7626637220382690e-01 3.7873879075050354e-01
            -7.4442893266677856e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 562 -3.1177429482340813e-03 -1 -2 563
            -7.7415622770786285e-02</internalNodes>
          <leafValues>
            -6.2568587064743042e-01 3.9839410781860352e-01
            -5.5262319743633270e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 564 -3.9252988994121552e-02 -1 -2 565
            2.2049970924854279e-02</internalNodes>
          <leafValues>
            3.4094831347465515e-01 -2.4413719773292542e-01
            4.3050870299339294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 566 -2.2205871064215899e-03 -1 -2 567
            2.8649640735238791e-03</internalNodes>
          <leafValues>
            2.8309720754623413e-01 -3.5401880741119385e-01
            2.1054570376873016e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 568 5.8806730521610007e-05 -1 -2 569
            -6.6595021635293961e-03</internalNodes>
          <leafValues>
            -2.7014040946960449e-01 -5.9313482046127319e-01
            2.1892869472503662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 570 1.6931600868701935e-02 -1 -2 571
            4.7026639804244041e-03</internalNodes>
          <leafValues>
            -1.1279620230197906e-01 4.9212211370468140e-01
            -3.9702880382537842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 572 1.7478819936513901e-03 -1 -2 573
            -2.0893230102956295e-03</internalNodes>
          <leafValues>
            -2.2339369356632233e-01 -4.3157818913459778e-01
            2.5373139977455139e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 574 1.1534850113093853e-02 -1 -2 575
            8.7350117973983288e-04</internalNodes>
          <leafValues>
            -7.0668542385101318e-01 -7.2509132325649261e-02
            3.9975029230117798e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 576 -7.2836421895772219e-04 -1 -2 577
            1.2666890397667885e-03</internalNodes>
          <leafValues>
            -2.3567649722099304e-01 2.2582389414310455e-01
            -4.2317348718643188e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 578 -8.4794021677225828e-04 -1 -2 579
            3.6212441325187683e-01</internalNodes>
          <leafValues>
            -2.8307029604911804e-01 1.6724239289760590e-01
            -7.6826947927474976e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 580 -1.9437649752944708e-03 -1 -2 581
            -4.1159680113196373e-03</internalNodes>
          <leafValues>
            -2.7229419350624084e-01 -6.4211308956146240e-01
            1.8810230493545532e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 582 2.3254039697349072e-03 -1 -2 583
            -1.4815620379522443e-03</internalNodes>
          <leafValues>
            2.8516888618469238e-01 4.2574208974838257e-01
            -2.1113610267639160e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 584 -6.6233296820428222e-05 -1 -2 585
            -3.3756431192159653e-02</internalNodes>
          <leafValues>
            -2.8205850720405579e-01 -8.1803041696548462e-01
            1.7053669691085815e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 586 -9.4350927975028753e-04 -1 -2 587
            1.0650219628587365e-03</internalNodes>
          <leafValues>
            1.5273140370845795e-01 -4.2650490999221802e-01
            1.5235939621925354e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 588 -1.2905279872938991e-03 -1 -2 589
            9.6549028530716896e-03</internalNodes>
          <leafValues>
            1.7365390062332153e-01 -3.9721599221229553e-01
            1.7953179776668549e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 590 1.3434770517051220e-03 -1 -2 591
            5.5220007197931409e-04</internalNodes>
          <leafValues>
            -6.9609320163726807e-01 -7.2258770465850830e-02
            3.4493291378021240e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 592 3.5795350559055805e-03 -1 -2 593
            -1.0585499927401543e-02</internalNodes>
          <leafValues>
            -4.8070669174194336e-01 -3.2975581288337708e-01
            1.4686919748783112e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 594 3.5636040847748518e-03 -1 -2 595
            -1.0298290103673935e-01</internalNodes>
          <leafValues>
            -6.1415022611618042e-01 -7.2366482019424438e-01
            8.4447070956230164e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 596 -2.9605759307742119e-02 -1 -2 597
            -3.4580599516630173e-02</internalNodes>
          <leafValues>
            4.7113609313964844e-01 -4.3128991127014160e-01
            2.4623470380902290e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 598 4.7923368401825428e-03 -1 -2 599
            1.7058040248230100e-03</internalNodes>
          <leafValues>
            -4.6270799636840820e-01 1.4738570153713226e-01
            -3.7818890810012817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 600 -3.3174119889736176e-03 -1 -2 601
            -1.7022279789671302e-03</internalNodes>
          <leafValues>
            2.7929860353469849e-01 2.6326990127563477e-01
            -2.5129210948944092e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 602 -8.1695342669263482e-04 -1 -2 603
            -1.4184829778969288e-03</internalNodes>
          <leafValues>
            -1.2859649956226349e-01 5.8855402469635010e-01
            -5.0085168331861496e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 604 -1.0478599928319454e-02 -1 -2 605
            3.1981911510229111e-02</internalNodes>
          <leafValues>
            1.4732900261878967e-01 -4.1299548745155334e-01
            3.4442049264907837e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 606 4.5543849468231201e-02 -1 -2 607
            2.3574009537696838e-02</internalNodes>
          <leafValues>
            4.8842081427574158e-01 -4.6383219957351685e-01
            3.7443768233060837e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>29</maxWeakCount>
      <stageThreshold>-1.7628519535064697e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            1 0 608 -3.2347131520509720e-02 -1 -2 609
            -7.4855431914329529e-02</internalNodes>
          <leafValues>
            -4.1153168678283691e-01 5.4409480094909668e-01
            -2.1043080091476440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 610 -5.9164799749851227e-02 -1 -2 611
            -5.0734709948301315e-03</internalNodes>
          <leafValues>
            4.6945521235466003e-01 8.0933347344398499e-02
            -4.0436869859695435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 612 6.6304411739110947e-03 -1 -2 613
            2.2804280743002892e-02</internalNodes>
          <leafValues>
            -3.1943950057029724e-01 -3.5277611017227173e-01
            3.6358159780502319e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 614 3.4148059785366058e-03 -1 -2 615
            -6.0696629807353020e-03</internalNodes>
          <leafValues>
            -4.2139899730682373e-01 2.8190940618515015e-01
            -2.5727981328964233e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 616 -3.3271780703216791e-03 -1 -2 617
            1.2381239794194698e-02</internalNodes>
          <leafValues>
            -3.3380180597305298e-01 2.5831120088696480e-02
            5.8200639486312866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 618 -7.8561902046203613e-02 -1 -2 619
            -7.6863910071551800e-03</internalNodes>
          <leafValues>
            5.7080817222595215e-01 1.9097380340099335e-01
            -2.4749469757080078e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 620 3.9404830895364285e-03 -1 -2 621
            -7.0624810177832842e-05</internalNodes>
          <leafValues>
            -3.5295888781547546e-01 2.8438061475753784e-01
            -1.6469420492649078e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 622 -2.2568539716303349e-03 -1 -2 623
            -3.5595949739217758e-03</internalNodes>
          <leafValues>
            -4.6189218759536743e-01 2.4525940418243408e-01
            -1.8984979391098022e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 624 -3.0113100074231625e-03 -1 -2 625
            -6.2748990021646023e-03</internalNodes>
          <leafValues>
            3.0594390630722046e-01 1.4716149866580963e-01
            -3.3265221118927002e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 626 2.5835279375314713e-03 -1 -2 627
            3.2576550729572773e-03</internalNodes>
          <leafValues>
            -7.4853891134262085e-01 -1.4949619770050049e-01
            2.6293671131134033e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 628 -2.6957978843711317e-04 -1 -2 629
            -4.4593680649995804e-03</internalNodes>
          <leafValues>
            -2.9468360543251038e-01 -4.5905289053916931e-01
            2.2235380113124847e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 630 2.2841650061309338e-03 -1 -2 631
            -6.7595718428492546e-04</internalNodes>
          <leafValues>
            -6.3815939426422119e-01 -3.1756940484046936e-01
            1.4903070032596588e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 632 6.1428439803421497e-03 -1 -2 633
            2.7392068877816200e-03</internalNodes>
          <leafValues>
            2.4187029898166656e-01 -3.1487539410591125e-01
            2.3589129745960236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 634 -2.0209311041980982e-03 -1 -2 635
            2.6892140507698059e-02</internalNodes>
          <leafValues>
            2.5389561057090759e-01 -3.4391039609909058e-01
            2.3010760545730591e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 636 1.4671060256659985e-02 -1 -2 637
            -1.2444119900465012e-02</internalNodes>
          <leafValues>
            5.9517538547515869e-01 3.7335929274559021e-01
            -1.4540639519691467e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 638 2.0527220331132412e-03 -1 -2 639
            -1.7088990658521652e-02</internalNodes>
          <leafValues>
            -2.1135020256042480e-01 -7.2516232728958130e-01
            2.3358739912509918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 640 -9.8585523664951324e-03 -1 -2 641
            -1.0541190393269062e-02</internalNodes>
          <leafValues>
            4.5390421152114868e-01 3.5500058531761169e-01
            -1.7118500173091888e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 642 4.0034228004515171e-03 -1 -2 643
            -1.1889140121638775e-02</internalNodes>
          <leafValues>
            -7.0433962345123291e-01 4.0436559915542603e-01
            -4.6263620257377625e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 644 -2.0685700699687004e-02 -1 -2 645
            -7.9243928194046021e-03</internalNodes>
          <leafValues>
            -6.4347600936889648e-01 -5.3632920980453491e-01
            1.1002989858388901e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 646 1.2431150535121560e-03 -1 -2 647
            -4.2312019504606724e-03</internalNodes>
          <leafValues>
            4.1220021247863770e-01 7.9887658357620239e-02
            -3.0926740169525146e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 648 9.8197339102625847e-03 -1 -2 649
            4.5455411076545715e-02</internalNodes>
          <leafValues>
            -6.0976761579513550e-01 1.0621140152215958e-01
            -6.4687371253967285e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 650 2.6892758905887604e-03 -1 -2 651
            -1.5172710409387946e-03</internalNodes>
          <leafValues>
            -4.9122989177703857e-01 1.7578749358654022e-01
            -2.6818940043449402e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 652 6.2014168361201882e-04 -1 -2 653
            -2.0233519899193197e-04</internalNodes>
          <leafValues>
            2.5500729680061340e-01 7.2745857760310173e-03
            -5.0815272331237793e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 654 3.1760020647197962e-03 -1 -2 655
            -1.2668699491769075e-03</internalNodes>
          <leafValues>
            4.3849268555641174e-01 1.6349400579929352e-01
            -2.9128161072731018e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 656 5.1056100055575371e-03 -1 -2 657
            -1.5026510227471590e-03</internalNodes>
          <leafValues>
            -7.5001358985900879e-01 2.7198830246925354e-01
            -9.9486798048019409e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 658 -3.6238620523363352e-03 -1 -2 659
            7.6577658765017986e-03</internalNodes>
          <leafValues>
            -6.0396248102188110e-01 1.0938379913568497e-01
            -5.3007638454437256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 660 -3.1830249354243279e-03 -1 -2 661
            1.0931329801678658e-02</internalNodes>
          <leafValues>
            -4.7724890708923340e-01 -4.3065819889307022e-02
            3.8945859670639038e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 662 -1.0047679534181952e-03 -1 -2 663
            -4.6660430729389191e-02</internalNodes>
          <leafValues>
            4.1553598642349243e-01 3.0159878730773926e-01
            -1.6184380650520325e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 664 3.2002381049096584e-03 -1 -2 665
            -1.7367519903928041e-03</internalNodes>
          <leafValues>
            -5.4621779918670654e-01 -2.1987779438495636e-01
            1.9606420397758484e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>33</maxWeakCount>
      <stageThreshold>-1.8088439702987671e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 1 666 1.7160519957542419e-02 -1 -2 667
            1.4503560028970242e-02</internalNodes>
          <leafValues>
            -3.2273009419441223e-01 -3.9438620209693909e-01
            5.7922977209091187e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 668 -9.0323518961668015e-03 -1 -2 669
            -6.9836131297051907e-03</internalNodes>
          <leafValues>
            -4.1536870598793030e-01 3.5515859723091125e-01
            -3.8177150487899780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 670 -1.9220909103751183e-02 -1 -2 671
            -4.0087159723043442e-02</internalNodes>
          <leafValues>
            4.5315900444984436e-01 1.7228379845619202e-01
            -3.1110560894012451e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 672 5.6549701839685440e-03 -1 -2 673
            -1.1611269786953926e-02</internalNodes>
          <leafValues>
            -4.0461608767509460e-01 2.9034239053726196e-01
            -2.2078509628772736e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 674 -1.0576159693300724e-03 -1 -2 675
            -1.3360800221562386e-03</internalNodes>
          <leafValues>
            3.5851669311523438e-01 1.5968900173902512e-02
            -4.1990101337432861e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 676 5.2302791737020016e-03 -1 -2 677
            -2.7848479803651571e-03</internalNodes>
          <leafValues>
            -4.9663281440734863e-01 -5.2960211038589478e-01
            1.5535449981689453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 678 -2.5654129683971405e-02 -1 -2 679
            -6.8942131474614143e-03</internalNodes>
          <leafValues>
            -5.9309178590774536e-01 2.4318109452724457e-01
            -1.8231940269470215e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 680 -6.9622750743292272e-05 -1 -2 681
            -6.4154611900448799e-03</internalNodes>
          <leafValues>
            -3.2716289162635803e-01 -5.0821667909622192e-01
            1.9543349742889404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 682 -6.7164386564400047e-05 -1 -2 683
            2.2416690364480019e-02</internalNodes>
          <leafValues>
            1.8602199852466583e-01 -3.9281991124153137e-01
            1.3279129564762115e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 684 8.4287580102682114e-03 -1 -2 685
            -8.7357551092281938e-04</internalNodes>
          <leafValues>
            -5.5447560548782349e-01 4.7158730030059814e-01
            -3.8492478430271149e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 686 -4.7496971092186868e-05 -1 -2 687
            4.5816078782081604e-03</internalNodes>
          <leafValues>
            -2.5197029113769531e-01 2.0250399410724640e-01
            -6.1638081073760986e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 688 -1.1175150051712990e-02 -1 -2 689
            -7.4238609522581100e-03</internalNodes>
          <leafValues>
            -2.7771198749542236e-01 -5.0103437900543213e-01
            1.9318529963493347e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 690 -3.0201480258256197e-03 -1 -2 691
            -3.0343679245561361e-03</internalNodes>
          <leafValues>
            -6.5904247760772705e-01 3.1962481141090393e-01
            -1.0512910038232803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 692 -1.0971290059387684e-02 -1 -2 693
            1.2000739661743864e-04</internalNodes>
          <leafValues>
            3.2707008719444275e-01 -4.1679269075393677e-01
            1.1645200103521347e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 694 2.1552699618041515e-03 -1 -2 695
            1.5970800304785371e-03</internalNodes>
          <leafValues>
            1.5389390289783478e-01 -4.2979270219802856e-01
            1.9192950427532196e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 696 -4.3590939603745937e-03 -1 -2 697
            -6.5752048976719379e-03</internalNodes>
          <leafValues>
            -8.6613738536834717e-01 3.5298541188240051e-01
            -7.2624720633029938e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 698 3.5486191045492887e-03 -1 -2 699
            1.7437560018151999e-03</internalNodes>
          <leafValues>
            -3.6141040921211243e-01 -4.0250919759273529e-02
            4.1119590401649475e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 700 6.5892767452169210e-05 -1 -2 701
            1.2217169627547264e-02</internalNodes>
          <leafValues>
            1.5523989498615265e-01 -3.6567229032516479e-01
            2.5159689784049988e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 702 6.0199309140443802e-02 -1 -2 703
            -9.1684371232986450e-02</internalNodes>
          <leafValues>
            -6.8959599733352661e-01 -6.6311872005462646e-01
            9.4827361404895782e-02</leafValues></_>
        <_>
          <internalNodes>
            1 0 704 8.9392811059951782e-04 -1 -2 705
            -1.1146500473842025e-03</internalNodes>
          <leafValues>
            2.8731009364128113e-01 3.6127060651779175e-01
            -2.4054229259490967e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 706 -1.1042780242860317e-02 -1 -2 707
            3.7769351154565811e-02</internalNodes>
          <leafValues>
            -7.1686691045761108e-01 1.1125349998474121e-01
            -5.6320947408676147e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 708 5.5979429744184017e-03 -1 -2 709
            -2.5462140329182148e-03</internalNodes>
          <leafValues>
            -5.6998908519744873e-01 2.6734578609466553e-01
            -1.0527700185775757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 710 -1.7929819878190756e-03 -1 -2 711
            -8.9686378487385809e-05</internalNodes>
          <leafValues>
            1.7712120711803436e-01 1.6762410104274750e-01
            -4.1336658596992493e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 712 -6.8254990037530661e-04 -1 -2 713
            4.0599349886178970e-03</internalNodes>
          <leafValues>
            -3.1327050924301147e-01 2.0312629640102386e-01
            -4.6360948681831360e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 714 1.5843180008232594e-03 -1 -2 715
            -4.6101640909910202e-02</internalNodes>
          <leafValues>
            2.6413089036941528e-01 2.4587640166282654e-01
            -3.1151199340820312e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 716 1.5759950038045645e-03 -1 -2 717
            3.5904631018638611e-02</internalNodes>
          <leafValues>
            -3.6593970656394958e-01 -1.3352620415389538e-02
            4.9500739574432373e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 718 1.9230529665946960e-02 -1 -2 719
            1.3461830094456673e-02</internalNodes>
          <leafValues>
            1.8603560328483582e-01 -4.2704311013221741e-01
            1.4756950736045837e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 720 6.3534970395267010e-03 -1 -2 721
            4.7998740337789059e-03</internalNodes>
          <leafValues>
            -5.8824592828750610e-01 1.3966129720211029e-01
            -3.6948320269584656e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 722 -9.7894563805311918e-04 -1 -2 723
            1.8534340197220445e-03</internalNodes>
          <leafValues>
            4.3156591057777405e-01 -1.9053110480308533e-01
            2.6868799328804016e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 724 5.5962381884455681e-04 -1 -2 725
            -8.1787789240479469e-03</internalNodes>
          <leafValues>
            -3.0545750260353088e-01 -7.2353351116180420e-01
            1.6197769343852997e-01</leafValues></_>
        <_>
          <internalNodes>
            1 0 726 -6.4591833506710827e-05 -1 -2 727
            -4.2282380163669586e-03</internalNodes>
          <leafValues>
            -1.6121749579906464e-01 4.2441681027412415e-01
            -1.1488209664821625e-01</leafValues></_>
        <_>
          <internalNodes>
            0 1 728 -3.2379399053752422e-03 -1 -2 729
            -4.7763898037374020e-03</internalNodes>
          <leafValues>
            -8.2811427116394043e-01 3.9157009124755859e-01
            -3.7677429616451263e-02</leafValues></_>
        <_>
          <internalNodes>
            0 1 730 -6.1182728968560696e-03 -1 -2 731
            3.1565790995955467e-03</internalNodes>
          <leafValues>
            3.0208829045295715e-01 -1.9045789539813995e-01
            3.0219689011573792e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          8 12 3 8 -1.</_>
        <_>
          8 16 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 8 9 -1.</_>
        <_>
          7 11 4 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 11 12 -1.</_>
        <_>
          8 11 11 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 7 8 -1.</_>
        <_>
          1 4 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 6 6 -1.</_>
        <_>
          7 9 6 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 7 4 -1.</_>
        <_>
          0 2 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 13 4 4 -1.</_>
        <_>
          18 13 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 15 2 3 -1.</_>
        <_>
          17 15 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 13 6 2 -1.</_>
        <_>
          2 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 6 6 -1.</_>
        <_>
          7 0 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 9 12 -1.</_>
        <_>
          8 11 3 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 4 10 -1.</_>
        <_>
          5 6 2 5 2.</_>
        <_>
          7 11 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 11 8 -1.</_>
        <_>
          8 16 11 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 8 -1.</_>
        <_>
          0 4 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 6 -1.</_>
        <_>
          3 0 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 14 6 6 -1.</_>
        <_>
          14 17 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 13 9 7 -1.</_>
        <_>
          8 13 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 6 3 -1.</_>
        <_>
          8 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 4 4 -1.</_>
        <_>
          0 2 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 3 3 -1.</_>
        <_>
          2 1 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          3 18 6 2 -1.</_>
        <_>
          3 19 6 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 4 2 -1.</_>
        <_>
          8 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 12 2 -1.</_>
        <_>
          6 11 12 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 3 1 -1.</_>
        <_>
          16 9 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 7 9 12 -1.</_>
        <_>
          8 11 3 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          16 13 1 6 -1.</_>
        <_>
          16 16 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 5 6 -1.</_>
        <_>
          7 9 5 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 12 4 6 -1.</_>
        <_>
          18 12 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 8 -1.</_>
        <_>
          0 4 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 15 12 -1.</_>
        <_>
          3 5 15 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 9 8 -1.</_>
        <_>
          11 16 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 12 9 -1.</_>
        <_>
          4 0 4 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 4 -1.</_>
        <_>
          2 12 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 18 4 2 -1.</_>
        <_>
          11 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 3 3 -1.</_>
        <_>
          6 2 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 3 2 -1.</_>
        <_>
          13 18 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 8 -1.</_>
        <_>
          1 0 1 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 18 4 2 -1.</_>
        <_>
          5 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 11 6 6 -1.</_>
        <_>
          17 11 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 8 4 -1.</_>
        <_>
          8 12 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 4 9 -1.</_>
        <_>
          9 9 4 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 9 4 7 -1.</_>
        <_>
          12 10 2 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 4 8 -1.</_>
        <_>
          5 8 2 4 2.</_>
        <_>
          7 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 11 8 -1.</_>
        <_>
          8 16 11 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 14 6 -1.</_>
        <_>
          3 3 14 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 6 12 -1.</_>
        <_>
          7 4 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 7 2 -1.</_>
        <_>
          0 19 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 12 4 3 -1.</_>
        <_>
          18 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 4 8 -1.</_>
        <_>
          2 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 4 1 -1.</_>
        <_>
          5 0 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 13 2 2 -1.</_>
        <_>
          3 13 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 16 19 4 -1.</_>
        <_>
          0 18 19 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 13 8 2 -1.</_>
        <_>
          11 13 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 4 1 -1.</_>
        <_>
          9 8 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 1 4 -1.</_>
        <_>
          0 3 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 4 -1.</_>
        <_>
          0 1 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 15 5 2 -1.</_>
        <_>
          15 16 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 3 2 -1.</_>
        <_>
          8 18 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 7 3 8 -1.</_>
        <_>
          11 9 3 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 12 2 8 -1.</_>
        <_>
          15 16 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 10 6 -1.</_>
        <_>
          2 3 10 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 18 15 -1.</_>
        <_>
          6 10 6 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 12 6 -1.</_>
        <_>
          7 13 4 2 9.</_></rects></_>
    <_>
      <rects>
        <_>
          16 12 4 7 -1.</_>
        <_>
          18 12 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 4 2 -1.</_>
        <_>
          9 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 4 3 -1.</_>
        <_>
          9 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 6 -1.</_>
        <_>
          2 12 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 16 4 4 -1.</_>
        <_>
          5 16 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 4 6 -1.</_>
        <_>
          4 0 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 4 7 -1.</_>
        <_>
          2 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 8 3 -1.</_>
        <_>
          6 0 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 4 6 -1.</_>
        <_>
          9 3 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 3 2 -1.</_>
        <_>
          10 11 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 3 7 6 -1.</_>
        <_>
          4 6 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 18 10 2 -1.</_>
        <_>
          15 18 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 6 1 -1.</_>
        <_>
          9 13 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 8 4 6 -1.</_>
        <_>
          8 10 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 12 6 8 -1.</_>
        <_>
          14 16 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 6 4 -1.</_>
        <_>
          12 10 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 12 6 3 -1.</_>
        <_>
          2 12 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 11 2 6 -1.</_>
        <_>
          19 11 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 10 -1.</_>
        <_>
          0 5 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 8 12 -1.</_>
        <_>
          7 4 4 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 9 8 -1.</_>
        <_>
          4 3 3 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 2 -1.</_>
        <_>
          0 1 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 6 12 -1.</_>
        <_>
          14 12 2 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          4 2 14 6 -1.</_>
        <_>
          4 4 14 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 12 8 -1.</_>
        <_>
          3 4 12 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 17 20 -1.</_>
        <_>
          0 5 17 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 13 6 -1.</_>
        <_>
          4 2 13 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 3 6 -1.</_>
        <_>
          3 10 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 14 6 4 -1.</_>
        <_>
          4 14 3 2 2.</_>
        <_>
          7 16 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 6 8 -1.</_>
        <_>
          10 1 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 2 6 -1.</_>
        <_>
          1 1 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 1 3 -1.</_>
        <_>
          7 13 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 4 8 4 -1.</_>
        <_>
          5 4 8 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 2 4 5 -1.</_>
        <_>
          1 2 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 2 -1.</_>
        <_>
          6 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 13 8 2 -1.</_>
        <_>
          7 13 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 9 9 8 -1.</_>
        <_>
          11 11 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 12 4 3 -1.</_>
        <_>
          18 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 14 4 6 -1.</_>
        <_>
          16 17 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 3 -1.</_>
        <_>
          2 12 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 7 6 -1.</_>
        <_>
          6 8 7 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 1 6 -1.</_>
        <_>
          0 3 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 15 5 -1.</_>
        <_>
          5 2 5 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 10 3 -1.</_>
        <_>
          13 11 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 2 8 -1.</_>
        <_>
          8 15 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 2 6 -1.</_>
        <_>
          1 1 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 4 4 -1.</_>
        <_>
          1 1 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 3 1 -1.</_>
        <_>
          6 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 0 7 15 -1.</_>
        <_>
          5 5 7 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 2 -1.</_>
        <_>
          18 1 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 18 6 2 -1.</_>
        <_>
          6 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 5 -1.</_>
        <_>
          7 1 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 0 6 8 -1.</_>
        <_>
          14 0 3 4 2.</_>
        <_>
          17 4 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 4 18 -1.</_>
        <_>
          5 2 2 9 2.</_>
        <_>
          7 11 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 6 2 -1.</_>
        <_>
          9 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 2 3 -1.</_>
        <_>
          10 9 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 4 2 -1.</_>
        <_>
          10 10 2 1 2.</_>
        <_>
          12 11 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 2 12 6 -1.</_>
        <_>
          4 4 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 12 8 -1.</_>
        <_>
          5 3 12 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 4 2 -1.</_>
        <_>
          2 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 8 1 -1.</_>
        <_>
          4 18 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 12 12 -1.</_>
        <_>
          8 11 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          16 11 4 6 -1.</_>
        <_>
          18 11 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 6 7 -1.</_>
        <_>
          8 13 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 8 -1.</_>
        <_>
          0 4 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 14 5 6 -1.</_>
        <_>
          15 17 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 6 9 -1.</_>
        <_>
          2 7 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 4 1 -1.</_>
        <_>
          16 12 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 11 8 2 -1.</_>
        <_>
          15 11 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 12 11 -1.</_>
        <_>
          3 1 6 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 6 4 -1.</_>
        <_>
          7 9 6 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 17 6 3 -1.</_>
        <_>
          8 17 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 4 -1.</_>
        <_>
          0 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 1 3 -1.</_>
        <_>
          2 2 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 11 2 3 -1.</_>
        <_>
          18 12 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 2 8 -1.</_>
        <_>
          3 12 1 4 2.</_>
        <_>
          4 16 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 3 3 -1.</_>
        <_>
          4 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 18 4 2 -1.</_>
        <_>
          12 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 10 3 3 -1.</_>
        <_>
          17 11 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 14 5 2 -1.</_>
        <_>
          7 15 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 4 5 -1.</_>
        <_>
          6 0 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 1 5 8 -1.</_>
        <_>
          6 5 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 9 8 -1.</_>
        <_>
          3 5 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 15 6 -1.</_>
        <_>
          7 14 5 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 6 5 -1.</_>
        <_>
          14 3 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 2 2 -1.</_>
        <_>
          5 16 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 16 2 2 -1.</_>
        <_>
          5 16 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 8 6 4 -1.</_>
        <_>
          11 10 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 4 -1.</_>
        <_>
          4 13 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 6 12 -1.</_>
        <_>
          15 12 2 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 10 -1.</_>
        <_>
          0 5 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 4 -1.</_>
        <_>
          2 12 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 8 6 -1.</_>
        <_>
          5 7 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 1 16 4 -1.</_>
        <_>
          3 3 16 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 10 9 -1.</_>
        <_>
          6 5 10 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 6 10 -1.</_>
        <_>
          17 10 3 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 4 3 -1.</_>
        <_>
          6 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 2 -1.</_>
        <_>
          6 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 2 -1.</_>
        <_>
          6 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 9 -1.</_>
        <_>
          1 0 1 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 3 2 -1.</_>
        <_>
          2 6 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 16 6 3 -1.</_>
        <_>
          9 16 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 6 2 -1.</_>
        <_>
          9 17 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 3 9 6 -1.</_>
        <_>
          4 5 9 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 15 3 2 -1.</_>
        <_>
          7 16 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 2 3 3 -1.</_>
        <_>
          7 2 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 6 4 -1.</_>
        <_>
          4 1 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 4 2 -1.</_>
        <_>
          13 11 2 1 2.</_>
        <_>
          15 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          14 10 1 1 2.</_>
        <_>
          15 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 7 3 3 -1.</_>
        <_>
          18 8 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          18 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 3 1 2 -1.</_>
        <_>
          0 4 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 1 2 5 -1.</_>
        <_>
          11 1 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 3 12 -1.</_>
        <_>
          1 11 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 8 2 -1.</_>
        <_>
          2 10 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 12 3 3 -1.</_>
        <_>
          7 13 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 3 4 -1.</_>
        <_>
          7 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 4 2 -1.</_>
        <_>
          6 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 20 1 -1.</_>
        <_>
          10 19 10 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 8 5 -1.</_>
        <_>
          7 11 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 8 9 -1.</_>
        <_>
          10 11 8 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 6 2 -1.</_>
        <_>
          2 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 14 2 1 -1.</_>
        <_>
          18 14 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 2 2 4 -1.</_>
        <_>
          2 2 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 8 5 -1.</_>
        <_>
          9 5 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 13 5 4 -1.</_>
        <_>
          7 15 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 18 3 2 -1.</_>
        <_>
          17 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 1 2 -1.</_>
        <_>
          0 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 1 3 -1.</_>
        <_>
          2 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 11 3 4 -1.</_>
        <_>
          11 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 11 4 8 -1.</_>
        <_>
          16 11 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 9 6 -1.</_>
        <_>
          2 5 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 17 8 -1.</_>
        <_>
          0 6 17 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 17 5 3 -1.</_>
        <_>
          15 18 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 2 8 -1.</_>
        <_>
          2 15 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 3 3 -1.</_>
        <_>
          4 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 9 7 -1.</_>
        <_>
          6 12 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 1 4 7 -1.</_>
        <_>
          14 1 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 16 2 2 -1.</_>
        <_>
          3 16 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 17 2 1 -1.</_>
        <_>
          3 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 9 6 6 -1.</_>
        <_>
          4 9 3 3 2.</_>
        <_>
          7 12 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 3 1 -1.</_>
        <_>
          12 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 20 3 -1.</_>
        <_>
          5 0 10 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 2 -1.</_>
        <_>
          0 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 1 -1.</_>
        <_>
          18 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 0 8 9 -1.</_>
        <_>
          4 3 8 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 4 -1.</_>
        <_>
          6 2 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 1 -1.</_>
        <_>
          18 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 2 6 1 -1.</_>
        <_>
          17 2 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 13 8 2 -1.</_>
        <_>
          7 13 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 12 3 8 -1.</_>
        <_>
          15 16 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 8 3 -1.</_>
        <_>
          5 11 8 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 11 9 -1.</_>
        <_>
          5 3 11 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 14 2 2 -1.</_>
        <_>
          19 14 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 9 8 -1.</_>
        <_>
          4 3 3 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 2 3 -1.</_>
        <_>
          2 7 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 6 2 3 -1.</_>
        <_>
          2 7 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 12 -1.</_>
        <_>
          13 11 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 1 15 -1.</_>
        <_>
          0 5 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 6 3 -1.</_>
        <_>
          6 10 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 18 3 2 -1.</_>
        <_>
          3 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 17 4 3 -1.</_>
        <_>
          16 18 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 17 4 3 -1.</_>
        <_>
          11 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 13 4 3 -1.</_>
        <_>
          14 13 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 15 3 2 -1.</_>
        <_>
          5 16 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 4 2 2 -1.</_>
        <_>
          1 4 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 2 5 -1.</_>
        <_>
          5 0 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 3 8 -1.</_>
        <_>
          1 11 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 1 3 -1.</_>
        <_>
          4 9 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 13 2 1 -1.</_>
        <_>
          5 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 4 9 -1.</_>
        <_>
          11 11 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 1 2 -1.</_>
        <_>
          0 2 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 3 -1.</_>
        <_>
          0 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 1 4 -1.</_>
        <_>
          12 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 10 3 3 -1.</_>
        <_>
          15 11 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 12 1 6 -1.</_>
        <_>
          18 12 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 17 3 2 -1.</_>
        <_>
          5 17 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          18 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 9 2 1 -1.</_>
        <_>
          18 9 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 11 4 5 -1.</_>
        <_>
          9 12 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 1 2 7 -1.</_>
        <_>
          8 1 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 4 14 6 -1.</_>
        <_>
          4 6 14 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 11 6 -1.</_>
        <_>
          2 5 11 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 16 2 2 -1.</_>
        <_>
          18 17 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 11 2 6 -1.</_>
        <_>
          18 11 1 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 3 -1.</_>
        <_>
          18 1 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 2 6 -1.</_>
        <_>
          18 3 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 6 8 -1.</_>
        <_>
          4 7 3 4 2.</_>
        <_>
          7 11 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 11 4 2 -1.</_>
        <_>
          11 11 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 6 7 -1.</_>
        <_>
          3 0 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 5 8 -1.</_>
        <_>
          15 12 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 3 8 -1.</_>
        <_>
          3 10 1 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 6 6 -1.</_>
        <_>
          7 9 6 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 1 6 6 -1.</_>
        <_>
          4 4 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 16 2 -1.</_>
        <_>
          4 1 16 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 8 6 6 -1.</_>
        <_>
          14 8 3 3 2.</_>
        <_>
          17 11 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 12 2 8 -1.</_>
        <_>
          4 12 1 4 2.</_>
        <_>
          5 16 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 7 2 -1.</_>
        <_>
          0 19 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 1 4 -1.</_>
        <_>
          9 15 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 10 2 8 -1.</_>
        <_>
          19 10 1 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 4 8 -1.</_>
        <_>
          7 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 6 6 -1.</_>
        <_>
          3 2 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 8 2 -1.</_>
        <_>
          10 10 4 1 2.</_>
        <_>
          14 11 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 2 3 -1.</_>
        <_>
          2 10 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 1 13 6 -1.</_>
        <_>
          5 3 13 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 4 13 6 -1.</_>
        <_>
          4 6 13 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 4 5 -1.</_>
        <_>
          8 1 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 2 1 -1.</_>
        <_>
          8 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 4 4 -1.</_>
        <_>
          6 5 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 4 2 -1.</_>
        <_>
          14 12 2 1 2.</_>
        <_>
          16 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 4 2 -1.</_>
        <_>
          13 11 2 1 2.</_>
        <_>
          15 12 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 10 4 3 -1.</_>
        <_>
          16 11 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 4 5 -1.</_>
        <_>
          11 0 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 1 3 -1.</_>
        <_>
          7 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 12 3 2 -1.</_>
        <_>
          7 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 8 2 3 -1.</_>
        <_>
          17 8 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 0 6 5 -1.</_>
        <_>
          13 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 3 3 -1.</_>
        <_>
          0 1 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 1 2 -1.</_>
        <_>
          2 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 7 2 -1.</_>
        <_>
          13 12 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 8 3 3 -1.</_>
        <_>
          18 9 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 15 1 3 -1.</_>
        <_>
          14 16 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 13 6 2 -1.</_>
        <_>
          8 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 10 3 4 -1.</_>
        <_>
          9 10 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 12 19 -1.</_>
        <_>
          13 0 6 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 8 4 -1.</_>
        <_>
          12 18 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 12 2 -1.</_>
        <_>
          14 5 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 6 4 -1.</_>
        <_>
          12 10 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 4 -1.</_>
        <_>
          4 13 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 12 7 -1.</_>
        <_>
          3 2 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 4 2 -1.</_>
        <_>
          8 0 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 11 6 6 -1.</_>
        <_>
          15 13 2 2 9.</_></rects></_>
    <_>
      <rects>
        <_>
          7 11 10 4 -1.</_>
        <_>
          12 11 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 4 5 -1.</_>
        <_>
          2 11 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 4 2 -1.</_>
        <_>
          3 15 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 1 6 -1.</_>
        <_>
          0 3 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 6 6 -1.</_>
        <_>
          6 5 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 4 2 -1.</_>
        <_>
          7 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 4 2 -1.</_>
        <_>
          7 18 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 4 7 4 -1.</_>
        <_>
          3 5 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 8 12 -1.</_>
        <_>
          7 8 4 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 2 1 -1.</_>
        <_>
          5 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 18 2 1 -1.</_>
        <_>
          5 18 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 16 7 2 -1.</_>
        <_>
          13 17 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 15 2 3 -1.</_>
        <_>
          7 15 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 2 4 5 -1.</_>
        <_>
          10 2 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 4 6 -1.</_>
        <_>
          8 2 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 3 3 -1.</_>
        <_>
          4 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 3 -1.</_>
        <_>
          6 13 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          4 12 3 2 -1.</_>
        <_>
          5 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 13 3 1 -1.</_>
        <_>
          11 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 4 3 -1.</_>
        <_>
          12 5 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 7 1 10 -1.</_>
        <_>
          19 12 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 2 3 -1.</_>
        <_>
          3 9 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 6 5 -1.</_>
        <_>
          9 0 2 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 6 2 -1.</_>
        <_>
          5 0 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 0 13 9 -1.</_>
        <_>
          5 3 13 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 1 2 -1.</_>
        <_>
          0 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 6 -1.</_>
        <_>
          1 2 16 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 4 -1.</_>
        <_>
          18 0 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 13 2 2 -1.</_>
        <_>
          4 13 1 1 2.</_>
        <_>
          5 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 4 1 -1.</_>
        <_>
          2 3 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 8 12 -1.</_>
        <_>
          3 6 8 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 4 1 -1.</_>
        <_>
          13 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 2 2 -1.</_>
        <_>
          12 12 1 1 2.</_>
        <_>
          13 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 3 1 -1.</_>
        <_>
          6 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 13 8 4 -1.</_>
        <_>
          3 13 4 2 2.</_>
        <_>
          7 15 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 18 3 -1.</_>
        <_>
          6 9 6 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 6 5 -1.</_>
        <_>
          11 4 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 14 9 1 -1.</_>
        <_>
          8 14 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 4 4 -1.</_>
        <_>
          4 0 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 9 12 8 -1.</_>
        <_>
          7 11 12 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 15 2 1 -1.</_>
        <_>
          18 15 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 13 2 4 -1.</_>
        <_>
          3 13 1 2 2.</_>
        <_>
          4 15 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 3 3 -1.</_>
        <_>
          3 8 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 1 2 7 -1.</_>
        <_>
          1 1 1 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 3 9 -1.</_>
        <_>
          5 0 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 3 3 -1.</_>
        <_>
          14 11 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 11 2 2 -1.</_>
        <_>
          12 11 1 1 2.</_>
        <_>
          13 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 4 -1.</_>
        <_>
          0 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 8 2 -1.</_>
        <_>
          12 19 8 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 9 2 2 -1.</_>
        <_>
          17 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 10 4 2 -1.</_>
        <_>
          17 11 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 13 10 1 -1.</_>
        <_>
          12 13 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 4 3 -1.</_>
        <_>
          9 7 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 6 2 -1.</_>
        <_>
          11 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 6 2 -1.</_>
        <_>
          10 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 9 3 1 -1.</_>
        <_>
          18 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 11 -1.</_>
        <_>
          18 7 1 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 4 4 -1.</_>
        <_>
          8 2 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 6 2 3 -1.</_>
        <_>
          7 6 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 9 5 -1.</_>
        <_>
          10 3 3 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 0 15 9 -1.</_>
        <_>
          6 3 5 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 4 3 -1.</_>
        <_>
          3 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 5 -1.</_>
        <_>
          1 12 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 2 3 -1.</_>
        <_>
          2 3 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 13 6 1 -1.</_>
        <_>
          4 13 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 0 4 6 -1.</_>
        <_>
          6 0 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 2 1 -1.</_>
        <_>
          2 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 9 1 3 -1.</_>
        <_>
          3 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 2 6 9 -1.</_>
        <_>
          2 2 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 2 2 -1.</_>
        <_>
          16 7 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 2 6 4 -1.</_>
        <_>
          9 2 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 6 2 -1.</_>
        <_>
          9 18 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 14 6 4 -1.</_>
        <_>
          3 14 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 7 3 -1.</_>
        <_>
          5 9 7 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 12 4 1 -1.</_>
        <_>
          15 13 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 2 -1.</_>
        <_>
          5 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 3 -1.</_>
        <_>
          6 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 2 2 2 -1.</_>
        <_>
          19 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 1 -1.</_>
        <_>
          17 0 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 3 -1.</_>
        <_>
          18 1 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 4 6 8 -1.</_>
        <_>
          13 4 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 3 2 -1.</_>
        <_>
          8 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 3 2 -1.</_>
        <_>
          16 1 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 9 4 -1.</_>
        <_>
          8 11 3 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 1 6 -1.</_>
        <_>
          12 11 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 4 4 -1.</_>
        <_>
          4 0 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 1 11 12 -1.</_>
        <_>
          5 5 11 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 12 4 8 -1.</_>
        <_>
          18 12 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 14 2 6 -1.</_>
        <_>
          18 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 4 4 -1.</_>
        <_>
          2 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 6 4 -1.</_>
        <_>
          5 8 6 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 15 3 2 -1.</_>
        <_>
          6 16 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 16 3 1 -1.</_>
        <_>
          7 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 14 1 2 -1.</_>
        <_>
          10 14 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 7 3 3 -1.</_>
        <_>
          3 8 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 0 6 8 -1.</_>
        <_>
          4 0 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 5 6 3 -1.</_>
        <_>
          4 5 2 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 3 6 -1.</_>
        <_>
          4 11 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 2 3 -1.</_>
        <_>
          14 12 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 17 4 3 -1.</_>
        <_>
          12 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 2 2 -1.</_>
        <_>
          13 11 1 1 2.</_>
        <_>
          14 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 2 2 -1.</_>
        <_>
          13 11 1 1 2.</_>
        <_>
          14 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 5 6 -1.</_>
        <_>
          8 5 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 2 -1.</_>
        <_>
          0 1 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 10 4 -1.</_>
        <_>
          0 10 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 11 3 1 -1.</_>
        <_>
          18 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 18 2 2 -1.</_>
        <_>
          8 18 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 18 4 -1.</_>
        <_>
          9 6 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 12 8 -1.</_>
        <_>
          6 12 4 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 14 1 -1.</_>
        <_>
          8 0 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 12 19 -1.</_>
        <_>
          14 0 6 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 3 2 -1.</_>
        <_>
          8 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 5 -1.</_>
        <_>
          9 11 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 3 2 -1.</_>
        <_>
          8 18 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 13 2 2 -1.</_>
        <_>
          5 13 1 1 2.</_>
        <_>
          6 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 9 3 1 -1.</_>
        <_>
          17 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 2 3 -1.</_>
        <_>
          18 0 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 2 15 6 -1.</_>
        <_>
          4 4 15 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 10 4 -1.</_>
        <_>
          10 0 5 2 2.</_>
        <_>
          15 2 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 12 6 -1.</_>
        <_>
          5 2 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 1 8 6 -1.</_>
        <_>
          12 1 4 3 2.</_>
        <_>
          16 4 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 2 1 -1.</_>
        <_>
          1 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 2 4 -1.</_>
        <_>
          16 7 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 17 5 3 -1.</_>
        <_>
          15 18 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 6 8 -1.</_>
        <_>
          8 12 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 2 2 -1.</_>
        <_>
          6 12 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 4 6 -1.</_>
        <_>
          14 12 2 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 4 -1.</_>
        <_>
          18 1 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 0 4 10 -1.</_>
        <_>
          5 0 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 3 -1.</_>
        <_>
          6 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 3 3 -1.</_>
        <_>
          12 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 1 3 -1.</_>
        <_>
          2 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 1 8 1 -1.</_>
        <_>
          4 1 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 18 12 -1.</_>
        <_>
          6 7 6 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 6 2 -1.</_>
        <_>
          15 18 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 9 4 7 -1.</_>
        <_>
          12 10 2 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 8 3 12 -1.</_>
        <_>
          16 12 1 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 7 3 -1.</_>
        <_>
          6 11 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 10 3 -1.</_>
        <_>
          4 10 10 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 15 7 -1.</_>
        <_>
          5 1 5 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 18 -1.</_>
        <_>
          0 6 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 2 4 -1.</_>
        <_>
          8 14 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 16 4 4 -1.</_>
        <_>
          16 18 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 10 4 8 -1.</_>
        <_>
          2 10 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 3 2 -1.</_>
        <_>
          3 16 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 17 2 1 -1.</_>
        <_>
          2 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 8 -1.</_>
        <_>
          18 10 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 11 18 3 -1.</_>
        <_>
          6 12 6 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 4 2 -1.</_>
        <_>
          16 11 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 1 5 4 -1.</_>
        <_>
          9 3 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 7 6 -1.</_>
        <_>
          6 4 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 3 8 6 -1.</_>
        <_>
          3 6 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 1 4 2 -1.</_>
        <_>
          18 1 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 12 2 3 -1.</_>
        <_>
          18 13 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 6 2 8 -1.</_>
        <_>
          17 6 1 4 2.</_>
        <_>
          18 10 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 5 3 4 -1.</_>
        <_>
          18 6 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 9 4 8 -1.</_>
        <_>
          0 11 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 3 8 -1.</_>
        <_>
          0 10 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 11 2 2 -1.</_>
        <_>
          14 11 1 1 2.</_>
        <_>
          15 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 3 3 -1.</_>
        <_>
          14 12 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 12 5 2 -1.</_>
        <_>
          14 13 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 12 1 2 -1.</_>
        <_>
          19 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 4 7 -1.</_>
        <_>
          7 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 3 2 -1.</_>
        <_>
          12 13 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 4 2 -1.</_>
        <_>
          12 13 2 1 2.</_>
        <_>
          14 14 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 18 4 2 -1.</_>
        <_>
          16 19 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 18 1 2 -1.</_>
        <_>
          14 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 0 3 2 -1.</_>
        <_>
          17 1 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 0 4 2 -1.</_>
        <_>
          17 1 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 13 2 2 -1.</_>
        <_>
          12 13 1 1 2.</_>
        <_>
          13 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 10 4 2 -1.</_>
        <_>
          7 10 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 3 1 3 -1.</_>
        <_>
          2 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 4 2 3 -1.</_>
        <_>
          2 5 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 16 6 -1.</_>
        <_>
          3 2 16 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 2 2 5 -1.</_>
        <_>
          12 2 1 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 0 1 3 -1.</_>
        <_>
          3 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 12 2 2 -1.</_>
        <_>
          13 12 1 1 2.</_>
        <_>
          14 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 4 3 -1.</_>
        <_>
          6 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 13 3 3 -1.</_>
        <_>
          17 14 3 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 2 8 -1.</_>
        <_>
          0 12 1 4 2.</_>
        <_>
          1 16 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 16 1 3 -1.</_>
        <_>
          3 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 2 1 2 -1.</_>
        <_>
          0 3 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 4 7 -1.</_>
        <_>
          11 2 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 6 9 -1.</_>
        <_>
          2 4 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 2 2 -1.</_>
        <_>
          2 4 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 2 2 -1.</_>
        <_>
          13 12 1 1 2.</_>
        <_>
          14 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 1 -1.</_>
        <_>
          19 0 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 13 3 1 -1.</_>
        <_>
          5 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 4 1 -1.</_>
        <_>
          7 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 6 3 -1.</_>
        <_>
          6 11 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 4 3 -1.</_>
        <_>
          7 10 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 4 3 -1.</_>
        <_>
          6 0 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 15 5 2 -1.</_>
        <_>
          15 16 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 18 12 -1.</_>
        <_>
          6 12 6 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 14 4 -1.</_>
        <_>
          8 6 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 6 3 -1.</_>
        <_>
          2 12 6 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 9 1 3 -1.</_>
        <_>
          4 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 10 3 3 -1.</_>
        <_>
          18 11 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 11 1 4 -1.</_>
        <_>
          16 12 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 0 12 9 -1.</_>
        <_>
          4 0 6 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 4 5 -1.</_>
        <_>
          10 3 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 6 3 -1.</_>
        <_>
          7 9 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 9 6 -1.</_>
        <_>
          7 3 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 2 2 -1.</_>
        <_>
          0 2 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 3 5 -1.</_>
        <_>
          14 9 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 16 3 1 -1.</_>
        <_>
          4 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 1 4 7 -1.</_>
        <_>
          12 1 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 2 2 -1.</_>
        <_>
          11 13 1 1 2.</_>
        <_>
          12 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 14 3 1 -1.</_>
        <_>
          13 14 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 3 1 -1.</_>
        <_>
          18 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 2 6 6 -1.</_>
        <_>
          14 2 3 3 2.</_>
        <_>
          17 5 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 8 4 -1.</_>
        <_>
          12 18 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 11 3 3 -1.</_>
        <_>
          6 12 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 8 6 -1.</_>
        <_>
          4 5 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 8 3 8 -1.</_>
        <_>
          1 10 3 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 8 6 -1.</_>
        <_>
          9 2 4 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 2 7 6 -1.</_>
        <_>
          5 5 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 13 3 1 -1.</_>
        <_>
          11 13 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 4 2 -1.</_>
        <_>
          12 12 2 1 2.</_>
        <_>
          14 13 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 14 19 -1.</_>
        <_>
          13 1 7 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 14 1 -1.</_>
        <_>
          13 9 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 0 2 1 -1.</_>
        <_>
          18 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 0 3 1 -1.</_>
        <_>
          16 1 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 7 2 3 -1.</_>
        <_>
          4 8 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 12 3 3 -1.</_>
        <_>
          14 13 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 17 4 2 -1.</_>
        <_>
          11 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 3 3 -1.</_>
        <_>
          9 13 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 7 6 -1.</_>
        <_>
          4 3 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 6 6 -1.</_>
        <_>
          11 2 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 1 4 -1.</_>
        <_>
          0 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 4 4 -1.</_>
        <_>
          8 5 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 1 3 -1.</_>
        <_>
          1 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 4 2 -1.</_>
        <_>
          9 4 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 13 2 5 -1.</_>
        <_>
          19 13 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 3 6 -1.</_>
        <_>
          3 11 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 2 12 -1.</_>
        <_>
          0 9 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 10 8 5 -1.</_>
        <_>
          15 10 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 4 2 -1.</_>
        <_>
          16 12 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 8 4 2 -1.</_>
        <_>
          16 9 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 13 2 1 -1.</_>
        <_>
          6 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 2 2 -1.</_>
        <_>
          13 13 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 8 8 -1.</_>
        <_>
          13 12 4 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 6 10 -1.</_>
        <_>
          5 0 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 14 2 2 -1.</_>
        <_>
          6 14 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 5 19 4 -1.</_>
        <_>
          0 7 19 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 4 3 2 -1.</_>
        <_>
          18 5 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 4 -1.</_>
        <_>
          18 4 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 13 8 2 -1.</_>
        <_>
          7 13 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 2 8 -1.</_>
        <_>
          0 4 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 15 6 -1.</_>
        <_>
          0 11 15 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 14 2 1 -1.</_>
        <_>
          18 14 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 4 8 -1.</_>
        <_>
          2 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 6 2 -1.</_>
        <_>
          2 13 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 18 3 2 -1.</_>
        <_>
          3 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 15 6 -1.</_>
        <_>
          7 13 5 2 9.</_></rects></_>
    <_>
      <rects>
        <_>
          7 14 3 3 -1.</_>
        <_>
          8 15 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          8 8 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 6 3 -1.</_>
        <_>
          6 10 6 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 7 3 -1.</_>
        <_>
          5 9 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          17 9 3 1 -1.</_>
        <_>
          18 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 9 3 2 -1.</_>
        <_>
          18 10 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 9 1 3 -1.</_>
        <_>
          11 10 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 2 2 -1.</_>
        <_>
          12 11 1 1 2.</_>
        <_>
          13 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 4 5 -1.</_>
        <_>
          4 6 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 4 3 -1.</_>
        <_>
          6 6 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 1 6 -1.</_>
        <_>
          0 5 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 2 2 -1.</_>
        <_>
          14 12 1 1 2.</_>
        <_>
          15 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 16 3 3 -1.</_>
        <_>
          4 16 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 14 4 -1.</_>
        <_>
          3 3 14 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 14 8 -1.</_>
        <_>
          6 0 7 4 2.</_>
        <_>
          13 4 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 4 8 -1.</_>
        <_>
          4 2 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 8 1 -1.</_>
        <_>
          13 0 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 1 6 1 -1.</_>
        <_>
          17 1 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          18 18 2 2 -1.</_>
        <_>
          18 19 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 2 2 -1.</_>
        <_>
          5 16 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 8 11 3 -1.</_>
        <_>
          2 9 11 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 2 3 -1.</_>
        <_>
          1 9 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          18 12 2 5 -1.</_>
        <_>
          19 12 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 16 1 3 -1.</_>
        <_>
          18 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 2 -1.</_>
        <_>
          14 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 11 2 2 -1.</_>
        <_>
          13 11 1 1 2.</_>
        <_>
          14 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 4 4 -1.</_>
        <_>
          14 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 11 1 3 -1.</_>
        <_>
          19 12 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 1 4 -1.</_>
        <_>
          0 3 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 20 20 -1.</_>
        <_>
          0 0 10 10 2.</_>
        <_>
          10 10 10 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 3 3 -1.</_>
        <_>
          10 13 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 17 1 2 -1.</_>
        <_>
          16 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 10 4 2 -1.</_>
        <_>
          13 10 2 1 2.</_>
        <_>
          15 11 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 2 2 -1.</_>
        <_>
          15 11 1 1 2.</_>
        <_>
          16 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 3 6 -1.</_>
        <_>
          3 10 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 9 -1.</_>
        <_>
          2 0 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 2 1 -1.</_>
        <_>
          8 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 18 8 1 -1.</_>
        <_>
          8 18 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 1 4 -1.</_>
        <_>
          3 12 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 11 3 3 -1.</_>
        <_>
          6 12 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 18 4 1 -1.</_>
        <_>
          10 18 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 2 1 -1.</_>
        <_>
          1 19 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 3 5 -1.</_>
        <_>
          12 6 1 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 12 20 -1.</_>
        <_>
          8 0 6 10 2.</_>
        <_>
          14 10 6 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 1 4 -1.</_>
        <_>
          3 1 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 14 16 4 -1.</_>
        <_>
          8 14 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 5 4 -1.</_>
        <_>
          6 10 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 12 6 2 -1.</_>
        <_>
          5 12 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 14 4 1 -1.</_>
        <_>
          1 14 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 1 3 -1.</_>
        <_>
          3 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 9 -1.</_>
        <_>
          4 10 1 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 3 4 -1.</_>
        <_>
          5 11 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 3 2 -1.</_>
        <_>
          6 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 3 2 -1.</_>
        <_>
          8 12 1 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 12 6 -1.</_>
        <_>
          5 2 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 8 3 -1.</_>
        <_>
          11 2 4 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 1 6 2 -1.</_>
        <_>
          8 1 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 4 15 9 -1.</_>
        <_>
          4 7 15 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 8 6 -1.</_>
        <_>
          7 10 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 9 9 -1.</_>
        <_>
          11 11 9 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 4 -1.</_>
        <_>
          9 2 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 11 6 3 -1.</_>
        <_>
          2 12 6 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 12 4 3 -1.</_>
        <_>
          18 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 2 10 -1.</_>
        <_>
          10 15 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 3 4 -1.</_>
        <_>
          4 8 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 9 6 1 -1.</_>
        <_>
          3 11 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 0 1 6 -1.</_>
        <_>
          0 3 1 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 10 10 2 -1.</_>
        <_>
          8 10 5 1 2.</_>
        <_>
          13 11 5 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 5 6 -1.</_>
        <_>
          5 5 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 6 1 -1.</_>
        <_>
          6 1 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 3 1 12 -1.</_>
        <_>
          0 7 1 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 2 1 -1.</_>
        <_>
          1 7 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 1 3 -1.</_>
        <_>
          2 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 12 2 3 -1.</_>
        <_>
          10 13 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 12 3 3 -1.</_>
        <_>
          11 12 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 3 3 -1.</_>
        <_>
          10 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 4 2 -1.</_>
        <_>
          7 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 6 2 -1.</_>
        <_>
          15 18 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 2 1 -1.</_>
        <_>
          3 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 15 4 1 -1.</_>
        <_>
          2 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 2 2 -1.</_>
        <_>
          18 1 2 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          19 0 1 3 -1.</_>
        <_>
          19 1 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 11 3 2 -1.</_>
        <_>
          16 11 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 12 2 3 -1.</_>
        <_>
          15 13 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 0 8 1 -1.</_>
        <_>
          16 0 4 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 9 6 -1.</_>
        <_>
          2 4 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 1 3 2 -1.</_>
        <_>
          17 1 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 5 6 4 -1.</_>
        <_>
          7 6 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 6 2 -1.</_>
        <_>
          7 6 3 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 4 6 6 -1.</_>
        <_>
          13 4 2 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 9 3 -1.</_>
        <_>
          5 8 9 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 9 3 -1.</_>
        <_>
          5 9 9 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 4 3 -1.</_>
        <_>
          2 0 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 9 5 4 -1.</_>
        <_>
          9 10 5 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 7 -1.</_>
        <_>
          3 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 9 3 2 -1.</_>
        <_>
          17 10 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 12 2 2 -1.</_>
        <_>
          14 12 1 1 2.</_>
        <_>
          15 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 14 1 -1.</_>
        <_>
          7 0 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 2 2 -1.</_>
        <_>
          15 11 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 14 12 4 -1.</_>
        <_>
          3 14 6 2 2.</_>
        <_>
          9 16 6 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 1 3 -1.</_>
        <_>
          4 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 12 3 2 -1.</_>
        <_>
          9 13 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 11 2 2 -1.</_>
        <_>
          14 11 1 1 2.</_>
        <_>
          15 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 7 2 -1.</_>
        <_>
          13 11 7 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 13 1 2 -1.</_>
        <_>
          7 13 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 12 4 3 -1.</_>
        <_>
          6 12 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 2 5 -1.</_>
        <_>
          9 2 1 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 4 2 -1.</_>
        <_>
          3 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 17 4 3 -1.</_>
        <_>
          13 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 16 5 3 -1.</_>
        <_>
          15 17 5 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 16 4 3 -1.</_>
        <_>
          15 17 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 16 3 -1.</_>
        <_>
          4 17 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 2 2 -1.</_>
        <_>
          0 14 1 1 2.</_>
        <_>
          1 15 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 6 6 -1.</_>
        <_>
          7 4 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 1 3 -1.</_>
        <_>
          2 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 7 2 2 -1.</_>
        <_>
          2 7 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 11 5 3 -1.</_>
        <_>
          5 12 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 14 4 6 -1.</_>
        <_>
          16 17 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 6 7 -1.</_>
        <_>
          8 13 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 12 11 -1.</_>
        <_>
          3 1 6 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 7 3 -1.</_>
        <_>
          6 11 7 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 9 4 -1.</_>
        <_>
          8 2 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 14 10 2 -1.</_>
        <_>
          10 15 10 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 1 18 -1.</_>
        <_>
          0 6 1 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 13 2 2 -1.</_>
        <_>
          4 13 1 1 2.</_>
        <_>
          5 14 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 11 3 6 -1.</_>
        <_>
          9 12 1 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 3 -1.</_>
        <_>
          5 8 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 8 3 3 -1.</_>
        <_>
          5 8 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 14 1 -1.</_>
        <_>
          1 4 7 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 13 8 3 -1.</_>
        <_>
          14 13 4 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 2 1 -1.</_>
        <_>
          4 17 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 16 2 2 -1.</_>
        <_>
          6 16 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 17 4 2 -1.</_>
        <_>
          4 17 2 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 20 2 -1.</_>
        <_>
          5 7 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 9 2 2 -1.</_>
        <_>
          15 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 12 2 2 -1.</_>
        <_>
          3 12 1 1 2.</_>
        <_>
          4 13 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 2 1 -1.</_>
        <_>
          1 5 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 0 3 2 -1.</_>
        <_>
          18 1 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 8 3 9 -1.</_>
        <_>
          3 11 1 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          15 7 4 2 -1.</_>
        <_>
          16 8 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 16 3 3 -1.</_>
        <_>
          5 16 1 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 14 6 1 -1.</_>
        <_>
          10 14 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 6 6 -1.</_>
        <_>
          14 0 3 3 2.</_>
        <_>
          17 3 3 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          17 2 2 1 -1.</_>
        <_>
          17 2 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 19 20 1 -1.</_>
        <_>
          10 19 10 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 6 1 -1.</_>
        <_>
          3 19 3 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 4 3 -1.</_>
        <_>
          10 17 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 3 3 -1.</_>
        <_>
          5 12 1 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          17 7 3 3 -1.</_>
        <_>
          18 8 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 1 1 4 -1.</_>
        <_>
          18 2 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 1 -1.</_>
        <_>
          7 8 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 4 4 -1.</_>
        <_>
          6 5 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 0 8 7 -1.</_>
        <_>
          9 0 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 5 9 -1.</_>
        <_>
          0 10 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          14 10 1 1 2.</_>
        <_>
          15 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 11 2 2 -1.</_>
        <_>
          15 11 1 1 2.</_>
        <_>
          16 12 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 2 6 4 -1.</_>
        <_>
          11 2 2 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 12 8 -1.</_>
        <_>
          6 12 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 2 -1.</_>
        <_>
          3 0 2 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 5 -1.</_>
        <_>
          1 12 2 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 4 4 -1.</_>
        <_>
          3 12 2 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 11 2 4 -1.</_>
        <_>
          13 11 1 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 1 4 -1.</_>
        <_>
          2 2 1 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 4 9 -1.</_>
        <_>
          7 1 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 2 3 -1.</_>
        <_>
          13 11 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 15 3 -1.</_>
        <_>
          8 10 5 1 9.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 3 1 -1.</_>
        <_>
          16 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 0 15 8 -1.</_>
        <_>
          1 2 15 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 15 6 -1.</_>
        <_>
          2 6 15 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 6 -1.</_>
        <_>
          6 2 6 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 9 4 3 -1.</_>
        <_>
          16 10 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          16 7 4 3 -1.</_>
        <_>
          16 8 4 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 10 2 2 -1.</_>
        <_>
          15 10 1 1 2.</_>
        <_>
          16 11 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 2 3 -1.</_>
        <_>
          13 12 2 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 2 2 -1.</_>
        <_>
          2 16 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 4 7 -1.</_>
        <_>
          4 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 2 2 -1.</_>
        <_>
          0 16 1 1 2.</_>
        <_>
          1 17 1 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 18 3 -1.</_>
        <_>
          8 0 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 1 3 -1.</_>
        <_>
          0 2 1 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 4 4 -1.</_>
        <_>
          10 7 4 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          16 4 4 6 -1.</_>
        <_>
          16 4 2 3 2.</_>
        <_>
          18 7 2 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 4 2 -1.</_>
        <_>
          11 12 2 1 2.</_>
        <_>
          13 13 2 1 2.</_></rects></_></features></cascade>
</opencv_storage>
