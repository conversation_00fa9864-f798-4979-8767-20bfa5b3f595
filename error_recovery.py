"""
Enhanced Error Recovery System for Auto Latent Video Processor.

This module provides robust error handling with automatic retry logic,
graceful degradation, circuit breakers, and comprehensive user feedback.
"""

import time
import logging
import functools
from typing import Callable, Any, Optional, Dict, List, Tuple, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import threading
import queue
import traceback


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryStrategy(Enum):
    """Error recovery strategies."""
    RETRY = "retry"
    FALLBACK = "fallback"
    SKIP = "skip"
    ABORT = "abort"
    CIRCUIT_BREAK = "circuit_break"


@dataclass
class ErrorContext:
    """Context information for error handling."""
    operation: str
    timestamp: datetime
    severity: ErrorSeverity
    error_type: str
    error_message: str
    stack_trace: str
    retry_count: int = 0
    recovery_strategy: Optional[RecoveryStrategy] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    backoff_strategy: str = "exponential"  # "linear", "exponential", "fixed"


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker pattern."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    success_threshold: int = 3


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """Circuit breaker implementation for preventing cascading failures."""
    
    def __init__(self, config: CircuitBreakerConfig, name: str = "default"):
        self.config = config
        self.name = name
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        with self.lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    logging.info(f"Circuit breaker {self.name} transitioning to HALF_OPEN")
                else:
                    raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        
        time_since_failure = time.time() - self.last_failure_time
        return time_since_failure >= self.config.recovery_timeout
    
    def _on_success(self):
        """Handle successful operation."""
        with self.lock:
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
                    logging.info(f"Circuit breaker {self.name} reset to CLOSED")
            elif self.state == CircuitBreakerState.CLOSED:
                self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed operation."""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                self.success_count = 0
                logging.warning(f"Circuit breaker {self.name} opened due to failure in HALF_OPEN state")
            elif (self.state == CircuitBreakerState.CLOSED and 
                  self.failure_count >= self.config.failure_threshold):
                self.state = CircuitBreakerState.OPEN
                logging.warning(f"Circuit breaker {self.name} opened due to {self.failure_count} failures")


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open."""
    pass


class ErrorRecoveryManager:
    """Central error recovery management system."""
    
    def __init__(self):
        self.error_history: List[ErrorContext] = []
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_configs: Dict[str, RetryConfig] = {}
        self.error_handlers: Dict[str, Callable] = {}
        self.fallback_handlers: Dict[str, Callable] = {}
        self.lock = threading.Lock()
        
        # Default configurations
        self._setup_default_configs()
    
    def _setup_default_configs(self):
        """Setup default retry and circuit breaker configurations."""
        # Network operations
        self.retry_configs["network"] = RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            exponential_base=2.0
        )
        
        # File operations
        self.retry_configs["file"] = RetryConfig(
            max_attempts=2,
            base_delay=0.5,
            max_delay=5.0,
            exponential_base=1.5
        )
        
        # ComfyUI operations
        self.retry_configs["comfyui"] = RetryConfig(
            max_attempts=5,
            base_delay=2.0,
            max_delay=60.0,
            exponential_base=2.0
        )
        
        # TTS operations
        self.retry_configs["tts"] = RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            exponential_base=2.0
        )
        
        # Circuit breakers
        self.circuit_breakers["comfyui"] = CircuitBreaker(
            CircuitBreakerConfig(failure_threshold=3, recovery_timeout=120.0),
            name="comfyui"
        )
        
        self.circuit_breakers["tts"] = CircuitBreaker(
            CircuitBreakerConfig(failure_threshold=5, recovery_timeout=60.0),
            name="tts"
        )
    
    def register_error_handler(self, operation: str, handler: Callable):
        """Register custom error handler for specific operation."""
        self.error_handlers[operation] = handler
    
    def register_fallback_handler(self, operation: str, handler: Callable):
        """Register fallback handler for specific operation."""
        self.fallback_handlers[operation] = handler
    
    def with_retry(self, operation: str, retry_config: Optional[RetryConfig] = None):
        """Decorator for adding retry logic to functions."""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                config = retry_config or self.retry_configs.get(operation, RetryConfig())
                return self._execute_with_retry(func, operation, config, *args, **kwargs)
            return wrapper
        return decorator
    
    def with_circuit_breaker(self, operation: str):
        """Decorator for adding circuit breaker protection."""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                breaker = self.circuit_breakers.get(operation)
                if breaker:
                    return breaker.call(func, *args, **kwargs)
                else:
                    return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def with_error_recovery(self, operation: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
        """Comprehensive error recovery decorator."""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                return self._execute_with_recovery(func, operation, severity, *args, **kwargs)
            return wrapper
        return decorator
    
    def _execute_with_retry(self, func: Callable, operation: str, config: RetryConfig, 
                           *args, **kwargs) -> Any:
        """Execute function with retry logic."""
        last_exception = None
        
        for attempt in range(config.max_attempts):
            try:
                result = func(*args, **kwargs)
                if attempt > 0:
                    logging.info(f"Operation {operation} succeeded on attempt {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < config.max_attempts - 1:
                    delay = self._calculate_delay(attempt, config)
                    logging.warning(
                        f"Operation {operation} failed on attempt {attempt + 1}/{config.max_attempts}. "
                        f"Retrying in {delay:.2f}s. Error: {e}"
                    )
                    time.sleep(delay)
                else:
                    logging.error(
                        f"Operation {operation} failed after {config.max_attempts} attempts. "
                        f"Final error: {e}"
                    )
        
        # All retries exhausted
        raise last_exception
    
    def _execute_with_recovery(self, func: Callable, operation: str, 
                              severity: ErrorSeverity, *args, **kwargs) -> Any:
        """Execute function with comprehensive error recovery."""
        try:
            # Try with circuit breaker if available
            breaker = self.circuit_breakers.get(operation)
            if breaker:
                return breaker.call(func, *args, **kwargs)
            else:
                return func(*args, **kwargs)
                
        except Exception as e:
            error_context = ErrorContext(
                operation=operation,
                timestamp=datetime.now(),
                severity=severity,
                error_type=type(e).__name__,
                error_message=str(e),
                stack_trace=traceback.format_exc()
            )
            
            # Record error
            self._record_error(error_context)
            
            # Determine recovery strategy
            strategy = self._determine_recovery_strategy(error_context)
            error_context.recovery_strategy = strategy
            
            # Execute recovery strategy
            return self._execute_recovery_strategy(func, error_context, *args, **kwargs)
    
    def _calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """Calculate delay for retry attempt."""
        if config.backoff_strategy == "fixed":
            delay = config.base_delay
        elif config.backoff_strategy == "linear":
            delay = config.base_delay * (attempt + 1)
        else:  # exponential
            delay = config.base_delay * (config.exponential_base ** attempt)
        
        # Apply maximum delay limit
        delay = min(delay, config.max_delay)
        
        # Add jitter to prevent thundering herd
        if config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay
    
    def _determine_recovery_strategy(self, error_context: ErrorContext) -> RecoveryStrategy:
        """Determine appropriate recovery strategy based on error context."""
        # Network-related errors
        if "connection" in error_context.error_message.lower():
            return RecoveryStrategy.RETRY
        
        # File not found errors
        if "filenotfound" in error_context.error_type.lower():
            return RecoveryStrategy.FALLBACK
        
        # Permission errors
        if "permission" in error_context.error_message.lower():
            return RecoveryStrategy.SKIP
        
        # Critical system errors
        if error_context.severity == ErrorSeverity.CRITICAL:
            return RecoveryStrategy.ABORT
        
        # Circuit breaker errors
        if isinstance(error_context, CircuitBreakerOpenError):
            return RecoveryStrategy.CIRCUIT_BREAK
        
        # Default strategy based on severity
        if error_context.severity == ErrorSeverity.LOW:
            return RecoveryStrategy.SKIP
        else:
            return RecoveryStrategy.RETRY
    
    def _execute_recovery_strategy(self, func: Callable, error_context: ErrorContext, 
                                  *args, **kwargs) -> Any:
        """Execute the determined recovery strategy."""
        strategy = error_context.recovery_strategy
        
        if strategy == RecoveryStrategy.RETRY:
            config = self.retry_configs.get(error_context.operation, RetryConfig())
            return self._execute_with_retry(func, error_context.operation, config, *args, **kwargs)
        
        elif strategy == RecoveryStrategy.FALLBACK:
            fallback = self.fallback_handlers.get(error_context.operation)
            if fallback:
                logging.info(f"Using fallback for operation {error_context.operation}")
                return fallback(*args, **kwargs)
            else:
                logging.warning(f"No fallback available for operation {error_context.operation}")
                raise RuntimeError(f"Operation {error_context.operation} failed and no fallback available")
        
        elif strategy == RecoveryStrategy.SKIP:
            logging.warning(f"Skipping operation {error_context.operation} due to error")
            return None
        
        elif strategy == RecoveryStrategy.CIRCUIT_BREAK:
            logging.error(f"Circuit breaker open for operation {error_context.operation}")
            raise CircuitBreakerOpenError(f"Circuit breaker open for {error_context.operation}")
        
        else:  # ABORT
            logging.critical(f"Aborting due to critical error in operation {error_context.operation}")
            raise RuntimeError(f"Critical error in {error_context.operation}: {error_context.error_message}")
    
    def _record_error(self, error_context: ErrorContext):
        """Record error in history for analysis."""
        with self.lock:
            self.error_history.append(error_context)
            
            # Keep only recent errors (last 1000)
            if len(self.error_history) > 1000:
                self.error_history = self.error_history[-1000:]
        
        # Log error
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_context.severity, logging.ERROR)
        
        logging.log(
            log_level,
            f"Error in {error_context.operation}: {error_context.error_message}"
        )
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        with self.lock:
            if not self.error_history:
                return {"total_errors": 0}
            
            # Count errors by operation
            operation_counts = {}
            severity_counts = {}
            recent_errors = []
            
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            for error in self.error_history:
                operation_counts[error.operation] = operation_counts.get(error.operation, 0) + 1
                severity_counts[error.severity.value] = severity_counts.get(error.severity.value, 0) + 1
                
                if error.timestamp > cutoff_time:
                    recent_errors.append(error)
            
            return {
                "total_errors": len(self.error_history),
                "recent_errors_24h": len(recent_errors),
                "errors_by_operation": operation_counts,
                "errors_by_severity": severity_counts,
                "circuit_breaker_states": {
                    name: breaker.state.value 
                    for name, breaker in self.circuit_breakers.items()
                }
            }


# Global error recovery manager instance
_error_recovery_manager: Optional[ErrorRecoveryManager] = None


def get_error_recovery_manager() -> ErrorRecoveryManager:
    """Get the global error recovery manager instance."""
    global _error_recovery_manager
    if _error_recovery_manager is None:
        _error_recovery_manager = ErrorRecoveryManager()
    return _error_recovery_manager


# Convenience decorators
def with_retry(operation: str, retry_config: Optional[RetryConfig] = None):
    """Convenience decorator for retry functionality."""
    return get_error_recovery_manager().with_retry(operation, retry_config)


def with_circuit_breaker(operation: str):
    """Convenience decorator for circuit breaker functionality."""
    return get_error_recovery_manager().with_circuit_breaker(operation)


def with_error_recovery(operation: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """Convenience decorator for comprehensive error recovery."""
    return get_error_recovery_manager().with_error_recovery(operation, severity)
