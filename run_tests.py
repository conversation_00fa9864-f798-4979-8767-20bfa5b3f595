#!/usr/bin/env python3
"""
Test runner for the Auto Latent Video Processor.

This script provides a convenient way to run different types of tests
with various configurations and reporting options.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, cwd=None):
    """Run a command and return the result."""
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=False
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)


def check_dependencies():
    """Check if required test dependencies are available."""
    print("Checking test dependencies...")
    
    required_packages = ['pytest', 'pytest-cov', 'pytest-mock']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package}")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def run_tests(test_type="all", coverage=False, verbose=False, fast=False, 
              output_format="terminal", output_file=None):
    """Run tests with specified configuration."""
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test selection based on type
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "gui":
        cmd.extend(["-m", "gui"])
    elif test_type == "network":
        cmd.extend(["-m", "network", "--run-network-tests"])
    elif test_type == "backend":
        cmd.extend(["tests/test_backend_processor.py", "tests/test_workflow_processing.py"])
    elif test_type == "tts":
        cmd.extend(["tests/test_tts_integration.py"])
    elif test_type == "error":
        cmd.extend(["tests/test_error_handling.py"])
    elif test_type == "all":
        cmd.extend(["tests/"])
    else:
        print(f"Unknown test type: {test_type}")
        return 1
    
    # Add coverage if requested
    if coverage:
        cmd.extend([
            "--cov=backend_processor",
            "--cov=main_gui",
            "--cov-report=term-missing"
        ])
        if output_file:
            cmd.extend([f"--cov-report=html:{output_file}_coverage"])
    
    # Add verbosity
    if verbose:
        cmd.extend(["-v", "-s"])
    else:
        cmd.extend(["-q"])
    
    # Fast mode (skip slow tests)
    if fast:
        cmd.extend(["-m", "not slow"])
    
    # Output format
    if output_format == "junit":
        if not output_file:
            output_file = "test_results.xml"
        cmd.extend([f"--junit-xml={output_file}"])
    elif output_format == "json":
        if not output_file:
            output_file = "test_results.json"
        cmd.extend([f"--json-report", f"--json-report-file={output_file}"])
    
    # Additional pytest options
    cmd.extend([
        "--tb=short",  # Shorter traceback format
        "--strict-markers",  # Strict marker checking
        "--disable-warnings"  # Disable warnings for cleaner output
    ])
    
    print(f"Running tests: {' '.join(cmd)}")
    print("-" * 60)
    
    # Run the tests
    returncode, stdout, stderr = run_command(cmd)
    
    # Print output
    if stdout:
        print(stdout)
    if stderr:
        print(stderr, file=sys.stderr)
    
    return returncode


def run_linting():
    """Run code linting checks."""
    print("Running code linting...")
    
    # Check if flake8 is available
    try:
        import flake8
        print("✓ flake8 available")
    except ImportError:
        print("✗ flake8 not available. Install with: pip install flake8")
        return 1
    
    # Run flake8
    cmd = [
        "python", "-m", "flake8",
        "main_gui.py",
        "backend_processor.py",
        "tests/",
        "--max-line-length=120",
        "--ignore=E501,W503,E203",  # Ignore some common issues
        "--exclude=.venv,__pycache__,.git"
    ]
    
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        print("✓ Code linting passed")
    else:
        print("✗ Code linting failed")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr, file=sys.stderr)
    
    return returncode


def run_type_checking():
    """Run type checking with mypy."""
    print("Running type checking...")
    
    # Check if mypy is available
    try:
        import mypy
        print("✓ mypy available")
    except ImportError:
        print("✗ mypy not available. Install with: pip install mypy")
        return 1
    
    # Run mypy
    cmd = [
        "python", "-m", "mypy",
        "backend_processor.py",
        "--ignore-missing-imports",
        "--no-strict-optional",
        "--show-error-codes"
    ]
    
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        print("✓ Type checking passed")
    else:
        print("✗ Type checking found issues")
        if stdout:
            print(stdout)
        if stderr:
            print(stderr, file=sys.stderr)
    
    return returncode


def generate_test_report():
    """Generate a comprehensive test report."""
    print("Generating comprehensive test report...")
    
    # Run all tests with coverage
    print("\n1. Running all tests with coverage...")
    test_result = run_tests(
        test_type="all",
        coverage=True,
        verbose=True,
        output_format="junit",
        output_file="test_results"
    )
    
    # Run linting
    print("\n2. Running code linting...")
    lint_result = run_linting()
    
    # Run type checking
    print("\n3. Running type checking...")
    type_result = run_type_checking()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST REPORT SUMMARY")
    print("=" * 60)
    print(f"Tests:        {'PASSED' if test_result == 0 else 'FAILED'}")
    print(f"Linting:      {'PASSED' if lint_result == 0 else 'FAILED'}")
    print(f"Type Check:   {'PASSED' if type_result == 0 else 'FAILED'}")
    
    overall_result = test_result + lint_result + type_result
    print(f"Overall:      {'PASSED' if overall_result == 0 else 'FAILED'}")
    
    return overall_result


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Test runner for Auto Latent Video Processor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Test Types:
  all         Run all tests (default)
  unit        Run only unit tests
  integration Run only integration tests
  gui         Run only GUI tests
  network     Run only network tests (requires --run-network-tests)
  backend     Run backend processing tests
  tts         Run TTS integration tests
  error       Run error handling tests

Examples:
  python run_tests.py                          # Run all tests
  python run_tests.py --type unit --coverage   # Unit tests with coverage
  python run_tests.py --type backend -v        # Backend tests with verbose output
  python run_tests.py --lint                   # Run linting only
  python run_tests.py --report                 # Generate full report
        """
    )
    
    parser.add_argument(
        "--type", "-t",
        choices=["all", "unit", "integration", "gui", "network", "backend", "tts", "error"],
        default="all",
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage report"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--fast", "-f",
        action="store_true",
        help="Skip slow tests"
    )
    
    parser.add_argument(
        "--output-format",
        choices=["terminal", "junit", "json"],
        default="terminal",
        help="Output format for test results"
    )
    
    parser.add_argument(
        "--output-file", "-o",
        help="Output file for test results"
    )
    
    parser.add_argument(
        "--lint",
        action="store_true",
        help="Run linting checks only"
    )
    
    parser.add_argument(
        "--type-check",
        action="store_true",
        help="Run type checking only"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate comprehensive test report"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check test dependencies"
    )
    
    args = parser.parse_args()
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Handle special commands
    if args.check_deps:
        return 0 if check_dependencies() else 1
    
    if args.lint:
        return run_linting()
    
    if args.type_check:
        return run_type_checking()
    
    if args.report:
        return generate_test_report()
    
    # Check dependencies before running tests
    if not check_dependencies():
        return 1
    
    # Run tests
    return run_tests(
        test_type=args.type,
        coverage=args.coverage,
        verbose=args.verbose,
        fast=args.fast,
        output_format=args.output_format,
        output_file=args.output_file
    )


if __name__ == "__main__":
    sys.exit(main())
