"""
Enhanced Progress & Feedback System for Auto Latent Video Processor.

This module provides comprehensive progress tracking, visual feedback, notifications,
status management, and user interaction systems for better user experience.
"""

import time
import logging
import json
import threading
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
from pathlib import Path
import queue
import uuid

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QProgressBar, QLabel,
    QPushButton, QFrame, QScrollArea, QListWidget, QListWidgetItem,
    QSystemTrayIcon, QMenu, QAction, QDialog, QDialogButtonBox,
    QTextEdit, QGroupBox, QGridLayout, QSplitter, QTabWidget,
    QApplication, QMessageBox
)
from PySide6.QtCore import (
    Qt, Signal, QObject, QThread, QTimer, QPropertyAnimation,
    QEasingCurve, QRect, QSize, pyqtProperty
)
from PySide6.QtGui import (
    QFont, QColor, QPalette, QPixmap, QIcon, QMovie,
    QPainter, QPen, QBrush, QLinearGradient
)


class ProgressType(Enum):
    """Types of progress indicators."""
    DETERMINATE = "determinate"      # Known total (0-100%)
    INDETERMINATE = "indeterminate"  # Unknown total (spinning)
    STEPPED = "stepped"              # Step-based progress
    CIRCULAR = "circular"            # Circular progress indicator


class NotificationType(Enum):
    """Types of notifications."""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    PROGRESS = "progress"


class FeedbackLevel(Enum):
    """Levels of feedback detail."""
    MINIMAL = "minimal"      # Only critical feedback
    NORMAL = "normal"        # Standard feedback
    DETAILED = "detailed"    # Detailed feedback
    VERBOSE = "verbose"      # All feedback including debug


@dataclass
class ProgressInfo:
    """Information about a progress operation."""
    id: str
    title: str
    description: str = ""
    progress_type: ProgressType = ProgressType.DETERMINATE
    current: int = 0
    total: int = 100
    start_time: datetime = field(default_factory=datetime.now)
    estimated_completion: Optional[datetime] = None
    can_cancel: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def percentage(self) -> float:
        """Get progress as percentage."""
        if self.total <= 0:
            return 0.0
        return min(100.0, (self.current / self.total) * 100.0)
    
    @property
    def elapsed_time(self) -> timedelta:
        """Get elapsed time since start."""
        return datetime.now() - self.start_time
    
    @property
    def estimated_remaining(self) -> Optional[timedelta]:
        """Get estimated remaining time."""
        if self.estimated_completion:
            remaining = self.estimated_completion - datetime.now()
            return remaining if remaining.total_seconds() > 0 else timedelta(0)
        return None


@dataclass
class NotificationInfo:
    """Information about a notification."""
    id: str
    title: str
    message: str
    notification_type: NotificationType
    timestamp: datetime = field(default_factory=datetime.now)
    duration: Optional[float] = None  # Auto-dismiss after seconds
    actions: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    persistent: bool = False
    
    def add_action(self, label: str, callback: Callable, icon: str = ""):
        """Add an action button to the notification."""
        self.actions.append({
            'label': label,
            'callback': callback,
            'icon': icon
        })


class ProgressTracker(QObject):
    """Tracks and manages progress operations."""
    
    # Signals
    progress_started = Signal(str)  # progress_id
    progress_updated = Signal(str, int, int)  # progress_id, current, total
    progress_completed = Signal(str)  # progress_id
    progress_cancelled = Signal(str)  # progress_id
    progress_error = Signal(str, str)  # progress_id, error_message
    
    def __init__(self):
        super().__init__()
        self.active_progress: Dict[str, ProgressInfo] = {}
        self.completed_progress: List[ProgressInfo] = []
        self.max_completed_history = 50
        self.lock = threading.RLock()
    
    def start_progress(self, title: str, description: str = "", 
                      total: int = 100, progress_type: ProgressType = ProgressType.DETERMINATE,
                      can_cancel: bool = False) -> str:
        """Start a new progress operation."""
        progress_id = str(uuid.uuid4())
        
        with self.lock:
            progress_info = ProgressInfo(
                id=progress_id,
                title=title,
                description=description,
                total=total,
                progress_type=progress_type,
                can_cancel=can_cancel
            )
            
            self.active_progress[progress_id] = progress_info
            self.progress_started.emit(progress_id)
            
            logging.info(f"Started progress: {title} ({progress_id})")
            return progress_id
    
    def update_progress(self, progress_id: str, current: int, 
                       description: str = None, total: int = None) -> bool:
        """Update progress for an operation."""
        with self.lock:
            if progress_id not in self.active_progress:
                return False
            
            progress_info = self.active_progress[progress_id]
            progress_info.current = current
            
            if total is not None:
                progress_info.total = total
            
            if description is not None:
                progress_info.description = description
            
            # Estimate completion time
            if progress_info.progress_type == ProgressType.DETERMINATE and progress_info.total > 0:
                elapsed = progress_info.elapsed_time.total_seconds()
                if current > 0 and elapsed > 0:
                    rate = current / elapsed
                    remaining_items = progress_info.total - current
                    if rate > 0:
                        remaining_seconds = remaining_items / rate
                        progress_info.estimated_completion = datetime.now() + timedelta(seconds=remaining_seconds)
            
            self.progress_updated.emit(progress_id, current, progress_info.total)
            return True
    
    def complete_progress(self, progress_id: str, success: bool = True, 
                         message: str = "") -> bool:
        """Complete a progress operation."""
        with self.lock:
            if progress_id not in self.active_progress:
                return False
            
            progress_info = self.active_progress.pop(progress_id)
            
            if success:
                progress_info.current = progress_info.total
                self.completed_progress.append(progress_info)
                
                # Limit completed history
                if len(self.completed_progress) > self.max_completed_history:
                    self.completed_progress.pop(0)
                
                self.progress_completed.emit(progress_id)
                logging.info(f"Completed progress: {progress_info.title}")
            else:
                self.progress_error.emit(progress_id, message)
                logging.error(f"Progress failed: {progress_info.title} - {message}")
            
            return True
    
    def cancel_progress(self, progress_id: str) -> bool:
        """Cancel a progress operation."""
        with self.lock:
            if progress_id not in self.active_progress:
                return False
            
            progress_info = self.active_progress.pop(progress_id)
            
            if progress_info.can_cancel:
                self.progress_cancelled.emit(progress_id)
                logging.info(f"Cancelled progress: {progress_info.title}")
                return True
            else:
                # Put it back if it can't be cancelled
                self.active_progress[progress_id] = progress_info
                return False
    
    def get_progress_info(self, progress_id: str) -> Optional[ProgressInfo]:
        """Get progress information."""
        with self.lock:
            return self.active_progress.get(progress_id)
    
    def get_all_active_progress(self) -> List[ProgressInfo]:
        """Get all active progress operations."""
        with self.lock:
            return list(self.active_progress.values())
    
    def get_completed_progress(self) -> List[ProgressInfo]:
        """Get completed progress history."""
        with self.lock:
            return self.completed_progress.copy()


class NotificationManager(QObject):
    """Manages notifications and alerts."""
    
    # Signals
    notification_added = Signal(str)  # notification_id
    notification_dismissed = Signal(str)  # notification_id
    notification_action_triggered = Signal(str, str)  # notification_id, action_label
    
    def __init__(self):
        super().__init__()
        self.notifications: Dict[str, NotificationInfo] = {}
        self.notification_queue = queue.Queue()
        self.max_visible_notifications = 5
        self.default_durations = {
            NotificationType.INFO: 5.0,
            NotificationType.SUCCESS: 3.0,
            NotificationType.WARNING: 8.0,
            NotificationType.ERROR: 0,  # Persistent
            NotificationType.PROGRESS: 0  # Persistent
        }
        
        # Auto-dismiss timer
        self.dismiss_timer = QTimer()
        self.dismiss_timer.timeout.connect(self._check_auto_dismiss)
        self.dismiss_timer.start(1000)  # Check every second
    
    def show_notification(self, title: str, message: str, 
                         notification_type: NotificationType = NotificationType.INFO,
                         duration: Optional[float] = None,
                         persistent: bool = False,
                         actions: List[Dict[str, Any]] = None) -> str:
        """Show a notification."""
        notification_id = str(uuid.uuid4())
        
        # Use default duration if not specified
        if duration is None and not persistent:
            duration = self.default_durations.get(notification_type, 5.0)
        
        notification = NotificationInfo(
            id=notification_id,
            title=title,
            message=message,
            notification_type=notification_type,
            duration=duration,
            persistent=persistent,
            actions=actions or []
        )
        
        self.notifications[notification_id] = notification
        self.notification_added.emit(notification_id)
        
        logging.info(f"Notification: {title} - {message}")
        return notification_id
    
    def dismiss_notification(self, notification_id: str) -> bool:
        """Dismiss a notification."""
        if notification_id in self.notifications:
            del self.notifications[notification_id]
            self.notification_dismissed.emit(notification_id)
            return True
        return False
    
    def trigger_notification_action(self, notification_id: str, action_label: str) -> bool:
        """Trigger a notification action."""
        if notification_id not in self.notifications:
            return False
        
        notification = self.notifications[notification_id]
        
        for action in notification.actions:
            if action['label'] == action_label:
                try:
                    action['callback']()
                    self.notification_action_triggered.emit(notification_id, action_label)
                    return True
                except Exception as e:
                    logging.error(f"Failed to execute notification action: {e}")
                    return False
        
        return False
    
    def get_notification(self, notification_id: str) -> Optional[NotificationInfo]:
        """Get notification information."""
        return self.notifications.get(notification_id)
    
    def get_all_notifications(self) -> List[NotificationInfo]:
        """Get all active notifications."""
        return list(self.notifications.values())
    
    def clear_all_notifications(self):
        """Clear all notifications."""
        notification_ids = list(self.notifications.keys())
        for notification_id in notification_ids:
            self.dismiss_notification(notification_id)
    
    def _check_auto_dismiss(self):
        """Check for notifications that should be auto-dismissed."""
        current_time = datetime.now()
        to_dismiss = []
        
        for notification_id, notification in self.notifications.items():
            if (notification.duration and 
                not notification.persistent and
                (current_time - notification.timestamp).total_seconds() >= notification.duration):
                to_dismiss.append(notification_id)
        
        for notification_id in to_dismiss:
            self.dismiss_notification(notification_id)
    
    # Convenience methods for common notification types
    def show_info(self, title: str, message: str, duration: float = 5.0) -> str:
        """Show an info notification."""
        return self.show_notification(title, message, NotificationType.INFO, duration)
    
    def show_success(self, title: str, message: str, duration: float = 3.0) -> str:
        """Show a success notification."""
        return self.show_notification(title, message, NotificationType.SUCCESS, duration)
    
    def show_warning(self, title: str, message: str, duration: float = 8.0) -> str:
        """Show a warning notification."""
        return self.show_notification(title, message, NotificationType.WARNING, duration)
    
    def show_error(self, title: str, message: str, persistent: bool = True) -> str:
        """Show an error notification."""
        return self.show_notification(title, message, NotificationType.ERROR, 
                                    persistent=persistent)


class StatusManager(QObject):
    """Manages application status and state information."""
    
    # Signals
    status_changed = Signal(str, str)  # component, status
    state_changed = Signal(str, dict)  # component, state_data
    
    def __init__(self):
        super().__init__()
        self.component_status: Dict[str, str] = {}
        self.component_states: Dict[str, Dict[str, Any]] = {}
        self.status_history: List[Dict[str, Any]] = []
        self.max_history = 100
    
    def set_status(self, component: str, status: str, details: str = ""):
        """Set status for a component."""
        old_status = self.component_status.get(component, "")
        self.component_status[component] = status
        
        # Add to history
        self.status_history.append({
            'timestamp': datetime.now(),
            'component': component,
            'old_status': old_status,
            'new_status': status,
            'details': details
        })
        
        # Limit history
        if len(self.status_history) > self.max_history:
            self.status_history.pop(0)
        
        self.status_changed.emit(component, status)
        logging.debug(f"Status changed: {component} -> {status}")
    
    def set_state(self, component: str, state_data: Dict[str, Any]):
        """Set state data for a component."""
        self.component_states[component] = state_data.copy()
        self.state_changed.emit(component, state_data)
    
    def get_status(self, component: str) -> str:
        """Get current status for a component."""
        return self.component_status.get(component, "unknown")
    
    def get_state(self, component: str) -> Dict[str, Any]:
        """Get current state for a component."""
        return self.component_states.get(component, {})
    
    def get_all_status(self) -> Dict[str, str]:
        """Get status for all components."""
        return self.component_status.copy()
    
    def get_status_history(self, component: str = None) -> List[Dict[str, Any]]:
        """Get status history, optionally filtered by component."""
        if component:
            return [h for h in self.status_history if h['component'] == component]
        return self.status_history.copy()


class FeedbackSystem(QObject):
    """Main feedback system coordinating all feedback components."""
    
    def __init__(self, feedback_level: FeedbackLevel = FeedbackLevel.NORMAL):
        super().__init__()
        
        self.feedback_level = feedback_level
        self.progress_tracker = ProgressTracker()
        self.notification_manager = NotificationManager()
        self.status_manager = StatusManager()
        
        # Connect internal signals
        self._connect_signals()
        
        # Performance monitoring
        self.performance_data: Dict[str, List[float]] = {}
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._collect_performance_data)
        self.performance_timer.start(5000)  # Collect every 5 seconds
    
    def _connect_signals(self):
        """Connect internal signals between components."""
        # Connect progress to notifications for important events
        self.progress_tracker.progress_completed.connect(self._on_progress_completed)
        self.progress_tracker.progress_error.connect(self._on_progress_error)
        self.progress_tracker.progress_cancelled.connect(self._on_progress_cancelled)
    
    def _on_progress_completed(self, progress_id: str):
        """Handle progress completion."""
        progress_info = self.progress_tracker.get_progress_info(progress_id)
        if progress_info and self.feedback_level in [FeedbackLevel.NORMAL, FeedbackLevel.DETAILED, FeedbackLevel.VERBOSE]:
            self.notification_manager.show_success(
                "Task Completed",
                f"{progress_info.title} completed successfully"
            )
    
    def _on_progress_error(self, progress_id: str, error_message: str):
        """Handle progress errors."""
        progress_info = self.progress_tracker.get_progress_info(progress_id)
        title = progress_info.title if progress_info else "Task"
        
        self.notification_manager.show_error(
            "Task Failed",
            f"{title} failed: {error_message}"
        )
    
    def _on_progress_cancelled(self, progress_id: str):
        """Handle progress cancellation."""
        if self.feedback_level in [FeedbackLevel.DETAILED, FeedbackLevel.VERBOSE]:
            progress_info = self.progress_tracker.get_progress_info(progress_id)
            title = progress_info.title if progress_info else "Task"
            
            self.notification_manager.show_info(
                "Task Cancelled",
                f"{title} was cancelled"
            )
    
    def _collect_performance_data(self):
        """Collect performance data for monitoring."""
        try:
            # Collect memory usage, CPU usage, etc.
            # This would integrate with actual performance monitoring
            import psutil
            
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()
            
            if 'memory_mb' not in self.performance_data:
                self.performance_data['memory_mb'] = []
            if 'cpu_percent' not in self.performance_data:
                self.performance_data['cpu_percent'] = []
            
            self.performance_data['memory_mb'].append(memory_mb)
            self.performance_data['cpu_percent'].append(cpu_percent)
            
            # Keep only last 100 data points
            for key in self.performance_data:
                if len(self.performance_data[key]) > 100:
                    self.performance_data[key].pop(0)
            
        except ImportError:
            # psutil not available, use mock data
            pass
        except Exception as e:
            logging.warning(f"Failed to collect performance data: {e}")
    
    def set_feedback_level(self, level: FeedbackLevel):
        """Set the feedback detail level."""
        self.feedback_level = level
        logging.info(f"Feedback level set to: {level.value}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        active_progress = self.progress_tracker.get_all_active_progress()
        active_notifications = self.notification_manager.get_all_notifications()
        
        return {
            'active_progress_count': len(active_progress),
            'active_notifications_count': len(active_notifications),
            'feedback_level': self.feedback_level.value,
            'component_status': self.status_manager.get_all_status(),
            'performance_data': self.performance_data.copy() if self.performance_data else {}
        }


class ProgressWidget(QWidget):
    """Widget for displaying progress operations."""

    # Signals
    progress_cancelled = Signal(str)  # progress_id

    def __init__(self, progress_info: ProgressInfo):
        super().__init__()
        self.progress_info = progress_info

        self._setup_ui()
        self._update_display()

    def _setup_ui(self):
        """Setup the progress widget UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)

        # Header with title and cancel button
        header_layout = QHBoxLayout()

        self.title_label = QLabel(self.progress_info.title)
        self.title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        header_layout.addWidget(self.title_label)

        header_layout.addStretch()

        if self.progress_info.can_cancel:
            self.cancel_button = QPushButton("Cancel")
            self.cancel_button.setFixedSize(60, 25)
            self.cancel_button.clicked.connect(self._on_cancel_clicked)
            header_layout.addWidget(self.cancel_button)

        layout.addLayout(header_layout)

        # Description
        if self.progress_info.description:
            self.description_label = QLabel(self.progress_info.description)
            self.description_label.setWordWrap(True)
            layout.addWidget(self.description_label)

        # Progress bar
        self.progress_bar = QProgressBar()

        if self.progress_info.progress_type == ProgressType.INDETERMINATE:
            self.progress_bar.setRange(0, 0)  # Indeterminate
        else:
            self.progress_bar.setRange(0, self.progress_info.total)
            self.progress_bar.setValue(self.progress_info.current)

        layout.addWidget(self.progress_bar)

        # Status info
        status_layout = QHBoxLayout()

        self.percentage_label = QLabel()
        status_layout.addWidget(self.percentage_label)

        status_layout.addStretch()

        self.time_label = QLabel()
        status_layout.addWidget(self.time_label)

        layout.addLayout(status_layout)

    def _update_display(self):
        """Update the display with current progress info."""
        # Update progress bar
        if self.progress_info.progress_type != ProgressType.INDETERMINATE:
            self.progress_bar.setValue(self.progress_info.current)
            self.progress_bar.setMaximum(self.progress_info.total)

        # Update percentage
        if self.progress_info.progress_type == ProgressType.DETERMINATE:
            self.percentage_label.setText(f"{self.progress_info.percentage:.1f}%")
        else:
            self.percentage_label.setText("Processing...")

        # Update time info
        elapsed = self.progress_info.elapsed_time
        elapsed_str = f"Elapsed: {elapsed.total_seconds():.0f}s"

        remaining = self.progress_info.estimated_remaining
        if remaining:
            remaining_str = f"Remaining: {remaining.total_seconds():.0f}s"
            self.time_label.setText(f"{elapsed_str} | {remaining_str}")
        else:
            self.time_label.setText(elapsed_str)

        # Update description if changed
        if hasattr(self, 'description_label'):
            self.description_label.setText(self.progress_info.description)

    def update_progress(self, current: int, total: int = None):
        """Update progress values."""
        self.progress_info.current = current
        if total is not None:
            self.progress_info.total = total

        self._update_display()

    def _on_cancel_clicked(self):
        """Handle cancel button click."""
        self.progress_cancelled.emit(self.progress_info.id)


class NotificationWidget(QWidget):
    """Widget for displaying a notification."""

    # Signals
    notification_dismissed = Signal(str)  # notification_id
    action_triggered = Signal(str, str)   # notification_id, action_label

    def __init__(self, notification_info: NotificationInfo):
        super().__init__()
        self.notification_info = notification_info

        self._setup_ui()
        self._setup_animations()

    def _setup_ui(self):
        """Setup the notification widget UI."""
        self.setFixedHeight(80)
        self.setStyleSheet(self._get_style_sheet())

        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)

        # Icon
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(32, 32)
        self.icon_label.setStyleSheet(f"background-color: {self._get_icon_color()}; border-radius: 16px;")
        layout.addWidget(self.icon_label)

        # Content
        content_layout = QVBoxLayout()

        # Title
        self.title_label = QLabel(self.notification_info.title)
        self.title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        content_layout.addWidget(self.title_label)

        # Message
        self.message_label = QLabel(self.notification_info.message)
        self.message_label.setWordWrap(True)
        content_layout.addWidget(self.message_label)

        layout.addLayout(content_layout)

        # Actions
        if self.notification_info.actions:
            actions_layout = QVBoxLayout()

            for action in self.notification_info.actions:
                action_btn = QPushButton(action['label'])
                action_btn.setFixedHeight(25)
                action_btn.clicked.connect(lambda checked=False, label=action['label']:
                                         self._on_action_clicked(label))
                actions_layout.addWidget(action_btn)

            layout.addLayout(actions_layout)

        # Dismiss button
        self.dismiss_button = QPushButton("×")
        self.dismiss_button.setFixedSize(25, 25)
        self.dismiss_button.setStyleSheet("border: none; font-size: 16px; font-weight: bold;")
        self.dismiss_button.clicked.connect(self._on_dismiss_clicked)
        layout.addWidget(self.dismiss_button)

    def _setup_animations(self):
        """Setup entrance and exit animations."""
        self.slide_animation = QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(300)
        self.slide_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)

    def _get_style_sheet(self) -> str:
        """Get style sheet based on notification type."""
        colors = {
            NotificationType.INFO: "#e3f2fd",
            NotificationType.SUCCESS: "#e8f5e8",
            NotificationType.WARNING: "#fff3e0",
            NotificationType.ERROR: "#ffebee",
            NotificationType.PROGRESS: "#f3e5f5"
        }

        bg_color = colors.get(self.notification_info.notification_type, "#f5f5f5")

        return f"""
            QWidget {{
                background-color: {bg_color};
                border: 1px solid #ddd;
                border-radius: 8px;
            }}
        """

    def _get_icon_color(self) -> str:
        """Get icon color based on notification type."""
        colors = {
            NotificationType.INFO: "#2196f3",
            NotificationType.SUCCESS: "#4caf50",
            NotificationType.WARNING: "#ff9800",
            NotificationType.ERROR: "#f44336",
            NotificationType.PROGRESS: "#9c27b0"
        }

        return colors.get(self.notification_info.notification_type, "#757575")

    def _on_action_clicked(self, action_label: str):
        """Handle action button click."""
        self.action_triggered.emit(self.notification_info.id, action_label)

    def _on_dismiss_clicked(self):
        """Handle dismiss button click."""
        self.notification_dismissed.emit(self.notification_info.id)

    def animate_in(self):
        """Animate the notification sliding in."""
        start_rect = QRect(self.x(), -self.height(), self.width(), self.height())
        end_rect = QRect(self.x(), self.y(), self.width(), self.height())

        self.setGeometry(start_rect)
        self.slide_animation.setStartValue(start_rect)
        self.slide_animation.setEndValue(end_rect)
        self.slide_animation.start()

    def animate_out(self, callback: Callable = None):
        """Animate the notification sliding out."""
        start_rect = self.geometry()
        end_rect = QRect(self.x(), -self.height(), self.width(), self.height())

        self.slide_animation.setStartValue(start_rect)
        self.slide_animation.setEndValue(end_rect)

        if callback:
            self.slide_animation.finished.connect(callback)

        self.slide_animation.start()


class ProgressPanel(QWidget):
    """Panel for displaying all active progress operations."""

    def __init__(self, progress_tracker: ProgressTracker):
        super().__init__()
        self.progress_tracker = progress_tracker
        self.progress_widgets: Dict[str, ProgressWidget] = {}

        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Setup the progress panel UI."""
        layout = QVBoxLayout(self)

        # Header
        header_label = QLabel("Active Tasks")
        header_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(header_label)

        # Scroll area for progress items
        self.scroll_area = QScrollArea()
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_layout.addStretch()

        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_area.setWidgetResizable(True)
        layout.addWidget(self.scroll_area)

    def _connect_signals(self):
        """Connect to progress tracker signals."""
        self.progress_tracker.progress_started.connect(self._on_progress_started)
        self.progress_tracker.progress_updated.connect(self._on_progress_updated)
        self.progress_tracker.progress_completed.connect(self._on_progress_completed)
        self.progress_tracker.progress_cancelled.connect(self._on_progress_cancelled)
        self.progress_tracker.progress_error.connect(self._on_progress_error)

    def _on_progress_started(self, progress_id: str):
        """Handle new progress operation."""
        progress_info = self.progress_tracker.get_progress_info(progress_id)
        if progress_info:
            widget = ProgressWidget(progress_info)
            widget.progress_cancelled.connect(self.progress_tracker.cancel_progress)

            # Insert before the stretch
            self.scroll_layout.insertWidget(self.scroll_layout.count() - 1, widget)
            self.progress_widgets[progress_id] = widget

    def _on_progress_updated(self, progress_id: str, current: int, total: int):
        """Handle progress update."""
        if progress_id in self.progress_widgets:
            self.progress_widgets[progress_id].update_progress(current, total)

    def _on_progress_completed(self, progress_id: str):
        """Handle progress completion."""
        self._remove_progress_widget(progress_id)

    def _on_progress_cancelled(self, progress_id: str):
        """Handle progress cancellation."""
        self._remove_progress_widget(progress_id)

    def _on_progress_error(self, progress_id: str, error_message: str):
        """Handle progress error."""
        logging.error(f"Progress error for {progress_id}: {error_message}")
        self._remove_progress_widget(progress_id)

    def _remove_progress_widget(self, progress_id: str):
        """Remove a progress widget."""
        if progress_id in self.progress_widgets:
            widget = self.progress_widgets.pop(progress_id)
            self.scroll_layout.removeWidget(widget)
            widget.deleteLater()


class NotificationPanel(QWidget):
    """Panel for displaying notifications."""

    def __init__(self, notification_manager: NotificationManager):
        super().__init__()
        self.notification_manager = notification_manager
        self.notification_widgets: Dict[str, NotificationWidget] = {}

        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Setup the notification panel UI."""
        layout = QVBoxLayout(self)

        # Header with clear all button
        header_layout = QHBoxLayout()

        header_label = QLabel("Notifications")
        header_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(header_label)

        header_layout.addStretch()

        clear_all_btn = QPushButton("Clear All")
        clear_all_btn.clicked.connect(self.notification_manager.clear_all_notifications)
        header_layout.addWidget(clear_all_btn)

        layout.addLayout(header_layout)

        # Scroll area for notifications
        self.scroll_area = QScrollArea()
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_layout.addStretch()

        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_area.setWidgetResizable(True)
        layout.addWidget(self.scroll_area)

    def _connect_signals(self):
        """Connect to notification manager signals."""
        self.notification_manager.notification_added.connect(self._on_notification_added)
        self.notification_manager.notification_dismissed.connect(self._on_notification_dismissed)

    def _on_notification_added(self, notification_id: str):
        """Handle new notification."""
        notification_info = self.notification_manager.get_notification(notification_id)
        if notification_info:
            widget = NotificationWidget(notification_info)
            widget.notification_dismissed.connect(self.notification_manager.dismiss_notification)
            widget.action_triggered.connect(self.notification_manager.trigger_notification_action)

            # Insert at top (before stretch)
            self.scroll_layout.insertWidget(0, widget)
            self.notification_widgets[notification_id] = widget

            # Animate in
            widget.animate_in()

    def _on_notification_dismissed(self, notification_id: str):
        """Handle notification dismissal."""
        if notification_id in self.notification_widgets:
            widget = self.notification_widgets.pop(notification_id)

            def remove_widget():
                self.scroll_layout.removeWidget(widget)
                widget.deleteLater()

            widget.animate_out(remove_widget)


class FeedbackWidget(QWidget):
    """Main feedback widget combining progress and notifications."""

    def __init__(self, feedback_system: FeedbackSystem):
        super().__init__()
        self.feedback_system = feedback_system

        self._setup_ui()

    def _setup_ui(self):
        """Setup the main feedback widget UI."""
        layout = QVBoxLayout(self)

        # Tab widget for different feedback types
        self.tab_widget = QTabWidget()

        # Progress tab
        self.progress_panel = ProgressPanel(self.feedback_system.progress_tracker)
        self.tab_widget.addTab(self.progress_panel, "Progress")

        # Notifications tab
        self.notification_panel = NotificationPanel(self.feedback_system.notification_manager)
        self.tab_widget.addTab(self.notification_panel, "Notifications")

        layout.addWidget(self.tab_widget)

        # Status bar
        self.status_bar = QLabel("Ready")
        self.status_bar.setStyleSheet("padding: 5px; border-top: 1px solid #ccc;")
        layout.addWidget(self.status_bar)

        # Update status periodically
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(2000)  # Update every 2 seconds

    def _update_status(self):
        """Update the status bar."""
        system_status = self.feedback_system.get_system_status()

        active_progress = system_status['active_progress_count']
        active_notifications = system_status['active_notifications_count']

        if active_progress > 0:
            self.status_bar.setText(f"{active_progress} active task(s)")
        elif active_notifications > 0:
            self.status_bar.setText(f"{active_notifications} notification(s)")
        else:
            self.status_bar.setText("Ready")
