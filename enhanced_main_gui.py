"""
Enhanced Auto Latent Video Processor with Integrated Advanced Features.

This is the enhanced version of main_gui.py that integrates all 8 advanced features
into the existing application while maintaining backward compatibility.
"""

# Import the original main_gui functionality
from main_gui import *

# Import our new advanced features with compatibility layer
try:
    # Mock external dependencies that might not be available
    import sys
    from unittest.mock import MagicMock

    # Only mock if not already available
    if 'numpy' not in sys.modules:
        sys.modules['numpy'] = MagicMock()
    if 'librosa' not in sys.modules:
        sys.modules['librosa'] = MagicMock()
    if 'soundfile' not in sys.modules:
        sys.modules['soundfile'] = MagicMock()
    if 'scipy' not in sys.modules:
        sys.modules['scipy'] = MagicMock()
        sys.modules['scipy.signal'] = MagicMock()
        sys.modules['scipy.io'] = MagicMock()
        sys.modules['scipy.io.wavfile'] = MagicMock()

    from ui_foundation import ThemeManager, LayoutManager
    from project_management import ProjectManager, ProjectWidget
    from timeline_editor import TimelineEditor
    from media_library import MediaLibraryWidget
    from visual_workflow_builder import WorkflowBuilderWidget
    from advanced_audio_system import AudioMixerWidget
    from undo_redo_system import UndoRedoManager, UndoRedoWidget
    from enhanced_progress_feedback import FeedbackSystem, FeedbackWidget
    ADVANCED_FEATURES_AVAILABLE = True
    print("✅ Advanced features loaded successfully!")
except ImportError as e:
    ADVANCED_FEATURES_AVAILABLE = False
    print(f"⚠️ Advanced features not available: {e}")
    print("   Running in basic mode...")
except Exception as e:
    ADVANCED_FEATURES_AVAILABLE = False
    print(f"⚠️ Error loading advanced features: {e}")
    print("   Running in basic mode...")


class EnhancedMainWindow(MainWindow):
    """Enhanced version of MainWindow with integrated advanced features."""
    
    def __init__(self):
        # Initialize the original MainWindow
        super().__init__()
        
        # Only add advanced features if available
        if ADVANCED_FEATURES_AVAILABLE:
            self._initialize_advanced_features()
            self._integrate_advanced_ui()
            print("🚀 Enhanced Auto Latent Video Processor loaded with all advanced features!")
        else:
            print("📱 Running basic Auto Latent Video Processor")
    
    def _initialize_advanced_features(self):
        """Initialize all advanced feature systems."""
        try:
            # Core systems
            self.theme_manager = ThemeManager()
            self.layout_manager = LayoutManager()
            self.project_manager = ProjectManager()
            self.undo_manager = UndoRedoManager()
            self.feedback_system = FeedbackSystem()
            
            # Connect signals
            self._connect_advanced_signals()
            
            print("✅ Advanced systems initialized")
            
        except Exception as e:
            print(f"❌ Error initializing advanced features: {e}")
            global ADVANCED_FEATURES_AVAILABLE
            ADVANCED_FEATURES_AVAILABLE = False
    
    def _integrate_advanced_ui(self):
        """Integrate advanced UI components into the existing interface."""
        try:
            # Get the current central widget
            current_central = self.centralWidget()
            
            # Create new enhanced layout
            enhanced_central = QWidget()
            self.setCentralWidget(enhanced_central)
            
            # Main layout with splitters
            main_layout = QVBoxLayout(enhanced_central)
            
            # Add menu bar enhancements
            self._enhance_menu_bar()
            
            # Create main splitter
            main_splitter = QSplitter(Qt.Orientation.Horizontal)
            
            # Left panel: Enhanced media library and project management
            left_panel = self._create_enhanced_left_panel()
            main_splitter.addWidget(left_panel)
            
            # Center panel: Original interface + timeline
            center_panel = self._create_enhanced_center_panel(current_central)
            main_splitter.addWidget(center_panel)
            
            # Right panel: Audio mixer and feedback
            right_panel = self._create_enhanced_right_panel()
            main_splitter.addWidget(right_panel)
            
            # Set splitter proportions
            main_splitter.setSizes([300, 800, 300])
            
            main_layout.addWidget(main_splitter)
            
            # Add status bar enhancements
            self._enhance_status_bar()
            
            print("✅ Advanced UI integrated successfully")
            
        except Exception as e:
            print(f"❌ Error integrating advanced UI: {e}")
            # Fallback to original interface
            self.setCentralWidget(current_central)
    
    def _enhance_menu_bar(self):
        """Add advanced features to the menu bar."""
        try:
            menubar = self.menuBar()
            
            # Enhanced View menu
            view_menu = None
            for action in menubar.actions():
                if action.text() == "View":
                    view_menu = action.menu()
                    break
            
            if not view_menu:
                view_menu = menubar.addMenu("View")
            
            # Theme submenu
            theme_menu = view_menu.addMenu("Theme")
            
            light_action = QAction("Light Theme", self)
            light_action.triggered.connect(lambda: self.theme_manager.set_theme("light"))
            theme_menu.addAction(light_action)
            
            dark_action = QAction("Dark Theme", self)
            dark_action.triggered.connect(lambda: self.theme_manager.set_theme("dark"))
            theme_menu.addAction(dark_action)
            
            # Enhanced Edit menu
            edit_menu = None
            for action in menubar.actions():
                if action.text() == "Edit":
                    edit_menu = action.menu()
                    break
            
            if not edit_menu:
                edit_menu = menubar.addMenu("Edit")
            
            edit_menu.addSeparator()
            
            undo_action = QAction("Undo", self)
            undo_action.setShortcut("Ctrl+Z")
            undo_action.triggered.connect(self.undo_manager.undo)
            edit_menu.addAction(undo_action)
            
            redo_action = QAction("Redo", self)
            redo_action.setShortcut("Ctrl+Y")
            redo_action.triggered.connect(self.undo_manager.redo)
            edit_menu.addAction(redo_action)
            
            # Tools menu
            tools_menu = menubar.addMenu("Tools")
            
            workflow_action = QAction("Workflow Builder", self)
            workflow_action.triggered.connect(self._show_workflow_builder)
            tools_menu.addAction(workflow_action)
            
            audio_mixer_action = QAction("Audio Mixer", self)
            audio_mixer_action.triggered.connect(self._show_audio_mixer)
            tools_menu.addAction(audio_mixer_action)
            
        except Exception as e:
            print(f"❌ Error enhancing menu bar: {e}")
    
    def _create_enhanced_left_panel(self) -> QWidget:
        """Create enhanced left panel with media library and project management."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for different views
        left_tabs = QTabWidget()
        
        try:
            # Enhanced Media Library
            self.media_library = MediaLibraryWidget()
            left_tabs.addTab(self.media_library, "Media Library")
            
            # Project Management
            self.project_widget = ProjectWidget(self.project_manager)
            left_tabs.addTab(self.project_widget, "Project")
            
        except Exception as e:
            print(f"❌ Error creating enhanced left panel: {e}")
            # Fallback to simple label
            left_tabs.addTab(QLabel("Media Library\n(Enhanced features loading...)"), "Media")
        
        layout.addWidget(left_tabs)
        return panel
    
    def _create_enhanced_center_panel(self, original_widget: QWidget) -> QWidget:
        """Create enhanced center panel with original interface + timeline."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for main work areas
        center_tabs = QTabWidget()
        
        # Original interface tab
        center_tabs.addTab(original_widget, "Lip Sync Processing")
        
        try:
            # Timeline Editor tab
            self.timeline_editor = TimelineEditor()
            center_tabs.addTab(self.timeline_editor, "Timeline Editor")
            
            # Workflow Builder tab
            self.workflow_builder = WorkflowBuilderWidget()
            center_tabs.addTab(self.workflow_builder, "Workflow Builder")
            
        except Exception as e:
            print(f"❌ Error creating enhanced center panel: {e}")
            # Just show original interface
            center_tabs.addTab(QLabel("Timeline Editor\n(Loading...)"), "Timeline")
        
        layout.addWidget(center_tabs)
        return panel
    
    def _create_enhanced_right_panel(self) -> QWidget:
        """Create enhanced right panel with audio mixer and feedback."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Tab widget for right panel views
        right_tabs = QTabWidget()
        
        try:
            # Audio Mixer tab
            self.audio_mixer = AudioMixerWidget()
            right_tabs.addTab(self.audio_mixer, "Audio Mixer")
            
            # Feedback System tab
            self.feedback_widget = FeedbackWidget(self.feedback_system)
            right_tabs.addTab(self.feedback_widget, "Progress & Feedback")
            
        except Exception as e:
            print(f"❌ Error creating enhanced right panel: {e}")
            # Fallback to simple labels
            right_tabs.addTab(QLabel("Audio Mixer\n(Loading...)"), "Audio")
            right_tabs.addTab(QLabel("Progress Feedback\n(Loading...)"), "Progress")
        
        layout.addWidget(right_tabs)
        return panel
    
    def _enhance_status_bar(self):
        """Enhance the status bar with advanced features."""
        try:
            status_bar = self.statusBar()
            
            # Add undo/redo status
            self.undo_status_label = QLabel("Ready")
            status_bar.addPermanentWidget(self.undo_status_label)
            
            # Update undo status when history changes
            self.undo_manager.history_changed.connect(self._update_undo_status)
            
        except Exception as e:
            print(f"❌ Error enhancing status bar: {e}")
    
    def _connect_advanced_signals(self):
        """Connect signals between advanced systems."""
        try:
            # Connect project management to feedback system
            self.project_manager.project_loaded.connect(
                lambda path: self.feedback_system.notification_manager.show_success(
                    "Project Loaded", f"Loaded: {Path(path).name}"
                )
            )
            
            # Connect undo/redo to feedback
            self.undo_manager.action_executed.connect(
                lambda action_id: self.feedback_system.status_manager.set_status(
                    "undo_system", "action_executed"
                )
            )
            
        except Exception as e:
            print(f"❌ Error connecting advanced signals: {e}")
    
    def _update_undo_status(self):
        """Update undo/redo status in status bar."""
        try:
            if hasattr(self, 'undo_status_label'):
                if self.undo_manager.can_undo():
                    self.undo_status_label.setText(f"Can undo: {self.undo_manager.get_undo_description()}")
                else:
                    self.undo_status_label.setText("Ready")
        except Exception as e:
            print(f"❌ Error updating undo status: {e}")
    
    def _show_workflow_builder(self):
        """Show the workflow builder in a separate window."""
        try:
            if hasattr(self, 'workflow_builder'):
                # Switch to workflow builder tab
                center_tabs = self.centralWidget().findChild(QTabWidget)
                if center_tabs:
                    for i in range(center_tabs.count()):
                        if center_tabs.tabText(i) == "Workflow Builder":
                            center_tabs.setCurrentIndex(i)
                            break
        except Exception as e:
            print(f"❌ Error showing workflow builder: {e}")
    
    def _show_audio_mixer(self):
        """Show the audio mixer."""
        try:
            if hasattr(self, 'audio_mixer'):
                # Switch to audio mixer tab
                right_panel = self.centralWidget().findChild(QSplitter).widget(2)
                right_tabs = right_panel.findChild(QTabWidget)
                if right_tabs:
                    for i in range(right_tabs.count()):
                        if right_tabs.tabText(i) == "Audio Mixer":
                            right_tabs.setCurrentIndex(i)
                            break
        except Exception as e:
            print(f"❌ Error showing audio mixer: {e}")


def main():
    """Enhanced main function with advanced features."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Auto Latent Video Processor - Enhanced")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("Auto Latent Video Processor")
    
    try:
        # Create enhanced main window
        window = EnhancedMainWindow()
        window.show()
        
        print("🎬 Enhanced Auto Latent Video Processor started successfully!")
        print("✨ Features available:")
        if ADVANCED_FEATURES_AVAILABLE:
            print("   • Original lip-sync processing")
            print("   • Professional UI with themes")
            print("   • Project management system")
            print("   • Timeline editor")
            print("   • Media library")
            print("   • Workflow builder")
            print("   • Audio mixer")
            print("   • Undo/redo system")
            print("   • Progress feedback")
        else:
            print("   • Original lip-sync processing (basic mode)")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ Failed to start enhanced application: {e}")
        print("🔄 Falling back to original application...")
        
        # Fallback to original
        from main_gui import MainWindow
        window = MainWindow()
        window.show()
        return app.exec()


if __name__ == "__main__":
    sys.exit(main())
