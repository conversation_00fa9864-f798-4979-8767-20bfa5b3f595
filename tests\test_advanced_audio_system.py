"""
Tests for the Advanced Audio System.
"""

import pytest
import numpy as np
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
import sys

# Mock external libraries for testing
sys.modules['librosa'] = MagicMock()
sys.modules['soundfile'] = MagicMock()
sys.modules['scipy'] = MagicMock()
sys.modules['scipy.signal'] = MagicMock()
sys.modules['scipy.io'] = MagicMock()
sys.modules['scipy.io.wavfile'] = MagicMock()
sys.modules['PySide6'] = MagicMock()
sys.modules['PySide6.QtWidgets'] = MagicMock()
sys.modules['PySide6.QtCore'] = MagicMock()
sys.modules['PySide6.QtGui'] = MagicMock()

from advanced_audio_system import (
    AudioClip, AudioTrack, AudioProcessor, AudioMixer,
    AudioEffectType, AudioFormat, detect_media_type
)


class TestAudioClip:
    """Test audio clip functionality."""
    
    def test_clip_creation(self):
        """Test creating an audio clip."""
        clip = AudioClip(
            id="clip_1",
            name="Test Clip",
            file_path="/path/to/audio.wav",
            start_time=5.0,
            duration=10.0,
            volume=0.8,
            pan=-0.5,
            fade_in=1.0,
            fade_out=2.0
        )
        
        assert clip.id == "clip_1"
        assert clip.name == "Test Clip"
        assert clip.file_path == "/path/to/audio.wav"
        assert clip.start_time == 5.0
        assert clip.duration == 10.0
        assert clip.volume == 0.8
        assert clip.pan == -0.5
        assert clip.fade_in == 1.0
        assert clip.fade_out == 2.0
        assert clip.muted is False
        assert clip.solo is False
    
    def test_clip_load_audio_mock(self):
        """Test loading audio with mock implementation."""
        clip = AudioClip(
            id="clip_1",
            name="Test Clip",
            file_path="/path/to/audio.wav"
        )
        
        # Should work with mock implementation
        result = clip.load_audio()
        assert result is True
        assert clip.sample_rate == 44100
        assert clip.duration == 10.0
        assert clip.audio_data is not None
        assert clip.audio_data.shape[0] == 2  # Stereo
    
    def test_apply_volume(self):
        """Test volume application."""
        clip = AudioClip(
            id="clip_1",
            name="Test Clip",
            file_path="/path/to/audio.wav",
            volume=0.5
        )
        
        # Create test audio data
        test_data = np.ones((2, 1000))
        
        # Apply volume
        result = clip.apply_volume(test_data)
        
        # Should be half the original volume
        assert np.allclose(result, test_data * 0.5)
    
    def test_apply_pan(self):
        """Test panning application."""
        clip = AudioClip(
            id="clip_1",
            name="Test Clip",
            file_path="/path/to/audio.wav",
            pan=-1.0  # Full left
        )
        
        # Create test stereo audio data
        test_data = np.ones((2, 1000))
        
        # Apply pan
        result = clip.apply_pan(test_data)
        
        # Left channel should be full, right channel should be reduced
        assert result[0, 0] == 1.0  # Left channel unchanged
        assert result[1, 0] < 1.0   # Right channel reduced
    
    def test_apply_fade(self):
        """Test fade in/out application."""
        clip = AudioClip(
            id="clip_1",
            name="Test Clip",
            file_path="/path/to/audio.wav",
            fade_in=1.0,
            fade_out=1.0
        )
        clip.sample_rate = 44100
        
        # Create test audio data (2 seconds)
        samples = 2 * 44100
        test_data = np.ones((2, samples))
        
        # Apply fade
        result = clip.apply_fade(test_data)
        
        # Check fade in (first second should start at 0)
        assert result[0, 0] == 0.0
        assert result[0, 44100] > 0.9  # Should be near full volume after 1 second
        
        # Check fade out (last second should end at 0)
        assert result[0, -1] == 0.0
        assert result[0, -44100] > 0.9  # Should be near full volume before fade


class TestAudioTrack:
    """Test audio track functionality."""
    
    def test_track_creation(self):
        """Test creating an audio track."""
        track = AudioTrack(
            id="track_1",
            name="Main Audio",
            volume=0.8,
            pan=0.2,
            muted=False,
            solo=True
        )
        
        assert track.id == "track_1"
        assert track.name == "Main Audio"
        assert track.volume == 0.8
        assert track.pan == 0.2
        assert track.muted is False
        assert track.solo is True
        assert len(track.clips) == 0
        assert len(track.effects) == 0
    
    def test_add_clip_to_track(self):
        """Test adding clips to a track."""
        track = AudioTrack(id="track_1", name="Test Track")
        
        clip1 = AudioClip(id="clip_1", name="Clip 1", file_path="/path/1.wav")
        clip2 = AudioClip(id="clip_2", name="Clip 2", file_path="/path/2.wav")
        
        # Add clips at different times
        track.add_clip(clip1, 0.0)
        track.add_clip(clip2, 5.0)
        
        assert len(track.clips) == 2
        assert track.clips[0].start_time == 0.0
        assert track.clips[1].start_time == 5.0
        
        # Clips should be sorted by start time
        assert track.clips[0] == clip1
        assert track.clips[1] == clip2
    
    def test_remove_clip_from_track(self):
        """Test removing clips from a track."""
        track = AudioTrack(id="track_1", name="Test Track")
        
        clip1 = AudioClip(id="clip_1", name="Clip 1", file_path="/path/1.wav")
        clip2 = AudioClip(id="clip_2", name="Clip 2", file_path="/path/2.wav")
        
        track.add_clip(clip1, 0.0)
        track.add_clip(clip2, 5.0)
        
        # Remove first clip
        track.remove_clip("clip_1")
        
        assert len(track.clips) == 1
        assert track.clips[0] == clip2
    
    def test_get_clips_at_time(self):
        """Test getting clips at specific time."""
        track = AudioTrack(id="track_1", name="Test Track")
        
        clip1 = AudioClip(id="clip_1", name="Clip 1", file_path="/path/1.wav", duration=3.0)
        clip2 = AudioClip(id="clip_2", name="Clip 2", file_path="/path/2.wav", duration=4.0)
        
        track.add_clip(clip1, 0.0)  # 0-3 seconds
        track.add_clip(clip2, 5.0)  # 5-9 seconds
        
        # Test different time points
        clips_at_1 = track.get_clips_at_time(1.0)
        assert len(clips_at_1) == 1
        assert clips_at_1[0] == clip1
        
        clips_at_4 = track.get_clips_at_time(4.0)
        assert len(clips_at_4) == 0  # Gap between clips
        
        clips_at_7 = track.get_clips_at_time(7.0)
        assert len(clips_at_7) == 1
        assert clips_at_7[0] == clip2


class TestAudioProcessor:
    """Test audio processor functionality."""
    
    def test_processor_creation(self):
        """Test creating an audio processor."""
        processor = AudioProcessor()
        
        assert processor.sample_rate == 44100
        assert processor.buffer_size == 1024
        assert len(processor.effects_library) > 0
        
        # Check that all effect types are available
        for effect_type in AudioEffectType:
            assert effect_type.value in processor.effects_library
    
    def test_apply_effect(self):
        """Test applying effects to audio data."""
        processor = AudioProcessor()
        
        # Create test audio data
        test_data = np.random.random((2, 44100)) * 0.1
        
        # Test noise reduction
        result = processor.apply_effect(
            test_data, 
            AudioEffectType.NOISE_REDUCTION.value,
            {'strength': 0.5}
        )
        
        assert result.shape == test_data.shape
        assert not np.array_equal(result, test_data)  # Should be modified
    
    def test_noise_reduction_effect(self):
        """Test noise reduction effect specifically."""
        processor = AudioProcessor()
        
        # Create noisy audio data
        clean_signal = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 44100))
        noise = np.random.random(44100) * 0.1
        noisy_data = clean_signal + noise
        stereo_data = np.stack([noisy_data, noisy_data])
        
        # Apply noise reduction
        result = processor._noise_reduction(stereo_data, {'strength': 0.8})
        
        assert result.shape == stereo_data.shape
        # With mock implementation, should be reduced by strength factor
        expected = stereo_data * (1 - 0.8 * 0.1)
        assert np.allclose(result, expected)
    
    def test_reverb_effect(self):
        """Test reverb effect."""
        processor = AudioProcessor()
        
        # Create test audio data
        test_data = np.random.random((2, 44100)) * 0.1
        
        # Apply reverb
        result = processor._reverb(test_data, {
            'room_size': 0.7,
            'damping': 0.3,
            'wet_level': 0.4
        })
        
        assert result.shape == test_data.shape
        # Result should be different from input (has reverb added)
        assert not np.array_equal(result, test_data)
    
    def test_voice_modulation_effects(self):
        """Test voice modulation effects."""
        processor = AudioProcessor()
        
        # Create test voice data
        test_data = np.sin(2 * np.pi * 200 * np.linspace(0, 1, 44100))  # 200 Hz tone
        stereo_data = np.stack([test_data, test_data])
        
        # Test robot voice
        robot_result = processor._voice_modulation(stereo_data, {
            'type': 'robot',
            'intensity': 0.5
        })
        assert robot_result.shape == stereo_data.shape
        
        # Test chipmunk voice (should use pitch shift)
        chipmunk_result = processor._voice_modulation(stereo_data, {
            'type': 'chipmunk',
            'intensity': 0.8
        })
        assert chipmunk_result.shape == stereo_data.shape
        
        # Test demon voice
        demon_result = processor._voice_modulation(stereo_data, {
            'type': 'demon',
            'intensity': 0.6
        })
        assert demon_result.shape == stereo_data.shape


class TestAudioMixer:
    """Test audio mixer functionality."""
    
    def test_mixer_creation(self):
        """Test creating an audio mixer."""
        mixer = AudioMixer(sample_rate=48000)
        
        assert mixer.sample_rate == 48000
        assert mixer.master_volume == 1.0
        assert len(mixer.tracks) == 0
        assert isinstance(mixer.processor, AudioProcessor)
    
    def test_add_remove_tracks(self):
        """Test adding and removing tracks."""
        mixer = AudioMixer()
        
        track1 = AudioTrack(id="track_1", name="Track 1")
        track2 = AudioTrack(id="track_2", name="Track 2")
        
        # Add tracks
        mixer.add_track(track1)
        mixer.add_track(track2)
        
        assert len(mixer.tracks) == 2
        assert mixer.get_track("track_1") == track1
        assert mixer.get_track("track_2") == track2
        
        # Remove track
        mixer.remove_track("track_1")
        
        assert len(mixer.tracks) == 1
        assert mixer.get_track("track_1") is None
        assert mixer.get_track("track_2") == track2
    
    def test_mix_empty_tracks(self):
        """Test mixing with no tracks."""
        mixer = AudioMixer()
        
        # Mix 1 second of audio
        result = mixer.mix_at_time(0.0, 1.0)
        
        assert result.shape == (2, 44100)  # Stereo, 1 second at 44.1kHz
        assert np.allclose(result, 0.0)  # Should be silence
    
    def test_mix_with_muted_tracks(self):
        """Test mixing with muted tracks."""
        mixer = AudioMixer()
        
        track = AudioTrack(id="track_1", name="Track 1", muted=True)
        clip = AudioClip(id="clip_1", name="Clip 1", file_path="/path/audio.wav")
        clip.audio_data = np.ones((2, 44100))  # 1 second of audio
        clip.sample_rate = 44100
        clip.duration = 1.0
        
        track.add_clip(clip, 0.0)
        mixer.add_track(track)
        
        # Mix should be silent because track is muted
        result = mixer.mix_at_time(0.0, 1.0)
        assert np.allclose(result, 0.0)
    
    def test_mix_with_solo_tracks(self):
        """Test mixing with solo tracks."""
        mixer = AudioMixer()
        
        # Create two tracks, one solo
        track1 = AudioTrack(id="track_1", name="Track 1", solo=False)
        track2 = AudioTrack(id="track_2", name="Track 2", solo=True)
        
        # Add clips to both tracks
        clip1 = AudioClip(id="clip_1", name="Clip 1", file_path="/path/1.wav")
        clip1.audio_data = np.ones((2, 44100))
        clip1.sample_rate = 44100
        clip1.duration = 1.0
        
        clip2 = AudioClip(id="clip_2", name="Clip 2", file_path="/path/2.wav")
        clip2.audio_data = np.ones((2, 44100)) * 2
        clip2.sample_rate = 44100
        clip2.duration = 1.0
        
        track1.add_clip(clip1, 0.0)
        track2.add_clip(clip2, 0.0)
        
        mixer.add_track(track1)
        mixer.add_track(track2)
        
        # Only solo track should be heard
        result = mixer.mix_at_time(0.0, 1.0)
        
        # Should contain only track2's audio (value 2)
        assert np.allclose(result, 2.0)
    
    def test_master_volume(self):
        """Test master volume application."""
        mixer = AudioMixer()
        mixer.master_volume = 0.5
        
        track = AudioTrack(id="track_1", name="Track 1")
        clip = AudioClip(id="clip_1", name="Clip 1", file_path="/path/audio.wav")
        clip.audio_data = np.ones((2, 44100))
        clip.sample_rate = 44100
        clip.duration = 1.0
        
        track.add_clip(clip, 0.0)
        mixer.add_track(track)
        
        result = mixer.mix_at_time(0.0, 1.0)
        
        # Should be half volume due to master volume
        assert np.allclose(result, 0.5)
    
    @patch('advanced_audio_system.sf')
    def test_export_audio(self, mock_sf):
        """Test audio export functionality."""
        mixer = AudioMixer()
        
        # Mock soundfile write
        mock_sf.write = Mock()
        
        track = AudioTrack(id="track_1", name="Track 1")
        clip = AudioClip(id="clip_1", name="Clip 1", file_path="/path/audio.wav")
        clip.audio_data = np.ones((2, 44100))
        clip.sample_rate = 44100
        clip.duration = 1.0
        
        track.add_clip(clip, 0.0)
        mixer.add_track(track)
        
        # Export audio
        result = mixer.export_audio("/output/test.wav", 0.0, 1.0)
        
        # Should succeed (mock implementation)
        assert result is True


class TestAudioEffectTypes:
    """Test audio effect type enumeration."""
    
    def test_effect_types_exist(self):
        """Test that all expected effect types exist."""
        expected_effects = [
            'noise_reduction',
            'echo_removal', 
            'reverb',
            'chorus',
            'distortion',
            'compressor',
            'equalizer',
            'pitch_shift',
            'time_stretch',
            'voice_modulation'
        ]
        
        for effect in expected_effects:
            assert hasattr(AudioEffectType, effect.upper())
            assert AudioEffectType[effect.upper()].value == effect


class TestAudioFormats:
    """Test audio format enumeration."""
    
    def test_audio_formats_exist(self):
        """Test that all expected audio formats exist."""
        expected_formats = ['wav', 'mp3', 'flac', 'aac', 'ogg']
        
        for format_name in expected_formats:
            assert hasattr(AudioFormat, format_name.upper())
            assert AudioFormat[format_name.upper()].value == format_name


class TestAudioIntegration:
    """Test integration between audio components."""
    
    def test_full_audio_pipeline(self):
        """Test complete audio processing pipeline."""
        # Create mixer
        mixer = AudioMixer()
        
        # Create track with effects
        track = AudioTrack(id="main", name="Main Track")
        track.effects = [
            {
                'type': AudioEffectType.NOISE_REDUCTION.value,
                'parameters': {'strength': 0.3}
            },
            {
                'type': AudioEffectType.REVERB.value,
                'parameters': {'room_size': 0.5, 'wet_level': 0.2}
            }
        ]
        
        # Create audio clip
        clip = AudioClip(id="speech", name="Speech", file_path="/path/speech.wav")
        clip.audio_data = np.random.random((2, 44100)) * 0.5
        clip.sample_rate = 44100
        clip.duration = 1.0
        clip.volume = 0.8
        clip.pan = -0.2
        
        # Add to track and mixer
        track.add_clip(clip, 0.0)
        mixer.add_track(track)
        
        # Process audio
        result = mixer.mix_at_time(0.0, 1.0)
        
        # Should produce valid audio output
        assert result.shape == (2, 44100)
        assert not np.allclose(result, 0.0)  # Should not be silence
        assert np.max(np.abs(result)) <= 1.0  # Should not clip
