# Auto Latent Video Processor - Complete Requirements
# Generated for integration and testing phase

# Core GUI Framework
PySide6>=6.6.0
shiboken6>=6.6.0

# Core Python Libraries
numpy>=1.24.0,<2.0.0  # Ensure NumPy 1.x for compatibility
pillow>=10.0.0
requests>=2.31.0
python-dotenv>=1.0.0
pathlib2>=2.3.7
typing-extensions>=4.7.0

# Audio Processing
librosa>=0.10.0
soundfile>=0.12.0
scipy>=1.11.0
pyaudio>=0.2.11
pydub>=0.25.1
resampy>=0.4.2

# Video Processing
opencv-python>=4.8.0
imageio>=2.31.0
imageio-ffmpeg>=0.4.8
moviepy>=1.0.3
ffmpeg-python>=0.2.0

# AI/ML Libraries
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
transformers>=4.30.0
diffusers>=0.20.0
accelerate>=0.21.0
safetensors>=0.3.0

# TTS Libraries
TTS>=0.15.0
espeak-ng>=1.51
pyttsx3>=2.90

# Data Processing
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Database
sqlalchemy>=2.0.0
alembic>=1.11.0

# Configuration Management
pydantic>=2.0.0
pydantic-settings>=2.0.0
configparser>=5.3.0
toml>=0.10.2
PyYAML>=6.0

# File Handling
watchdog>=3.0.0
send2trash>=1.8.2
psutil>=5.9.0
humanize>=4.7.0

# Networking
aiohttp>=3.8.0
websockets>=11.0.0
httpx>=0.24.0

# Utilities
tqdm>=4.65.0
rich>=13.4.0
click>=8.1.0
colorama>=0.4.6
python-dateutil>=2.8.2
pytz>=2023.3

# Development Tools
pytest>=7.4.0
pytest-qt>=4.2.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-asyncio>=0.21.0

# Code Quality
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
isort>=5.12.0
pre-commit>=3.3.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# Packaging
setuptools>=68.0.0
wheel>=0.41.0
build>=0.10.0
twine>=4.0.0

# Performance Monitoring
memory-profiler>=0.61.0
line-profiler>=4.1.0
py-spy>=0.3.14
