"""
Tests for the Undo/Redo System.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
import sys

# Mock PySide6 for testing
sys.modules['PySide6'] = MagicMock()
sys.modules['PySide6.QtCore'] = MagicMock()
sys.modules['PySide6.QtWidgets'] = MagicMock()

from undo_redo_system import (
    UndoRedoManager, PropertyChangeAction, CompositeAction, ActionGroup,
    UndoRedoIntegration, ActionType, TimelineClipMoveAction,
    WorkflowNodeAddAction, MediaMetadataChangeAction,
    execute_action, undo, redo, start_group, end_group,
    can_undo, can_redo, get_global_undo_manager
)


class TestObject:
    """Test object for property change testing."""
    
    def __init__(self):
        self.name = "Test Object"
        self.value = 42
        self.enabled = True
        self.position = (0, 0)


class TestPropertyChangeAction:
    """Test property change actions."""
    
    def test_property_change_creation(self):
        """Test creating a property change action."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100, "Change value")
        
        assert action.target_object is obj
        assert action.property_name == "value"
        assert action.old_value == 42
        assert action.new_value == 100
        assert action.description == "Change value"
        assert action.can_merge is True
    
    def test_property_change_execute(self):
        """Test executing a property change."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Execute the action
        result = action.execute()
        
        assert result is True
        assert obj.value == 100
        assert action.executed is True
    
    def test_property_change_undo(self):
        """Test undoing a property change."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Execute then undo
        action.execute()
        assert obj.value == 100
        
        result = action.undo()
        assert result is True
        assert obj.value == 42
    
    def test_property_change_redo(self):
        """Test redoing a property change."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Execute, undo, then redo
        action.execute()
        action.undo()
        assert obj.value == 42
        
        result = action.redo()
        assert result is True
        assert obj.value == 100
    
    def test_property_change_merge(self):
        """Test merging property change actions."""
        obj = TestObject()
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "value", 50, 100)
        
        # Check if they can merge
        assert action1.can_merge_with(action2)
        
        # Merge them
        merged = action1.merge_with(action2)
        
        assert merged.old_value == 42  # From first action
        assert merged.new_value == 100  # From second action
        assert merged.target_object is obj
        assert merged.property_name == "value"
    
    def test_property_change_no_merge_different_objects(self):
        """Test that actions on different objects don't merge."""
        obj1 = TestObject()
        obj2 = TestObject()
        
        action1 = PropertyChangeAction(obj1, "value", 42, 50)
        action2 = PropertyChangeAction(obj2, "value", 42, 50)
        
        assert not action1.can_merge_with(action2)
    
    def test_property_change_no_merge_different_properties(self):
        """Test that actions on different properties don't merge."""
        obj = TestObject()
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "old", "new")
        
        assert not action1.can_merge_with(action2)


class TestCompositeAction:
    """Test composite actions."""
    
    def test_composite_creation(self):
        """Test creating a composite action."""
        action = CompositeAction("comp_1", "Composite Action")
        
        assert action.action_id == "comp_1"
        assert action.description == "Composite Action"
        assert len(action.actions) == 0
    
    def test_composite_add_actions(self):
        """Test adding sub-actions to composite."""
        obj = TestObject()
        composite = CompositeAction("comp_1", "Multiple Changes")
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "old", "new")
        
        composite.add_action(action1)
        composite.add_action(action2)
        
        assert len(composite.actions) == 2
        assert composite.actions[0] is action1
        assert composite.actions[1] is action2
    
    def test_composite_execute(self):
        """Test executing a composite action."""
        obj = TestObject()
        composite = CompositeAction("comp_1", "Multiple Changes")
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "Test Object", "New Name")
        
        composite.add_action(action1)
        composite.add_action(action2)
        
        # Execute composite
        result = composite.execute()
        
        assert result is True
        assert obj.value == 50
        assert obj.name == "New Name"
        assert composite.executed is True
    
    def test_composite_undo(self):
        """Test undoing a composite action."""
        obj = TestObject()
        composite = CompositeAction("comp_1", "Multiple Changes")
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "Test Object", "New Name")
        
        composite.add_action(action1)
        composite.add_action(action2)
        
        # Execute then undo
        composite.execute()
        assert obj.value == 50
        assert obj.name == "New Name"
        
        result = composite.undo()
        assert result is True
        assert obj.value == 42
        assert obj.name == "Test Object"


class TestActionGroup:
    """Test action groups."""
    
    def test_group_creation(self):
        """Test creating an action group."""
        group = ActionGroup("group_1", "Test Group", "A test group")
        
        assert group.group_id == "group_1"
        assert group.name == "Test Group"
        assert group.description == "A test group"
        assert len(group.actions) == 0
        assert group.closed is False
    
    def test_group_add_actions(self):
        """Test adding actions to a group."""
        group = ActionGroup("group_1", "Test Group")
        obj = TestObject()
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "old", "new")
        
        group.add_action(action1)
        group.add_action(action2)
        
        assert len(group.actions) == 2
        assert group.actions[0] is action1
        assert group.actions[1] is action2
    
    def test_group_close(self):
        """Test closing a group."""
        group = ActionGroup("group_1", "Test Group")
        
        group.close()
        assert group.closed is True
        
        # Should not be able to add actions after closing
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 50)
        
        with pytest.raises(ValueError):
            group.add_action(action)
    
    def test_group_to_composite(self):
        """Test converting group to composite action."""
        group = ActionGroup("group_1", "Test Group")
        obj = TestObject()
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "old", "new")
        
        group.add_action(action1)
        group.add_action(action2)
        
        composite = group.to_composite_action()
        
        assert composite.action_id == "group_1"
        assert composite.description == "Test Group"
        assert len(composite.actions) == 2
        assert composite.actions[0] is action1
        assert composite.actions[1] is action2


class TestUndoRedoManager:
    """Test the main undo/redo manager."""
    
    def setup_method(self):
        """Setup test manager."""
        self.manager = UndoRedoManager(max_history_size=10)
    
    def test_manager_creation(self):
        """Test creating an undo/redo manager."""
        assert self.manager.max_history_size == 10
        assert len(self.manager.undo_stack) == 0
        assert len(self.manager.redo_stack) == 0
        assert self.manager.enabled is True
        assert self.manager.recording is True
        assert self.manager.current_group is None
    
    def test_execute_action(self):
        """Test executing an action through the manager."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        result = self.manager.execute_action(action)
        
        assert result is True
        assert obj.value == 100
        assert len(self.manager.undo_stack) == 1
        assert len(self.manager.redo_stack) == 0
        assert self.manager.stats['total_actions'] == 1
    
    def test_undo_action(self):
        """Test undoing an action."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Execute then undo
        self.manager.execute_action(action)
        assert obj.value == 100
        
        result = self.manager.undo()
        assert result is True
        assert obj.value == 42
        assert len(self.manager.undo_stack) == 0
        assert len(self.manager.redo_stack) == 1
        assert self.manager.stats['total_undos'] == 1
    
    def test_redo_action(self):
        """Test redoing an action."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Execute, undo, then redo
        self.manager.execute_action(action)
        self.manager.undo()
        assert obj.value == 42
        
        result = self.manager.redo()
        assert result is True
        assert obj.value == 100
        assert len(self.manager.undo_stack) == 1
        assert len(self.manager.redo_stack) == 0
        assert self.manager.stats['total_redos'] == 1
    
    def test_can_undo_redo(self):
        """Test checking if undo/redo is possible."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Initially no actions
        assert not self.manager.can_undo()
        assert not self.manager.can_redo()
        
        # After executing action
        self.manager.execute_action(action)
        assert self.manager.can_undo()
        assert not self.manager.can_redo()
        
        # After undoing
        self.manager.undo()
        assert not self.manager.can_undo()
        assert self.manager.can_redo()
    
    def test_action_merging(self):
        """Test automatic action merging."""
        obj = TestObject()
        
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "value", 50, 100)
        
        # Execute both actions
        self.manager.execute_action(action1)
        self.manager.execute_action(action2)
        
        # Should be merged into single action
        assert len(self.manager.undo_stack) == 1
        
        # Undo should restore original value
        self.manager.undo()
        assert obj.value == 42
    
    def test_action_groups(self):
        """Test action grouping."""
        obj = TestObject()
        
        # Start a group
        group_id = self.manager.start_group("Test Group")
        assert self.manager.current_group is not None
        assert self.manager.current_group.group_id == group_id
        
        # Execute actions in group
        action1 = PropertyChangeAction(obj, "value", 42, 50)
        action2 = PropertyChangeAction(obj, "name", "old", "new")
        
        self.manager.execute_action(action1)
        self.manager.execute_action(action2)
        
        # Actions should be in group, not main stack yet
        assert len(self.manager.undo_stack) == 0
        assert len(self.manager.current_group.actions) == 2
        
        # End group
        self.manager.end_group()
        assert self.manager.current_group is None
        assert len(self.manager.undo_stack) == 1
        
        # Undo should undo entire group
        self.manager.undo()
        assert obj.value == 42
        assert obj.name == "old"
    
    def test_clear_history(self):
        """Test clearing undo/redo history."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        self.manager.execute_action(action)
        self.manager.undo()
        
        assert len(self.manager.undo_stack) == 0
        assert len(self.manager.redo_stack) == 1
        
        self.manager.clear_history()
        
        assert len(self.manager.undo_stack) == 0
        assert len(self.manager.redo_stack) == 0
    
    def test_disabled_manager(self):
        """Test manager when disabled."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        self.manager.set_enabled(False)
        
        # Action should execute but not be recorded
        result = self.manager.execute_action(action)
        assert result is True
        assert obj.value == 100
        assert len(self.manager.undo_stack) == 0
        
        # Undo/redo should not work
        assert not self.manager.can_undo()
        assert not self.manager.undo()


class TestUndoRedoIntegration:
    """Test undo/redo integration helper."""
    
    def setup_method(self):
        """Setup test integration."""
        self.manager = UndoRedoManager()
        self.integration = UndoRedoIntegration(self.manager)
    
    def test_track_object(self):
        """Test tracking objects."""
        obj = TestObject()
        
        object_id = self.integration.track_object(obj, "test_obj")
        
        assert object_id == "test_obj"
        assert "test_obj" in self.integration.tracked_objects
        assert self.integration.tracked_objects["test_obj"] is obj
    
    def test_watch_property(self):
        """Test watching object properties."""
        obj = TestObject()
        object_id = self.integration.track_object(obj)
        
        self.integration.watch_property(object_id, "value", "Change value")
        
        assert object_id in self.integration.property_watchers
        assert "value" in self.integration.property_watchers[object_id]
        
        watcher = self.integration.property_watchers[object_id]["value"]
        assert watcher["current_value"] == 42
        assert watcher["description"] == "Change value"
    
    def test_commit_property_change(self):
        """Test committing property changes."""
        obj = TestObject()
        object_id = self.integration.track_object(obj)
        self.integration.watch_property(object_id, "value")
        
        # Change the property
        obj.value = 100
        
        # Commit the change
        result = self.integration.commit_property_change(object_id, "value")
        
        assert result is True
        assert len(self.manager.undo_stack) == 1
        
        # Undo should restore original value
        self.manager.undo()
        assert obj.value == 42


class TestSpecificActions:
    """Test specific action implementations."""
    
    def test_timeline_clip_move_action(self):
        """Test timeline clip move action."""
        action = TimelineClipMoveAction("clip_1", 5.0, 10.0)
        
        assert action.clip_id == "clip_1"
        assert action.old_position == 5.0
        assert action.new_position == 10.0
        assert action.can_merge is True
        
        # Test execution
        result = action.execute()
        assert result is True
        assert action.executed is True
    
    def test_timeline_clip_move_merge(self):
        """Test merging timeline clip moves."""
        action1 = TimelineClipMoveAction("clip_1", 5.0, 7.0)
        action2 = TimelineClipMoveAction("clip_1", 7.0, 10.0)
        
        assert action1.can_merge_with(action2)
        
        merged = action1.merge_with(action2)
        assert merged.old_position == 5.0  # Original start
        assert merged.new_position == 10.0  # Final end
    
    def test_workflow_node_add_action(self):
        """Test workflow node add action."""
        node_data = {"name": "Test Node", "type": "processing"}
        action = WorkflowNodeAddAction(node_data, (100, 200))
        
        assert action.node_data == node_data
        assert action.position == (100, 200)
        
        # Test execution
        result = action.execute()
        assert result is True
        assert action.node_id is not None
    
    def test_media_metadata_change_action(self):
        """Test media metadata change action."""
        action = MediaMetadataChangeAction("/path/file.mp3", "title", "Old Title", "New Title")
        
        assert action.media_path == "/path/file.mp3"
        assert action.property_name == "title"
        assert action.old_value == "Old Title"
        assert action.new_value == "New Title"
        assert action.can_merge is True
        
        # Test execution
        result = action.execute()
        assert result is True


class TestGlobalFunctions:
    """Test global convenience functions."""
    
    def test_global_manager(self):
        """Test global manager access."""
        manager1 = get_global_undo_manager()
        manager2 = get_global_undo_manager()
        
        # Should return same instance
        assert manager1 is manager2
    
    def test_global_functions(self):
        """Test global convenience functions."""
        obj = TestObject()
        action = PropertyChangeAction(obj, "value", 42, 100)
        
        # Test execute
        result = execute_action(action)
        assert result is True
        assert obj.value == 100
        
        # Test can_undo/can_redo
        assert can_undo()
        assert not can_redo()
        
        # Test undo
        result = undo()
        assert result is True
        assert obj.value == 42
        
        # Test redo
        result = redo()
        assert result is True
        assert obj.value == 100
    
    def test_global_groups(self):
        """Test global group functions."""
        obj = TestObject()
        
        # Start group
        group_id = start_group("Test Group")
        assert group_id is not None
        
        # Execute action in group
        action = PropertyChangeAction(obj, "value", 42, 100)
        execute_action(action)
        
        # End group
        result = end_group()
        assert result is True
        
        # Should be able to undo entire group
        undo()
        assert obj.value == 42
