import os
import json
import tempfile
import pytest
import copy
from unittest.mock import Mock, patch, MagicMock

from backend_processor import (
    load_workflow, 
    update_workflow, 
    update_framepack_workflow,
    submit_workflow,
    poll_comfyui_status,
    get_comfyui_status
)


class TestWorkflowLoading:
    """Test workflow loading functionality."""
    
    def test_load_workflow_success(self, tmp_path):
        """Test successful workflow loading."""
        workflow_data = {
            "1": {"class_type": "LoadVideo", "inputs": {"video": ""}},
            "2": {"class_type": "AudioLoader", "inputs": {"audio": ""}}
        }
        workflow_file = tmp_path / "test_workflow.json"
        workflow_file.write_text(json.dumps(workflow_data))
        
        result = load_workflow(str(workflow_file))
        assert result == workflow_data
    
    def test_load_workflow_file_not_found(self):
        """Test workflow loading with non-existent file."""
        result = load_workflow("nonexistent_file.json")
        assert result is None
    
    def test_load_workflow_invalid_json(self, tmp_path):
        """Test workflow loading with invalid JSON."""
        workflow_file = tmp_path / "invalid.json"
        workflow_file.write_text("{ invalid json }")
        
        result = load_workflow(str(workflow_file))
        assert result is None
    
    def test_load_workflow_permission_error(self, tmp_path):
        """Test workflow loading with permission issues."""
        workflow_file = tmp_path / "restricted.json"
        workflow_file.write_text('{"test": "data"}')
        
        # Mock os.open to raise PermissionError
        with patch('builtins.open', side_effect=PermissionError("Access denied")):
            result = load_workflow(str(workflow_file))
            assert result is None


class TestWorkflowUpdating:
    """Test workflow updating functionality."""
    
    @pytest.fixture
    def base_workflow(self):
        """Base workflow for testing."""
        return {
            "1": {"class_type": "VHS_LoadVideo", "inputs": {"video": ""}},
            "4": {"class_type": "AudioLoader", "inputs": {"audio": ""}},
            "3": {"class_type": "LatentSync", "inputs": {
                "seed": 0, "lips_expression": 1.0, "inference_steps": 20
            }},
            "5": {"class_type": "VideoCombine", "inputs": {"crf": 19}},
            "2": {"class_type": "VideoLengthAdjuster", "inputs": {"silent_padding_sec": 0.3}}
        }
    
    def test_update_workflow_success(self, base_workflow, tmp_path):
        """Test successful workflow updating."""
        video_file = tmp_path / "test.mp4"
        audio_file = tmp_path / "test.wav"
        video_file.touch()
        audio_file.touch()
        
        config = {
            'seed': 12345,
            'lips_expression': 2.5,
            'inference_steps': 30,
            'crf': 23,
            'silent_padding_sec': 0.5
        }
        
        result = update_workflow(base_workflow, str(video_file), str(audio_file), config)
        
        assert result is not None
        assert result["1"]["inputs"]["video"] == str(video_file.absolute())
        assert result["4"]["inputs"]["audio"] == str(audio_file.absolute())
        assert result["3"]["inputs"]["seed"] == 12345
        assert result["3"]["inputs"]["lips_expression"] == 2.5
        assert result["3"]["inputs"]["inference_steps"] == 30
        assert result["5"]["inputs"]["crf"] == 23
        assert result["2"]["inputs"]["silent_padding_sec"] == 0.5
    
    def test_update_workflow_missing_nodes(self):
        """Test workflow updating with missing nodes."""
        incomplete_workflow = {"1": {"inputs": {}}}
        config = {'seed': 123}
        
        result = update_workflow(incomplete_workflow, "video.mp4", "audio.wav", config)
        assert result is not None  # Should still work, just log warnings
    
    def test_update_workflow_deep_copy(self, base_workflow):
        """Test that workflow updating doesn't modify original."""
        original_seed = base_workflow["3"]["inputs"]["seed"]
        config = {'seed': 99999}
        
        result = update_workflow(base_workflow, "video.mp4", "audio.wav", config)
        
        # Original should be unchanged
        assert base_workflow["3"]["inputs"]["seed"] == original_seed
        # Result should be updated
        assert result["3"]["inputs"]["seed"] == 99999


class TestFramePackWorkflow:
    """Test FramePack workflow functionality."""
    
    @pytest.fixture
    def framepack_workflow(self):
        """Base FramePack workflow for testing."""
        return {
            "72": {"class_type": "LoadImage", "inputs": {"image": ""}},
            "62": {"class_type": "StringConstantMultiline", "inputs": {"string": "default prompt"}},
            "67": {"class_type": "CR Seed", "inputs": {"seed": 0}}
        }
    
    def test_update_framepack_workflow_success(self, framepack_workflow, tmp_path):
        """Test successful FramePack workflow updating."""
        image_file = tmp_path / "test.jpg"
        image_file.touch()
        
        config = {
            'framepack_prompt': 'custom prompt text',
            'framepack_seed': 12345,
            'framepack_random': False
        }
        
        result = update_framepack_workflow(framepack_workflow, str(image_file), config)
        
        assert result is not None
        assert result["72"]["inputs"]["image"] == str(image_file.absolute())
        assert result["62"]["inputs"]["string"] == 'custom prompt text'
        assert result["67"]["inputs"]["seed"] == 12345
    
    def test_update_framepack_workflow_random_seed(self, framepack_workflow, tmp_path):
        """Test FramePack workflow with random seed."""
        image_file = tmp_path / "test.jpg"
        image_file.touch()
        
        config = {'framepack_random': True}
        
        result = update_framepack_workflow(framepack_workflow, str(image_file), config)
        
        assert result is not None
        # Should have a random seed (not 0)
        assert result["67"]["inputs"]["seed"] != 0
        assert isinstance(result["67"]["inputs"]["seed"], int)


class TestComfyUIInteraction:
    """Test ComfyUI API interaction."""
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_success(self, mock_post):
        """Test successful workflow submission."""
        mock_response = Mock()
        mock_response.json.return_value = {'prompt_id': 'test-prompt-id-123'}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result == 'test-prompt-id-123'
        mock_post.assert_called_once()
        
        # Check the call arguments
        call_args = mock_post.call_args
        assert call_args[1]['headers']['Content-Type'] == 'application/json'
        
        # Check payload structure
        payload = json.loads(call_args[1]['data'])
        assert 'prompt' in payload
        assert payload['prompt'] == workflow
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_http_error(self, mock_post):
        """Test workflow submission with HTTP error."""
        mock_post.side_effect = Exception("Connection error")
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result is None
    
    @patch('backend_processor.requests.post')
    def test_submit_workflow_no_prompt_id(self, mock_post):
        """Test workflow submission with missing prompt_id in response."""
        mock_response = Mock()
        mock_response.json.return_value = {'error': 'Something went wrong'}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        workflow = {"1": {"class_type": "TestNode"}}
        result = submit_workflow("127.0.0.1:8188", workflow)
        
        assert result is None
    
    @patch('backend_processor.requests.get')
    def test_get_comfyui_status_success(self, mock_get):
        """Test successful ComfyUI status retrieval."""
        mock_response = Mock()
        mock_response.json.return_value = {'status': 'running'}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = get_comfyui_status("http://127.0.0.1:8188/history")
        
        assert result == {'status': 'running'}
    
    @patch('backend_processor.requests.get')
    def test_get_comfyui_status_error(self, mock_get):
        """Test ComfyUI status retrieval with error."""
        mock_get.side_effect = Exception("Network error")
        
        result = get_comfyui_status("http://127.0.0.1:8188/history")
        
        assert result is None


class TestPollingStatus:
    """Test ComfyUI status polling functionality."""
    
    @patch('backend_processor.get_comfyui_status')
    @patch('backend_processor.time.sleep')
    def test_poll_comfyui_status_success(self, mock_sleep, mock_get_status):
        """Test successful status polling."""
        # Mock successful completion
        mock_get_status.side_effect = [
            {},  # First call: empty (still processing)
            {    # Second call: completed
                'test-prompt-id': {
                    'status': {'completed': True},
                    'outputs': {'5': {'videos': [{'filename': 'output.mp4'}]}}
                }
            }
        ]
        
        success, files = poll_comfyui_status("127.0.0.1:8188", "test-prompt-id")
        
        assert success is True
        assert files == ['output.mp4']
        assert mock_get_status.call_count == 2
    
    @patch('backend_processor.get_comfyui_status')
    @patch('backend_processor.time.sleep')
    def test_poll_comfyui_status_timeout(self, mock_sleep, mock_get_status):
        """Test status polling timeout."""
        # Mock always empty response (never completes)
        mock_get_status.return_value = {}
        
        # Mock time.time to simulate timeout
        with patch('backend_processor.time.time') as mock_time:
            mock_time.side_effect = [0, 301]  # Start at 0, then 301 seconds later
            
            success, files = poll_comfyui_status("127.0.0.1:8188", "test-prompt-id")
            
            assert success is False
            assert files == []
